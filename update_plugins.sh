#!/bin/bash

# 快速更新插件以实现新接口的脚本

echo "正在更新插件以移除向后兼容代码..."

# 更新 system-collector
echo "更新 system-collector..."
sed -i '' 's/\*plugininterface\.PluginInfo/\*plugininterface.EnhancedPluginInfo/g' plugins/examples/system-collector/main.go
sed -i '' 's/plugininterface\.PluginInfo{/plugininterface.EnhancedPluginInfo{/g' plugins/examples/system-collector/main.go

# 更新 email-alerter
echo "更新 email-alerter..."
sed -i '' 's/\*plugininterface\.PluginInfo/\*plugininterface.EnhancedPluginInfo/g' plugins/examples/email-alerter/main.go
sed -i '' 's/plugininterface\.PluginInfo{/plugininterface.EnhancedPluginInfo{/g' plugins/examples/email-alerter/main.go

echo "插件更新完成"
