load conf file: config/agent_config.yaml
2025-05-26 18:56:21.471048000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent 启动中...
2025-05-26 18:56:21.471184000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:42	Agent ID:	{"agent_id": "7623a7b2-bd99-4537-8606-c6251fdecbd9"}
2025-05-26 18:56:21.471199000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:51	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-26 18:56:21.471208000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-26 18:56:21.473523000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Connecting", "new_state": "Connected"}
2025-05-26 18:56:21.473537000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:136	连接已建立	{"server": "localhost:50051"}
2025-05-26 18:56:21.474672000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:87	Agent 注册成功
2025-05-26 18:56:21.474693000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:509	开始初始化插件管理器
2025-05-26 18:56:21.474702000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:513	预加载插件	{"device_type": "system"}
2025-05-26 18:56:21.474710000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "system"}
2025-05-26 18:56:21.474720000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:41	请求插件	{"plugin_name": "system-collector", "version": "", "device_type": "system"}
2025-05-26 18:56:21.518031000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:133	从插件路径加载插件	{"device_type": "system", "plugin_name": "system-collector", "plugin_version": "1.0.0", "plugin_path": "plugins/build/system-collector-1.0.0.so", "absolute_path": "plugins/build/system-collector-1.0.0.so"}
2025-05-26 18:56:21.527824000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:259	插件校验和	{"path": "plugins/build/system-collector-1.0.0.so", "checksum": "f0ac593c08a16ea451635452f8efb309710bf7239c40654b9812caab44753f6d"}
2025-05-26 18:56:21.534666000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:238	插件加载成功	{"plugin_id": "system", "plugin_path": "plugins/build/system-collector-1.0.0.so"}
2025-05-26 18:56:21.534687000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:523	插件预加载成功	{"device_type": "system"}
2025-05-26 18:56:21.534694000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:513	预加载插件	{"device_type": "mysql"}
2025-05-26 18:56:21.534703000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "mysql"}
2025-05-26 18:56:21.534712000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:41	请求插件	{"plugin_name": "mysql-collector", "version": "", "device_type": "mysql"}
2025-05-26 18:56:21.577302000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:133	从插件路径加载插件	{"device_type": "mysql", "plugin_name": "mysql-collector", "plugin_version": "1.0.0", "plugin_path": "plugins/build/mysql-collector-1.0.0.so", "absolute_path": "plugins/build/mysql-collector-1.0.0.so"}
2025-05-26 18:56:21.584458000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:259	插件校验和	{"path": "plugins/build/mysql-collector-1.0.0.so", "checksum": "20e8b76acad66a8fa1bc98a7ca6c178e90eea267397d6165952bff6fa6489048"}
2025-05-26 18:56:21.592091000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:238	插件加载成功	{"plugin_id": "mysql", "plugin_path": "plugins/build/mysql-collector-1.0.0.so"}
2025-05-26 18:56:21.592117000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:523	插件预加载成功	{"device_type": "mysql"}
2025-05-26 18:56:21.592131000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:526	插件管理器初始化完成
2025-05-26 18:56:21.592186000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Connecting", "timestamp": "2025-05-26 18:56:21.471208000"}
2025-05-26 18:56:21.592208000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-26 18:56:21.473523000"}
2025-05-26 18:56:21.592218000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:581	日志转发器正在启动...
2025-05-26 18:56:21.592243000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:248	任务流已连接
2025-05-26 18:56:21.592255000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:486	启动插件更新流监听
2025-05-26 18:56:21.592270000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:133	启动插件更新流监听
2025-05-26 18:56:21.592277000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:174	Task manager started	{"worker_count": 5}
2025-05-26 18:56:21.592243000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:370	指标流已连接
2025-05-26 18:56:21.592497000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 4}
2025-05-26 18:56:21.592512000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 3}
2025-05-26 18:56:21.592514000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 2}
2025-05-26 18:56:21.592534000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_processors.go:16	Metric data processor started
2025-05-26 18:56:21.592542000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 1}
2025-05-26 18:56:21.592243000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:432	日志流已连接
2025-05-26 18:56:21.592579000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 0}
2025-05-26 18:56:21.592584000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "/var/log/system.log", "source": "system"}
2025-05-26 18:56:21.592523000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_processors.go:174	Analysis result processor started
2025-05-26 18:56:21.592682000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent.log"}
2025-05-26 18:56:21.592702000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent_test.log"}
2025-05-26 18:56:21.592769000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/control_plane_test.log", "source": "agent"}
2025-05-26 18:56:21.592837000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/plane.log", "source": "agent"}
2025-05-26 18:56:21.592849000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:594	日志转发器启动完成	{"active_watchers": 3}
2025-05-26 18:56:31.593396000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 8, "dropped": 0}
2025-05-26 18:56:39.243856000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:204	接收到退出信号，正在关闭 Agent...
2025-05-26 18:56:39.243967000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:317	Task worker stopped due to context cancellation	{"worker_id": 3}
2025-05-26 18:56:39.244034000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:304	停止接收任务配置，流已关闭或上下文已取消:	{"error": "rpc error: code = Canceled desc = context canceled"}
2025-05-26 18:56:39.244082000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:317	Task worker stopped due to context cancellation	{"worker_id": 0}
2025-05-26 18:56:39.244008000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:356	任务流正在关闭...
2025-05-26 18:56:39.243984000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:317	Task worker stopped due to context cancellation	{"worker_id": 4}
2025-05-26 18:56:39.244236000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:199	Task manager stopped
2025-05-26 18:56:39.244247000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:358	任务管理器已停止.
2025-05-26 18:56:39.244073000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_processors.go:179	Analysis result processor stopped
2025-05-26 18:56:39.243999000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:410	指标流上下文取消，正在关闭...
2025-05-26 18:56:39.244094000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:169	插件更新流错误:	{"error": "启动插件更新流失败: 接收插件更新通知失败: rpc error: code = Canceled desc = context canceled"}
main.main.func4
	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:169
2025-05-26 18:56:39.244105000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:317	Task worker stopped due to context cancellation	{"worker_id": 1}
2025-05-26 18:56:39.244068000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:470	日志流上下文取消，正在关闭...
2025-05-26 18:56:39.244061000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_processors.go:33	Metric data processor stopped
2025-05-26 18:56:39.244154000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:317	Task worker stopped due to context cancellation	{"worker_id": 2}
2025-05-26 18:56:41.594547000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 74, "dropped": 0}
2025-05-26 18:56:51.474316000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:256	健康检查失败，开始重连	{"error": "连接状态异常: IDLE"}
2025-05-26 18:56:51.474573000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Reconnecting", "timestamp": "2025-05-26 18:56:51.474528000"}
2025-05-26 18:56:51.474614000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Connected", "new_state": "Reconnecting"}
2025-05-26 18:56:51.474925000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 1, "server": "localhost:50051"}
2025-05-26 18:56:51.476705000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 1}
2025-05-26 18:56:52.477831000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 2, "server": "localhost:50051"}
2025-05-26 18:56:52.479388000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 2}
2025-05-26 18:56:54.480493000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 3, "server": "localhost:50051"}
2025-05-26 18:56:54.482115000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 4}
2025-05-26 18:56:58.483059000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 4, "server": "localhost:50051"}
2025-05-26 18:56:58.485363000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 8}
2025-05-26 18:57:06.486430000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 5, "server": "localhost:50051"}
2025-05-26 18:57:06.487330000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 16}
2025-05-26 18:57:22.488266000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 6, "server": "localhost:50051"}
2025-05-26 18:57:22.490759000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 32}
2025-05-26 18:57:54.491638000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 7, "server": "localhost:50051"}
2025-05-26 18:57:54.495995000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 64}
2025-05-26 18:58:58.496415000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 8, "server": "localhost:50051"}
2025-05-26 18:58:58.855005000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 128}
2025-05-26 19:01:06.854454000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 9, "server": "localhost:50051"}
2025-05-26 19:01:06.964788000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 256}
2025-05-26 19:05:22.962409000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 10, "server": "localhost:50051"}
2025-05-26 19:05:23.354480000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 19:07:31.755606000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 75, "dropped": 0}
2025-05-26 19:10:23.394121000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 11, "server": "localhost:50051"}
2025-05-26 19:10:24.031749000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 19:15:24.031101000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 12, "server": "localhost:50051"}
2025-05-26 19:15:24.407868000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 19:18:11.973441000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 76, "dropped": 0}
2025-05-26 19:20:24.492613000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 13, "server": "localhost:50051"}
2025-05-26 19:20:25.134421000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 19:25:25.137334000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 14, "server": "localhost:50051"}
2025-05-26 19:25:25.508541000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 19:29:42.134218000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 77, "dropped": 0}
2025-05-26 19:30:25.507244000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 15, "server": "localhost:50051"}
2025-05-26 19:30:26.092511000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 19:35:26.092129000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 16, "server": "localhost:50051"}
2025-05-26 19:35:26.223637000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 19:40:26.203133000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 17, "server": "localhost:50051"}
2025-05-26 19:40:26.437105000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 19:45:12.325753000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 78, "dropped": 0}
2025-05-26 19:45:26.434569000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 18, "server": "localhost:50051"}
2025-05-26 19:45:26.532306000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 19:50:26.530269000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 19, "server": "localhost:50051"}
2025-05-26 19:50:26.583425000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 19:55:26.592724000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 20, "server": "localhost:50051"}
2025-05-26 19:55:27.000962000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 19:56:52.574730000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 79, "dropped": 0}
2025-05-26 19:58:12.608746000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 81, "dropped": 0}
2025-05-26 19:59:12.628937000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 83, "dropped": 0}
2025-05-26 19:59:22.736938000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 85, "dropped": 0}
2025-05-26 19:59:52.742700000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 87, "dropped": 0}
2025-05-26 20:00:03.103368000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 89, "dropped": 0}
2025-05-26 20:00:27.000647000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 21, "server": "localhost:50051"}
2025-05-26 20:00:27.353604000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 20:01:03.116254000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 91, "dropped": 0}
2025-05-26 20:03:13.491141000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 93, "dropped": 0}
2025-05-26 20:04:03.603614000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 95, "dropped": 0}
2025-05-26 20:05:03.728477000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 97, "dropped": 0}
2025-05-26 20:05:27.353503000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 22, "server": "localhost:50051"}
2025-05-26 20:05:27.486408000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 20:07:13.939324000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 100, "dropped": 0}
2025-05-26 20:07:44.043517000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 102, "dropped": 0}
2025-05-26 20:09:14.172302000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 104, "dropped": 0}
2025-05-26 20:10:27.486371000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 23, "server": "localhost:50051"}
2025-05-26 20:10:28.110323000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 20:11:14.475551000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 106, "dropped": 0}
2025-05-26 20:12:14.498280000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 108, "dropped": 0}
2025-05-26 20:12:54.615988000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 110, "dropped": 0}
2025-05-26 20:13:14.724357000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 112, "dropped": 0}
2025-05-26 20:13:54.740599000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 114, "dropped": 0}
2025-05-26 20:14:24.847679000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 116, "dropped": 0}
2025-05-26 20:15:14.964888000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 118, "dropped": 0}
2025-05-26 20:15:28.110229000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 24, "server": "localhost:50051"}
2025-05-26 20:15:28.113154000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 20:19:35.273148000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 119, "dropped": 0}
2025-05-26 20:20:28.112201000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 25, "server": "localhost:50051"}
2025-05-26 20:20:28.601255000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 20:21:05.311261000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 121, "dropped": 0}
2025-05-26 20:22:05.438000000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 123, "dropped": 0}
2025-05-26 20:24:05.488193000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 125, "dropped": 0}
2025-05-26 20:24:55.629500000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 127, "dropped": 0}
2025-05-26 20:25:28.601132000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 26, "server": "localhost:50051"}
2025-05-26 20:25:28.604182000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 20:25:55.768876000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 129, "dropped": 0}
2025-05-26 20:28:05.849628000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 131, "dropped": 0}
2025-05-26 20:29:05.989978000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 133, "dropped": 0}
2025-05-26 20:30:06.118111000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 134, "dropped": 0}
2025-05-26 20:30:16.151875000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 136, "dropped": 0}
2025-05-26 20:30:28.604184000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 27, "server": "localhost:50051"}
2025-05-26 20:30:28.607410000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 20:30:36.157500000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 138, "dropped": 0}
2025-05-26 20:30:56.166328000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 140, "dropped": 0}
2025-05-26 20:31:26.178905000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 142, "dropped": 0}
2025-05-26 20:31:46.295069000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 144, "dropped": 0}
2025-05-26 20:32:46.374660000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 146, "dropped": 0}
2025-05-26 20:34:26.427403000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 148, "dropped": 0}
2025-05-26 20:35:28.607529000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 28, "server": "localhost:50051"}
2025-05-26 20:35:28.709215000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 20:35:36.452666000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 150, "dropped": 0}
2025-05-26 20:36:16.464858000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 152, "dropped": 0}
2025-05-26 20:37:31.589264000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 154, "dropped": 0}
2025-05-26 20:40:02.169737000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 156, "dropped": 0}
2025-05-26 20:40:12.274402000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 161, "dropped": 0}
2025-05-26 20:40:28.709800000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 29, "server": "localhost:50051"}
2025-05-26 20:40:28.712710000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 20:40:32.282377000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 163, "dropped": 0}
2025-05-26 20:40:42.285477000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 165, "dropped": 0}
2025-05-26 20:41:12.298545000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 167, "dropped": 0}
2025-05-26 20:41:32.414612000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 169, "dropped": 0}
2025-05-26 20:42:02.425257000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 171, "dropped": 0}
2025-05-26 20:43:12.450301000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 173, "dropped": 0}
2025-05-26 20:45:12.598001000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 175, "dropped": 0}
2025-05-26 20:45:28.712718000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 30, "server": "localhost:50051"}
2025-05-26 20:45:28.715618000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 20:45:32.946101000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 177, "dropped": 0}
2025-05-26 20:47:32.990402000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 179, "dropped": 0}
2025-05-26 20:49:03.131503000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 181, "dropped": 0}
2025-05-26 20:49:13.479491000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 183, "dropped": 0}
2025-05-26 20:49:33.488061000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 185, "dropped": 0}
2025-05-26 20:49:43.492550000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 187, "dropped": 0}
2025-05-26 20:49:53.496987000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 189, "dropped": 0}
2025-05-26 20:50:03.604292000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 193, "dropped": 0}
2025-05-26 20:50:13.609763000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 196, "dropped": 0}
2025-05-26 20:50:28.715530000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 31, "server": "localhost:50051"}
2025-05-26 20:50:28.718548000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 20:50:33.620155000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 198, "dropped": 0}
2025-05-26 20:50:43.624503000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 200, "dropped": 0}
2025-05-26 20:51:03.632709000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 202, "dropped": 0}
2025-05-26 20:51:23.645321000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 204, "dropped": 0}
2025-05-26 20:51:33.650810000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 206, "dropped": 0}
2025-05-26 20:51:43.655307000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 210, "dropped": 0}
2025-05-26 20:51:53.659770000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 212, "dropped": 0}
2025-05-26 20:52:33.677574000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 214, "dropped": 0}
2025-05-26 20:52:53.690926000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 216, "dropped": 0}
2025-05-26 20:53:03.697729000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 218, "dropped": 0}
2025-05-26 20:53:23.705496000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 220, "dropped": 0}
2025-05-26 20:53:33.709998000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 222, "dropped": 0}
2025-05-26 20:54:03.734909000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 224, "dropped": 0}
2025-05-26 20:54:13.760493000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 226, "dropped": 0}
2025-05-26 20:54:23.764245000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 228, "dropped": 0}
2025-05-26 20:54:43.771357000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 230, "dropped": 0}
2025-05-26 20:55:03.776766000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 232, "dropped": 0}
2025-05-26 20:55:13.780281000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 234, "dropped": 0}
2025-05-26 20:55:23.782553000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 236, "dropped": 0}
2025-05-26 20:55:28.823762000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 32, "server": "localhost:50051"}
2025-05-26 20:55:28.964868000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 20:55:43.787619000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 238, "dropped": 0}
2025-05-26 20:55:53.791627000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 240, "dropped": 0}
2025-05-26 20:56:13.796878000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 242, "dropped": 0}
2025-05-26 20:56:23.799497000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 244, "dropped": 0}
2025-05-26 20:56:43.803021000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 246, "dropped": 0}
2025-05-26 20:56:53.805242000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 248, "dropped": 0}
2025-05-26 20:57:03.808476000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 250, "dropped": 0}
2025-05-26 20:57:23.973424000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 252, "dropped": 0}
2025-05-26 20:57:33.977177000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 254, "dropped": 0}
2025-05-26 20:57:43.993508000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 256, "dropped": 0}
2025-05-26 20:58:03.997768000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 258, "dropped": 0}
2025-05-26 20:58:14.002031000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 260, "dropped": 0}
2025-05-26 20:58:34.006373000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 262, "dropped": 0}
2025-05-26 20:58:44.008685000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 264, "dropped": 0}
2025-05-26 20:58:54.010922000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 266, "dropped": 0}
2025-05-26 20:59:14.014974000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 268, "dropped": 0}
2025-05-26 20:59:24.018323000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 270, "dropped": 0}
2025-05-26 20:59:34.020679000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 272, "dropped": 0}
2025-05-26 20:59:54.023442000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 274, "dropped": 0}
2025-05-26 21:00:24.033238000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 277, "dropped": 0}
2025-05-26 21:00:28.965433000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 33, "server": "localhost:50051"}
2025-05-26 21:00:28.967602000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 21:00:44.039908000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 279, "dropped": 0}
2025-05-26 21:00:54.044117000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 281, "dropped": 0}
2025-05-26 21:01:04.048333000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 283, "dropped": 0}
2025-05-26 21:01:14.053295000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 285, "dropped": 0}
2025-05-26 21:01:34.059371000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 287, "dropped": 0}
2025-05-26 21:01:44.082872000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 289, "dropped": 0}
2025-05-26 21:01:54.087956000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 291, "dropped": 0}
2025-05-26 21:02:14.093552000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 293, "dropped": 0}
2025-05-26 21:02:24.097030000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 295, "dropped": 0}
2025-05-26 21:02:44.104548000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 297, "dropped": 0}
2025-05-26 21:02:54.109063000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 299, "dropped": 0}
2025-05-26 21:03:04.111401000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 301, "dropped": 0}
2025-05-26 21:03:24.121818000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 303, "dropped": 0}
2025-05-26 21:03:34.125881000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 305, "dropped": 0}
2025-05-26 21:03:44.129047000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 307, "dropped": 0}
2025-05-26 21:04:04.135904000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 309, "dropped": 0}
2025-05-26 21:04:14.139212000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 311, "dropped": 0}
2025-05-26 21:04:34.146441000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 313, "dropped": 0}
2025-05-26 21:04:44.375780000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 315, "dropped": 0}
2025-05-26 21:05:04.382290000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 317, "dropped": 0}
2025-05-26 21:05:14.387791000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 319, "dropped": 0}
2025-05-26 21:05:28.966547000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 34, "server": "localhost:50051"}
2025-05-26 21:05:28.968288000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 21:05:34.394144000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 321, "dropped": 0}
2025-05-26 21:05:44.399135000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 323, "dropped": 0}
2025-05-26 21:05:54.401100000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 325, "dropped": 0}
2025-05-26 21:06:14.765727000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 327, "dropped": 0}
2025-05-26 21:06:24.771139000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 329, "dropped": 0}
2025-05-26 21:06:44.776943000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 331, "dropped": 0}
2025-05-26 21:06:54.782282000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 333, "dropped": 0}
2025-05-26 21:07:04.786028000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 335, "dropped": 0}
2025-05-26 21:07:24.792941000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 337, "dropped": 0}
2025-05-26 21:07:34.797101000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 339, "dropped": 0}
2025-05-26 21:07:44.798029000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 341, "dropped": 0}
2025-05-26 21:08:04.806112000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 343, "dropped": 0}
2025-05-26 21:08:14.811117000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 345, "dropped": 0}
2025-05-26 21:08:34.817764000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 347, "dropped": 0}
2025-05-26 21:08:44.821766000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 349, "dropped": 0}
2025-05-26 21:08:54.824673000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 351, "dropped": 0}
2025-05-26 21:09:04.830016000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 353, "dropped": 0}
2025-05-26 21:09:24.836641000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 355, "dropped": 0}
2025-05-26 21:09:44.844421000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 357, "dropped": 0}
2025-05-26 21:09:54.846851000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 359, "dropped": 0}
2025-05-26 21:10:04.850946000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 361, "dropped": 0}
2025-05-26 21:10:24.855566000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 363, "dropped": 0}
2025-05-26 21:10:28.966530000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 35, "server": "localhost:50051"}
2025-05-26 21:10:28.968573000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 21:10:34.857786000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 366, "dropped": 0}
2025-05-26 21:10:44.860766000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 368, "dropped": 0}
2025-05-26 21:11:04.866311000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 370, "dropped": 0}
2025-05-26 21:11:14.870230000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 372, "dropped": 0}
2025-05-26 21:11:24.873712000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 374, "dropped": 0}
2025-05-26 21:11:44.880913000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 376, "dropped": 0}
2025-05-26 21:11:54.885131000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 378, "dropped": 0}
2025-05-26 21:12:04.887582000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 380, "dropped": 0}
2025-05-26 21:12:24.896425000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 382, "dropped": 0}
2025-05-26 21:12:34.899074000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 384, "dropped": 0}
2025-05-26 21:12:54.905448000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 386, "dropped": 0}
2025-05-26 21:13:04.909071000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 388, "dropped": 0}
2025-05-26 21:13:14.914105000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 390, "dropped": 0}
2025-05-26 21:13:34.920205000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 392, "dropped": 0}
2025-05-26 21:13:44.923657000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 394, "dropped": 0}
2025-05-26 21:13:54.927333000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 396, "dropped": 0}
2025-05-26 21:14:14.931494000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 398, "dropped": 0}
2025-05-26 21:14:24.935969000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 400, "dropped": 0}
2025-05-26 21:14:34.940122000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 402, "dropped": 0}
2025-05-26 21:14:54.948222000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 404, "dropped": 0}
2025-05-26 21:15:05.059570000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 406, "dropped": 0}
2025-05-26 21:15:15.063647000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 408, "dropped": 0}
2025-05-26 21:15:25.066774000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 410, "dropped": 0}
2025-05-26 21:15:28.968522000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 36, "server": "localhost:50051"}
2025-05-26 21:15:28.970279000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 21:15:45.072728000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 412, "dropped": 0}
2025-05-26 21:15:55.077570000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 414, "dropped": 0}
2025-05-26 21:16:15.084839000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 416, "dropped": 0}
2025-05-26 21:16:45.424743000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 418, "dropped": 0}
2025-05-26 21:16:55.531745000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 420, "dropped": 0}
2025-05-26 21:17:05.532904000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 424, "dropped": 0}
2025-05-26 21:17:15.644016000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 426, "dropped": 0}
2025-05-26 21:17:25.646944000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 430, "dropped": 0}
2025-05-26 21:17:35.699525000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 436, "dropped": 0}
2025-05-26 21:17:45.795641000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 440, "dropped": 0}
2025-05-26 21:18:25.808053000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 442, "dropped": 0}
2025-05-26 21:18:45.912839000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 444, "dropped": 0}
2025-05-26 21:18:55.999242000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 448, "dropped": 0}
2025-05-26 21:19:06.001085000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 450, "dropped": 0}
2025-05-26 21:19:16.109187000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 452, "dropped": 0}
2025-05-26 21:19:36.211181000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 454, "dropped": 0}
2025-05-26 21:19:46.214324000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 456, "dropped": 0}
2025-05-26 21:19:56.218415000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 458, "dropped": 0}
2025-05-26 21:20:16.328482000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 460, "dropped": 0}
2025-05-26 21:20:26.359148000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 462, "dropped": 0}
2025-05-26 21:20:28.970246000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 37, "server": "localhost:50051"}
2025-05-26 21:20:28.972162000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 21:20:36.360216000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 465, "dropped": 0}
2025-05-26 21:20:46.364152000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 467, "dropped": 0}
2025-05-26 21:20:56.468379000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 471, "dropped": 0}
2025-05-26 21:21:56.484130000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 473, "dropped": 0}
2025-05-26 21:22:11.833815000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 475, "dropped": 0}
2025-05-26 21:22:32.075589000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 477, "dropped": 0}
2025-05-26 21:22:42.077907000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 479, "dropped": 0}
2025-05-26 21:22:52.181500000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 481, "dropped": 0}
2025-05-26 21:23:12.185450000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 483, "dropped": 0}
2025-05-26 21:23:22.289094000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 485, "dropped": 0}
2025-05-26 21:23:42.293830000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 487, "dropped": 0}
2025-05-26 21:23:52.398134000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 489, "dropped": 0}
2025-05-26 21:24:02.401310000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 491, "dropped": 0}
2025-05-26 21:24:22.406584000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 493, "dropped": 0}
2025-05-26 21:24:32.746264000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 495, "dropped": 0}
2025-05-26 21:24:52.754498000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 497, "dropped": 0}
2025-05-26 21:25:02.756918000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 499, "dropped": 0}
2025-05-26 21:25:12.981296000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 501, "dropped": 0}
2025-05-26 21:25:28.972129000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 38, "server": "localhost:50051"}
2025-05-26 21:25:28.973770000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 21:25:32.987921000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 503, "dropped": 0}
2025-05-26 21:25:42.990384000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 505, "dropped": 0}
2025-05-26 21:25:52.993966000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 507, "dropped": 0}
2025-05-26 21:26:12.999110000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 509, "dropped": 0}
2025-05-26 21:26:23.106304000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 511, "dropped": 0}
2025-05-26 21:26:33.110522000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 513, "dropped": 0}
2025-05-26 21:26:53.118619000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 515, "dropped": 0}
2025-05-26 21:27:03.220084000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 517, "dropped": 0}
2025-05-26 21:27:13.227470000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 519, "dropped": 0}
2025-05-26 21:27:30.930547000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 521, "dropped": 0}
2025-05-26 21:27:41.031973000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 523, "dropped": 0}
2025-05-26 21:27:51.036962000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 525, "dropped": 0}
2025-05-26 21:28:11.136394000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 527, "dropped": 0}
2025-05-26 21:28:21.236640000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 529, "dropped": 0}
2025-05-26 21:28:31.238519000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 531, "dropped": 0}
2025-05-26 21:28:51.242699000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 533, "dropped": 0}
2025-05-26 21:29:01.244934000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 535, "dropped": 0}
2025-05-26 21:29:11.346848000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 537, "dropped": 0}
2025-05-26 21:29:31.367903000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 539, "dropped": 0}
2025-05-26 21:29:41.709213000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 541, "dropped": 0}
2025-05-26 21:30:01.713057000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 543, "dropped": 0}
2025-05-26 21:30:11.740959000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 545, "dropped": 0}
2025-05-26 21:30:21.743323000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 547, "dropped": 0}
2025-05-26 21:30:26.668556000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 39, "server": "localhost:50051"}
2025-05-26 21:30:26.670912000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 21:30:41.853815000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 550, "dropped": 0}
2025-05-26 21:31:01.957774000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 552, "dropped": 0}
2025-05-26 21:31:11.960715000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 554, "dropped": 0}
2025-05-26 21:31:22.078871000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 556, "dropped": 0}
2025-05-26 21:31:42.261689000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 558, "dropped": 0}
2025-05-26 21:31:52.362343000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 560, "dropped": 0}
2025-05-26 21:32:02.467901000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 562, "dropped": 0}
2025-05-26 21:32:22.520094000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 564, "dropped": 0}
2025-05-26 21:32:42.523146000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 566, "dropped": 0}
2025-05-26 21:32:52.623379000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 568, "dropped": 0}
2025-05-26 21:33:12.626660000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 570, "dropped": 0}
2025-05-26 21:33:22.732753000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 572, "dropped": 0}
2025-05-26 21:33:32.748746000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 574, "dropped": 0}
2025-05-26 21:33:52.753109000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 576, "dropped": 0}
2025-05-26 21:34:02.754916000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 578, "dropped": 0}
2025-05-26 21:34:12.758146000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 580, "dropped": 0}
2025-05-26 21:34:22.761382000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 582, "dropped": 0}
2025-05-26 21:34:42.765194000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 584, "dropped": 0}
2025-05-26 21:34:52.768378000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 586, "dropped": 0}
2025-05-26 21:35:12.881714000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 588, "dropped": 0}
2025-05-26 21:35:23.289538000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 590, "dropped": 0}
2025-05-26 21:35:26.669865000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 40, "server": "localhost:50051"}
2025-05-26 21:35:26.672300000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 21:35:33.351816000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 592, "dropped": 0}
2025-05-26 21:35:43.402419000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 594, "dropped": 0}
2025-05-26 21:35:53.403912000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 596, "dropped": 0}
2025-05-26 21:36:13.507660000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 598, "dropped": 0}
2025-05-26 21:36:23.609146000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 600, "dropped": 0}
2025-05-26 21:36:33.611490000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 602, "dropped": 0}
2025-05-26 21:36:53.639162000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 604, "dropped": 0}
2025-05-26 21:37:03.736012000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 606, "dropped": 0}
2025-05-26 21:37:13.753139000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 608, "dropped": 0}
2025-05-26 21:37:23.757445000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 610, "dropped": 0}
2025-05-26 21:37:43.762506000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 612, "dropped": 0}
2025-05-26 21:37:53.764037000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 614, "dropped": 0}
2025-05-26 21:38:03.765630000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 616, "dropped": 0}
2025-05-26 21:38:23.899441000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 618, "dropped": 0}
2025-05-26 21:38:33.963243000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 620, "dropped": 0}
2025-05-26 21:38:44.068105000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 622, "dropped": 0}
2025-05-26 21:39:04.174668000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 624, "dropped": 0}
2025-05-26 21:39:09.276186000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 626, "dropped": 0}
2025-05-26 21:39:19.279233000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 628, "dropped": 0}
2025-05-26 21:39:39.402138000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 630, "dropped": 0}
2025-05-26 21:39:49.510366000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 632, "dropped": 0}
2025-05-26 21:39:59.513886000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 634, "dropped": 0}
2025-05-26 21:40:19.626453000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 636, "dropped": 0}
2025-05-26 21:40:26.672171000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 41, "server": "localhost:50051"}
2025-05-26 21:40:26.674948000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 21:40:30.033133000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 638, "dropped": 0}
2025-05-26 21:40:50.037264000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 641, "dropped": 0}
2025-05-26 21:41:00.397104000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 643, "dropped": 0}
2025-05-26 21:41:10.401324000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 645, "dropped": 0}
2025-05-26 21:41:30.523893000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 647, "dropped": 0}
2025-05-26 21:41:40.528115000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 651, "dropped": 0}
2025-05-26 21:42:00.939247000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 655, "dropped": 0}
2025-05-26 21:42:11.304700000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 659, "dropped": 0}
2025-05-26 21:42:31.310558000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 663, "dropped": 0}
2025-05-26 21:42:41.425706000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 665, "dropped": 0}
2025-05-26 21:42:51.428714000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 667, "dropped": 0}
2025-05-26 21:43:11.552011000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 669, "dropped": 0}
2025-05-26 21:43:21.660557000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 671, "dropped": 0}
2025-05-26 21:43:31.782137000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 673, "dropped": 0}
2025-05-26 21:43:51.789110000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 675, "dropped": 0}
2025-05-26 21:44:01.899916000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 677, "dropped": 0}
2025-05-26 21:44:11.929192000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 679, "dropped": 0}
2025-05-26 21:44:31.936552000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 681, "dropped": 0}
2025-05-26 21:44:41.971533000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 683, "dropped": 0}
2025-05-26 21:44:51.975084000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 685, "dropped": 0}
2025-05-26 21:45:12.384521000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 687, "dropped": 0}
2025-05-26 21:45:22.747142000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 689, "dropped": 0}
2025-05-26 21:45:26.674799000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 42, "server": "localhost:50051"}
2025-05-26 21:45:26.957349000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 21:45:32.751507000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 691, "dropped": 0}
2025-05-26 21:45:52.758344000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 693, "dropped": 0}
2025-05-26 21:46:03.170624000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 695, "dropped": 0}
2025-05-26 21:46:43.184965000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 697, "dropped": 0}
2025-05-26 21:46:53.294805000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 699, "dropped": 0}
2025-05-26 21:47:03.414316000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 701, "dropped": 0}
2025-05-26 21:47:23.558697000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 703, "dropped": 0}
2025-05-26 21:47:33.670728000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 705, "dropped": 0}
2025-05-26 21:47:43.699719000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 707, "dropped": 0}
2025-05-26 21:48:03.706853000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 709, "dropped": 0}
2025-05-26 21:48:13.830906000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 711, "dropped": 0}
2025-05-26 21:48:23.833550000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 713, "dropped": 0}
2025-05-26 21:48:43.949433000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 715, "dropped": 0}
2025-05-26 21:48:54.059012000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 717, "dropped": 0}
2025-05-26 21:49:04.062624000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 719, "dropped": 0}
2025-05-26 21:49:24.184237000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 721, "dropped": 0}
2025-05-26 21:49:34.186246000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 723, "dropped": 0}
2025-05-26 21:49:39.397182000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 725, "dropped": 0}
2025-05-26 21:49:59.401779000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 727, "dropped": 0}
2025-05-26 21:50:09.405966000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 729, "dropped": 0}
2025-05-26 21:50:19.489524000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 731, "dropped": 0}
2025-05-26 21:50:26.955337000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 43, "server": "localhost:50051"}
2025-05-26 21:50:26.957893000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 21:50:29.562432000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 733, "dropped": 0}
2025-05-26 21:50:39.566571000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 735, "dropped": 0}
2025-05-26 21:50:59.985789000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 738, "dropped": 0}
2025-05-26 21:51:09.990009000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 740, "dropped": 0}
2025-05-26 21:51:20.181042000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 742, "dropped": 0}
2025-05-26 21:51:30.296316000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 744, "dropped": 0}
2025-05-26 21:52:00.442888000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 746, "dropped": 0}
2025-05-26 21:52:20.757688000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 748, "dropped": 0}
2025-05-26 21:52:30.865662000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 750, "dropped": 0}
2025-05-26 21:52:51.071844000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 752, "dropped": 0}
2025-05-26 21:53:01.427135000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 754, "dropped": 0}
2025-05-26 21:53:11.430157000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 756, "dropped": 0}
2025-05-26 21:53:31.444033000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 758, "dropped": 0}
2025-05-26 21:53:41.448692000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 760, "dropped": 0}
2025-05-26 21:53:51.451367000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 762, "dropped": 0}
2025-05-26 21:54:11.457069000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 764, "dropped": 0}
2025-05-26 21:54:21.968615000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 766, "dropped": 0}
2025-05-26 21:54:31.972850000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 768, "dropped": 0}
2025-05-26 21:54:51.997925000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 770, "dropped": 0}
2025-05-26 21:55:02.002027000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 772, "dropped": 0}
2025-05-26 21:55:12.024290000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 774, "dropped": 0}
2025-05-26 21:55:26.957208000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 44, "server": "localhost:50051"}
2025-05-26 21:55:27.059463000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 21:55:32.030101000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 776, "dropped": 0}
2025-05-26 21:55:42.034296000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 778, "dropped": 0}
2025-05-26 21:55:52.037069000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 780, "dropped": 0}
2025-05-26 21:56:12.043812000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 786, "dropped": 0}
2025-05-26 21:56:32.150835000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 788, "dropped": 0}
2025-05-26 21:56:42.153496000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 790, "dropped": 0}
2025-05-26 21:56:52.155316000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 792, "dropped": 0}
2025-05-26 21:57:12.158854000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 794, "dropped": 0}
2025-05-26 21:57:22.162661000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 796, "dropped": 0}
2025-05-26 21:57:32.165608000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 798, "dropped": 0}
2025-05-26 21:58:22.193425000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 800, "dropped": 0}
2025-05-26 21:58:42.200569000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 802, "dropped": 0}
2025-05-26 21:58:52.306067000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 804, "dropped": 0}
2025-05-26 21:59:12.314835000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 806, "dropped": 0}
2025-05-26 21:59:22.410852000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 808, "dropped": 0}
2025-05-26 21:59:32.476703000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 810, "dropped": 0}
2025-05-26 21:59:52.480784000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 812, "dropped": 0}
2025-05-26 22:00:12.591619000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 814, "dropped": 0}
2025-05-26 22:00:22.700790000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 816, "dropped": 0}
2025-05-26 22:00:27.058941000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 45, "server": "localhost:50051"}
2025-05-26 22:00:27.196147000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 22:00:32.706093000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 818, "dropped": 0}
2025-05-26 22:00:52.729555000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 820, "dropped": 0}
2025-05-26 22:01:02.848941000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 823, "dropped": 0}
2025-05-26 22:01:22.852810000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 825, "dropped": 0}
2025-05-26 22:01:32.853577000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 827, "dropped": 0}
2025-05-26 22:01:42.856933000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 829, "dropped": 0}
2025-05-26 22:02:02.862192000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 831, "dropped": 0}
2025-05-26 22:02:12.883210000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 833, "dropped": 0}
2025-05-26 22:02:22.886970000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 835, "dropped": 0}
2025-05-26 22:02:43.080422000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 837, "dropped": 0}
2025-05-26 22:02:53.146281000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 839, "dropped": 0}
2025-05-26 22:03:03.171590000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 841, "dropped": 0}
2025-05-26 22:03:23.266286000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 845, "dropped": 0}
2025-05-26 22:03:33.269902000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 849, "dropped": 0}
2025-05-26 22:04:03.489851000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 851, "dropped": 0}
2025-05-26 22:04:23.605154000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 853, "dropped": 0}
2025-05-26 22:04:33.609450000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 855, "dropped": 0}
2025-05-26 22:04:43.701112000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 857, "dropped": 0}
2025-05-26 22:05:03.708025000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 859, "dropped": 0}
2025-05-26 22:05:13.713495000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 861, "dropped": 0}
2025-05-26 22:05:27.196005000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 46, "server": "localhost:50051"}
2025-05-26 22:05:27.553545000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 22:08:23.774513000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 863, "dropped": 0}
2025-05-26 22:08:33.802280000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 865, "dropped": 0}
2025-05-26 22:08:53.811158000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 867, "dropped": 0}
2025-05-26 22:09:13.913520000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 869, "dropped": 0}
2025-05-26 22:09:19.252418000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 871, "dropped": 0}
2025-05-26 22:09:29.256812000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 873, "dropped": 0}
2025-05-26 22:09:49.382493000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 875, "dropped": 0}
2025-05-26 22:09:59.386690000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 877, "dropped": 0}
2025-05-26 22:10:09.800894000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 879, "dropped": 0}
2025-05-26 22:10:27.552589000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 47, "server": "localhost:50051"}
2025-05-26 22:10:27.554013000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 22:10:29.807009000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 881, "dropped": 0}
2025-05-26 22:10:39.810153000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 883, "dropped": 0}
2025-05-26 22:10:49.915226000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 885, "dropped": 0}
2025-05-26 22:11:10.332641000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 888, "dropped": 0}
2025-05-26 22:11:30.435444000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 890, "dropped": 0}
2025-05-26 22:11:40.545011000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 892, "dropped": 0}
2025-05-26 22:12:00.565166000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 894, "dropped": 0}
2025-05-26 22:12:10.567723000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 896, "dropped": 0}
2025-05-26 22:12:20.570267000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 898, "dropped": 0}
2025-05-26 22:12:30.572785000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 900, "dropped": 0}
2025-05-26 22:12:40.705327000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 904, "dropped": 0}
2025-05-26 22:13:20.712498000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 906, "dropped": 0}
2025-05-26 22:13:50.719184000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 908, "dropped": 0}
2025-05-26 22:14:01.182247000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 910, "dropped": 0}
2025-05-26 22:14:21.186328000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 912, "dropped": 0}
2025-05-26 22:15:27.552634000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 48, "server": "localhost:50051"}
2025-05-26 22:15:27.555355000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 22:15:31.535359000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 914, "dropped": 0}
2025-05-26 22:17:01.553336000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 920, "dropped": 0}
2025-05-26 22:19:01.715455000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 922, "dropped": 0}
2025-05-26 22:19:21.740855000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 924, "dropped": 0}
2025-05-26 22:19:32.042362000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 926, "dropped": 0}
2025-05-26 22:20:27.555311000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 49, "server": "localhost:50051"}
2025-05-26 22:20:27.927439000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 22:21:22.220072000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 929, "dropped": 0}
2025-05-26 22:22:02.638924000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 931, "dropped": 0}
2025-05-26 22:22:13.009895000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 935, "dropped": 0}
2025-05-26 22:22:23.121921000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 937, "dropped": 0}
2025-05-26 22:22:53.133275000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 939, "dropped": 0}
2025-05-26 22:25:03.275770000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 941, "dropped": 0}
2025-05-26 22:25:27.926382000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 50, "server": "localhost:50051"}
2025-05-26 22:25:28.263396000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 22:26:03.631616000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 943, "dropped": 0}
2025-05-26 22:26:43.744195000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 945, "dropped": 0}
2025-05-26 22:28:54.133751000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 947, "dropped": 0}
2025-05-26 22:29:04.157483000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 949, "dropped": 0}
2025-05-26 22:29:09.228320000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 953, "dropped": 0}
2025-05-26 22:29:29.231695000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 955, "dropped": 0}
2025-05-26 22:29:39.336425000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 957, "dropped": 0}
2025-05-26 22:30:28.262437000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 51, "server": "localhost:50051"}
2025-05-26 22:30:28.659406000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 22:32:09.376345000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 960, "dropped": 0}
2025-05-26 22:32:59.395455000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 962, "dropped": 0}
2025-05-26 22:33:39.761429000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 964, "dropped": 0}
2025-05-26 22:35:28.658330000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 52, "server": "localhost:50051"}
2025-05-26 22:35:29.067023000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 22:35:29.799453000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 966, "dropped": 0}
2025-05-26 22:36:09.806539000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 968, "dropped": 0}
2025-05-26 22:36:59.930934000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 970, "dropped": 0}
2025-05-26 22:39:29.967801000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 972, "dropped": 0}
2025-05-26 22:40:29.066140000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 53, "server": "localhost:50051"}
2025-05-26 22:40:29.443008000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 22:41:50.100078000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 974, "dropped": 0}
2025-05-26 22:42:00.116940000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 976, "dropped": 0}
2025-05-26 22:42:20.155468000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 979, "dropped": 0}
2025-05-26 22:44:30.658195000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 981, "dropped": 0}
2025-05-26 22:45:00.764149000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 983, "dropped": 0}
2025-05-26 22:45:29.509464000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 54, "server": "localhost:50051"}
2025-05-26 22:45:30.356556000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 22:46:11.018527000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 985, "dropped": 0}
2025-05-26 22:47:41.067887000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 987, "dropped": 0}
2025-05-26 22:50:11.445089000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 989, "dropped": 0}
2025-05-26 22:50:30.359103000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 55, "server": "localhost:50051"}
2025-05-26 22:50:30.455303000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 22:52:42.046871000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 992, "dropped": 0}
2025-05-26 22:55:12.189517000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 994, "dropped": 0}
2025-05-26 22:55:30.457792000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 56, "server": "localhost:50051"}
2025-05-26 22:55:30.842761000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 22:56:52.294690000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 998, "dropped": 0}
2025-05-26 22:57:12.402898000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1000, "dropped": 0}
2025-05-26 22:58:42.516249000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1002, "dropped": 0}
2025-05-26 23:00:30.844639000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 57, "server": "localhost:50051"}
2025-05-26 23:00:31.221019000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 23:00:52.658470000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1004, "dropped": 0}
2025-05-26 23:01:52.773148000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1006, "dropped": 0}
2025-05-26 23:02:12.792815000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1008, "dropped": 0}
2025-05-26 23:02:22.806126000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1010, "dropped": 0}
2025-05-26 23:02:42.882832000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1011, "dropped": 0}
2025-05-26 23:03:12.899311000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1013, "dropped": 0}
2025-05-26 23:04:03.034650000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1015, "dropped": 0}
2025-05-26 23:04:43.093114000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1017, "dropped": 0}
2025-05-26 23:05:31.223612000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 58, "server": "localhost:50051"}
2025-05-26 23:05:31.642442000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 23:05:53.206502000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1019, "dropped": 0}
2025-05-26 23:07:33.241929000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1021, "dropped": 0}
2025-05-26 23:08:23.278287000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1023, "dropped": 0}
2025-05-26 23:10:31.663513000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 59, "server": "localhost:50051"}
2025-05-26 23:10:32.034932000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 23:10:53.440951000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1025, "dropped": 0}
2025-05-26 23:11:23.557992000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1027, "dropped": 0}
2025-05-26 23:12:13.672382000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1029, "dropped": 0}
2025-05-26 23:12:43.687415000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1030, "dropped": 0}
2025-05-26 23:13:23.711663000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1032, "dropped": 0}
2025-05-26 23:13:43.812498000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1034, "dropped": 0}
2025-05-26 23:14:53.926280000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1036, "dropped": 0}
2025-05-26 23:15:32.274800000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 60, "server": "localhost:50051"}
2025-05-26 23:15:32.404583000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 23:15:54.039414000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1038, "dropped": 0}
2025-05-26 23:16:14.147417000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1040, "dropped": 0}
2025-05-26 23:17:34.262365000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1042, "dropped": 0}
2025-05-26 23:19:59.399569000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1044, "dropped": 0}
2025-05-26 23:20:32.407888000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 61, "server": "localhost:50051"}
2025-05-26 23:20:32.441655000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 23:22:29.545015000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1046, "dropped": 0}
2025-05-26 23:24:59.641253000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1049, "dropped": 0}
2025-05-26 23:25:09.693188000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1051, "dropped": 0}
2025-05-26 23:25:32.444108000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 62, "server": "localhost:50051"}
2025-05-26 23:25:32.473412000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 23:27:49.896515000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1053, "dropped": 0}
2025-05-26 23:30:20.052058000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1055, "dropped": 0}
2025-05-26 23:30:32.475145000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 63, "server": "localhost:50051"}
2025-05-26 23:30:32.611603000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 23:31:30.084936000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1057, "dropped": 0}
2025-05-26 23:32:40.143065000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1059, "dropped": 0}
2025-05-26 23:33:10.183849000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1061, "dropped": 0}
2025-05-26 23:35:32.627619000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 64, "server": "localhost:50051"}
2025-05-26 23:35:32.899916000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 23:37:00.452767000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1064, "dropped": 0}
2025-05-26 23:38:20.571233000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1066, "dropped": 0}
2025-05-26 23:39:10.999663000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1068, "dropped": 0}
2025-05-26 23:40:32.902598000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 65, "server": "localhost:50051"}
2025-05-26 23:40:33.160319000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 23:40:51.069331000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1070, "dropped": 0}
2025-05-26 23:42:31.486370000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1072, "dropped": 0}
2025-05-26 23:42:51.867241000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1074, "dropped": 0}
2025-05-26 23:44:01.884150000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1076, "dropped": 0}
2025-05-26 23:45:33.652751000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 66, "server": "localhost:50051"}
2025-05-26 23:45:34.298089000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 23:46:02.122925000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1078, "dropped": 0}
2025-05-26 23:46:32.142648000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1080, "dropped": 0}
2025-05-26 23:46:42.196833000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1082, "dropped": 0}
2025-05-26 23:47:22.304868000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1085, "dropped": 0}
2025-05-26 23:48:02.417762000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1087, "dropped": 0}
2025-05-26 23:48:32.443191000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1091, "dropped": 0}
2025-05-26 23:49:52.476809000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1093, "dropped": 0}
2025-05-26 23:50:02.559539000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1094, "dropped": 0}
