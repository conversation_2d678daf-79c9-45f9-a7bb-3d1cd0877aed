load conf file: config/agent_config.yaml
2025-05-26 18:56:21.471048000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent 启动中...
2025-05-26 18:56:21.471184000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:42	Agent ID:	{"agent_id": "7623a7b2-bd99-4537-8606-c6251fdecbd9"}
2025-05-26 18:56:21.471199000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:51	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-26 18:56:21.471208000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-26 18:56:21.473523000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Connecting", "new_state": "Connected"}
2025-05-26 18:56:21.473537000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:136	连接已建立	{"server": "localhost:50051"}
2025-05-26 18:56:21.474672000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:87	Agent 注册成功
2025-05-26 18:56:21.474693000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:509	开始初始化插件管理器
2025-05-26 18:56:21.474702000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:513	预加载插件	{"device_type": "system"}
2025-05-26 18:56:21.474710000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "system"}
2025-05-26 18:56:21.474720000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:41	请求插件	{"plugin_name": "system-collector", "version": "", "device_type": "system"}
2025-05-26 18:56:21.518031000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:133	从插件路径加载插件	{"device_type": "system", "plugin_name": "system-collector", "plugin_version": "1.0.0", "plugin_path": "plugins/build/system-collector-1.0.0.so", "absolute_path": "plugins/build/system-collector-1.0.0.so"}
2025-05-26 18:56:21.527824000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:259	插件校验和	{"path": "plugins/build/system-collector-1.0.0.so", "checksum": "f0ac593c08a16ea451635452f8efb309710bf7239c40654b9812caab44753f6d"}
2025-05-26 18:56:21.534666000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:238	插件加载成功	{"plugin_id": "system", "plugin_path": "plugins/build/system-collector-1.0.0.so"}
2025-05-26 18:56:21.534687000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:523	插件预加载成功	{"device_type": "system"}
2025-05-26 18:56:21.534694000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:513	预加载插件	{"device_type": "mysql"}
2025-05-26 18:56:21.534703000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "mysql"}
2025-05-26 18:56:21.534712000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:41	请求插件	{"plugin_name": "mysql-collector", "version": "", "device_type": "mysql"}
2025-05-26 18:56:21.577302000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:133	从插件路径加载插件	{"device_type": "mysql", "plugin_name": "mysql-collector", "plugin_version": "1.0.0", "plugin_path": "plugins/build/mysql-collector-1.0.0.so", "absolute_path": "plugins/build/mysql-collector-1.0.0.so"}
2025-05-26 18:56:21.584458000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:259	插件校验和	{"path": "plugins/build/mysql-collector-1.0.0.so", "checksum": "20e8b76acad66a8fa1bc98a7ca6c178e90eea267397d6165952bff6fa6489048"}
2025-05-26 18:56:21.592091000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:238	插件加载成功	{"plugin_id": "mysql", "plugin_path": "plugins/build/mysql-collector-1.0.0.so"}
2025-05-26 18:56:21.592117000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:523	插件预加载成功	{"device_type": "mysql"}
2025-05-26 18:56:21.592131000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:526	插件管理器初始化完成
2025-05-26 18:56:21.592186000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Connecting", "timestamp": "2025-05-26 18:56:21.471208000"}
2025-05-26 18:56:21.592208000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-26 18:56:21.473523000"}
2025-05-26 18:56:21.592218000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:581	日志转发器正在启动...
2025-05-26 18:56:21.592243000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:248	任务流已连接
2025-05-26 18:56:21.592255000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:486	启动插件更新流监听
2025-05-26 18:56:21.592270000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:133	启动插件更新流监听
2025-05-26 18:56:21.592277000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:174	Task manager started	{"worker_count": 5}
2025-05-26 18:56:21.592243000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:370	指标流已连接
2025-05-26 18:56:21.592497000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 4}
2025-05-26 18:56:21.592512000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 3}
2025-05-26 18:56:21.592514000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 2}
2025-05-26 18:56:21.592534000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_processors.go:16	Metric data processor started
2025-05-26 18:56:21.592542000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 1}
2025-05-26 18:56:21.592243000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:432	日志流已连接
2025-05-26 18:56:21.592579000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 0}
2025-05-26 18:56:21.592584000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "/var/log/system.log", "source": "system"}
2025-05-26 18:56:21.592523000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_processors.go:174	Analysis result processor started
2025-05-26 18:56:21.592682000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent.log"}
2025-05-26 18:56:21.592702000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent_test.log"}
2025-05-26 18:56:21.592769000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/control_plane_test.log", "source": "agent"}
2025-05-26 18:56:21.592837000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/plane.log", "source": "agent"}
2025-05-26 18:56:21.592849000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:594	日志转发器启动完成	{"active_watchers": 3}
