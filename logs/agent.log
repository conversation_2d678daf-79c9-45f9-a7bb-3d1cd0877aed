2024-05-24 10:00:01 [INFO] Agent 启动中...
2024-05-24 10:00:02 [DEBUG] 连接到控制平面
2024-05-24 10:00:03 [WARN] 配置项缺失，使用默认值
2024-05-24 10:00:04 [INFO] Agent 注册成功
2024-05-24 10:00:05 [ERROR] 某个错误信息用于测试
2025-05-24 23:35:58.256261000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 232, "dropped": 0}
2025-05-24 23:36:00.415161000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:40	Agent 启动中...
2025-05-24 23:36:00.415612000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent ID:	{"agent_id": "test-agent-001"}
2025-05-24 23:36:00.415632000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:50	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-24 23:36:00.415698000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-24 23:36:00.420656000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Connecting", "new_state": "Connected"}
2025-05-24 23:36:00.420693000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:136	连接已建立	{"server": "localhost:50051"}
2025-05-24 23:36:00.422463000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:86	Agent 注册成功
2025-05-24 23:36:00.422507000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connecting", "timestamp": "2025-05-24 23:36:00.415698000"}
2025-05-24 23:36:00.422525000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-24 23:36:00.420655000"}
2025-05-24 23:36:00.422641000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:298	指标流已连接
2025-05-24 23:36:00.422592000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:581	日志转发器正在启动...
2025-05-24 23:36:00.422647000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:220	任务流已连接
2025-05-24 23:36:00.422679000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:360	日志流已连接
2025-05-24 23:36:00.422769000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "/var/log/system.log", "source": "system"}
2025-05-24 23:36:00.422844000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent.log"}
2025-05-24 23:36:00.422871000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent_test.log"}
2025-05-24 23:36:00.422906000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/control_plane_test.log", "source": "agent"}
2025-05-24 23:36:00.422944000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/plane.log", "source": "agent"}
2025-05-24 23:36:00.422952000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:594	日志转发器启动完成	{"active_watchers": 3}
2025-05-24 23:36:08.258173000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 244, "dropped": 0}
2025-05-24 23:36:10.425322000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 4, "dropped": 0}
2024-05-24 10:01:01 [INFO] 新的日志条目 - 测试日志转发
2024-05-24 10:01:02 [ERROR] 测试错误日志转发
2025-05-24 23:36:18.260515000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 246, "dropped": 0}
2025-05-24 23:36:20.426724000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 6, "dropped": 0}
2024-05-24 10:01:01 [INFO] 批量日志测试 - 条目 1
2024-05-24 10:02:01 [INFO] 批量日志测试 - 条目 2
2024-05-24 10:03:01 [INFO] 批量日志测试 - 条目 3
2024-05-24 10:04:01 [INFO] 批量日志测试 - 条目 4
2024-05-24 10:05:01 [INFO] 批量日志测试 - 条目 5
2025-05-24 23:36:44.982878000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:176	接收到退出信号，正在关闭 Agent...
2025-05-24 23:36:44.983173000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:338	指标流上下文取消，正在关闭...
2025-05-24 23:36:44.983190000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:398	日志流上下文取消，正在关闭...
2025-05-24 23:36:44.983247000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:284	任务流正在关闭...
2025-05-24 23:36:44.983277000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:286	所有采集器已停止.
2025-05-24 23:36:44.983269000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:251	停止接收任务配置，流已关闭或上下文已取消:	{"error": "rpc error: code = Canceled desc = context canceled"}
2025-05-24 23:36:48.266549000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 308, "dropped": 0}
2025-05-24 23:36:50.432560000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 68, "dropped": 0}
2025-05-24 23:37:00.420907000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:225	健康检查失败，开始重连	{"error": "连接状态异常: IDLE"}
2025-05-24 23:37:00.421172000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Connected", "new_state": "Reconnecting"}
2025-05-24 23:37:00.421202000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Reconnecting", "timestamp": "2025-05-24 23:37:00.421163000"}
2025-05-24 23:37:00.421476000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 1, "server": "localhost:50051"}
2025-05-24 23:37:10.422579000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 1}
2025-05-24 23:37:10.438885000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 69, "dropped": 0}
2025-05-24 23:37:11.429575000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 2, "server": "localhost:50051"}
2025-05-24 23:37:18.272763000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 309, "dropped": 0}
2025-05-24 23:37:21.430952000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 2}
2025-05-24 23:37:23.432188000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 3, "server": "localhost:50051"}
2025-05-24 23:37:33.433492000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 4}
2025-05-24 23:37:37.434686000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 4, "server": "localhost:50051"}
2025-05-24 23:37:47.436023000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 8}
2025-05-24 23:37:55.437245000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 5, "server": "localhost:50051"}
2025-05-24 23:38:05.440263000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 16}
2025-05-24 23:38:20.452307000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 83, "dropped": 0}
2025-05-24 23:38:21.542817000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 6, "server": "localhost:50051"}
2025-05-24 23:38:21.545298000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Reconnecting", "new_state": "Connected"}
2025-05-24 23:38:21.545406000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:337	重连成功	{"server": "localhost:50051"}
2025-05-24 23:38:21.545419000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-24 23:38:21.545287000"}
2025-05-24 23:38:28.285562000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 323, "dropped": 0}
2025-05-24 23:39:00.564825000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 88, "dropped": 0}
2025-05-24 23:39:08.293980000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 328, "dropped": 0}
2025-05-24 23:39:18.295579000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 334, "dropped": 0}
2025-05-24 23:39:20.569483000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 94, "dropped": 0}
2025-05-24 23:39:21.545156000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:225	健康检查失败，开始重连	{"error": "连接状态异常: IDLE"}
2025-05-24 23:39:21.545363000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Reconnecting", "timestamp": "2025-05-24 23:39:21.545348000"}
2025-05-24 23:39:21.545386000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Connected", "new_state": "Reconnecting"}
2025-05-24 23:39:21.545738000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 1, "server": "localhost:50051"}
2025-05-24 23:39:31.546851000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 1}
2025-05-24 23:39:32.548039000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 2, "server": "localhost:50051"}
2025-05-24 23:39:42.549335000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 2}
2025-05-24 23:39:44.550465000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 3, "server": "localhost:50051"}
2025-05-24 23:39:54.551739000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 4}
2025-05-24 23:39:58.552892000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 4, "server": "localhost:50051"}
2025-05-24 23:40:08.573953000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 8}
2025-05-24 23:40:16.678695000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 5, "server": "localhost:50051"}
2025-05-24 23:40:26.679838000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 16}
2025-05-24 23:40:38.241451000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:298	尝试重连	{"attempt": 15, "server": "localhost:50051"}
2025-05-24 23:40:42.779901000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 6, "server": "localhost:50051"}
2025-05-24 23:40:48.246080000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:305	重连失败，等待重试	{"error": "context deadline exceeded", "backoff": 300}
2025-05-24 23:40:52.782891000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 32}
2025-05-24 23:41:24.885549000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 7, "server": "localhost:50051"}
2025-05-24 23:41:35.244038000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 64}
2025-05-24 23:42:39.405710000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 8, "server": "localhost:50051"}
2025-05-24 23:42:50.094940000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 128}
2025-05-24 23:43:00.662694000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 100, "dropped": 0}
2025-05-24 23:43:08.488068000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 340, "dropped": 0}
2025-05-24 23:44:58.320009000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 9, "server": "localhost:50051"}
2025-05-24 23:45:08.406784000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 256}
2025-05-24 23:45:48.299633000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:298	尝试重连	{"attempt": 16, "server": "localhost:50051"}
2025-05-24 23:45:58.318982000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:305	重连失败，等待重试	{"error": "context deadline exceeded", "backoff": 300}
2025-05-24 23:47:20.732143000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 101, "dropped": 0}
2025-05-24 23:47:28.541193000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 341, "dropped": 0}
2025-05-24 23:48:58.674432000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 348, "dropped": 0}
2025-05-24 23:49:00.819960000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 108, "dropped": 0}
2025-05-24 23:49:24.519805000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 10, "server": "localhost:50051"}
2025-05-24 23:49:34.886957000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 300}
2025-05-24 23:50:58.322202000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:298	尝试重连	{"attempt": 17, "server": "localhost:50051"}
2025-05-24 23:51:08.326791000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:305	重连失败，等待重试	{"error": "context deadline exceeded", "backoff": 300}
2025-05-24 23:53:40.881209000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 112, "dropped": 0}
2025-05-24 23:53:48.866660000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 352, "dropped": 0}
2025-05-24 23:54:34.888504000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 11, "server": "localhost:50051"}
2025-05-24 23:54:45.247194000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 300}
2025-05-24 23:56:08.326457000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:298	尝试重连	{"attempt": 18, "server": "localhost:50051"}
2025-05-24 23:56:18.331025000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:305	重连失败，等待重试	{"error": "context deadline exceeded", "backoff": 300}
2025-05-24 23:57:01.198706000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 116, "dropped": 0}
2025-05-24 23:57:09.007067000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 356, "dropped": 0}
2025-05-24 23:57:59.031729000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 361, "dropped": 0}
2025-05-24 23:58:01.208057000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 121, "dropped": 0}
2025-05-24 23:59:31.227541000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 125, "dropped": 0}
2025-05-24 23:59:39.150356000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 365, "dropped": 0}
2025-05-24 23:59:45.328263000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 12, "server": "localhost:50051"}
2025-05-24 23:59:55.435835000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 300}
2025-05-25 00:00:00.543169000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:264	监控的日志文件已被删除	{"file": "/var/log/system.log"}
2025-05-25 00:00:00.543270000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:215	移除日志文件监控	{"file": "/var/log/system.log"}
2025-05-25 00:00:06.027396000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:264	监控的日志文件已被删除	{"file": "/var/log/system.log"}
2025-05-25 00:00:06.028081000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:215	移除日志文件监控	{"file": "/var/log/system.log"}
2025-05-25 00:01:18.411792000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:298	尝试重连	{"attempt": 19, "server": "localhost:50051"}
2025-05-25 00:01:28.413588000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:305	重连失败，等待重试	{"error": "context deadline exceeded", "backoff": 300}
2025-05-25 00:04:55.434739000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 13, "server": "localhost:50051"}
2025-05-25 00:05:05.551816000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 300}
2025-05-25 00:06:28.412732000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:298	尝试重连	{"attempt": 20, "server": "localhost:50051"}
2025-05-25 00:06:38.415146000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:305	重连失败，等待重试	{"error": "context deadline exceeded", "backoff": 300}
2025-05-25 14:52:47.367876000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:40	Agent 启动中...
2025-05-25 14:52:47.381882000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent ID:	{"agent_id": "5a5bcaaf-cd71-4673-b500-cfda3db896ea"}
2025-05-25 14:52:47.381907000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:50	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-25 14:52:47.381931000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:231	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-25 14:52:47.382058000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:47.483248000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:47.584721000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:47.685943000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:47.787290000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:47.888538000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:47.989774000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:48.090324000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:48.191551000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:48.292769000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:48.393989000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:48.495215000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:48.598398000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:48.698753000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:48.799928000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:48.901104000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:49.002326000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:49.103554000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:49.204798000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:49.306017000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:49.407158000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:49.508455000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:49.609671000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:49.710397000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:49.811736000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:49.912068000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:50.013279000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:50.114495000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:50.214859000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:50.316091000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:50.417333000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:50.518501000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:50.619704000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:50.720881000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:50.822044000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:50.923280000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:51.024523000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:51.125746000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:51.226581000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:51.327743000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:51.428967000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:51.530320000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:51.631693000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:51.732509000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:51.833890000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:51.935226000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:52.036594000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:52.137895000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:52.239253000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:52.340622000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:52.441910000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:52.543209000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:52.644409000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:52.745638000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:52.847005000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:52.948345000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:53.049675000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:53.150948000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:53.252055000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:53.353239000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:53.454574000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:53.555908000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:53.656309000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:53.757452000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:53.858502000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:53.959706000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:54.060910000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:54.162119000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:54.263337000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:54.364555000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:54.465372000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:54.566677000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:54.667982000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:54.768272000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:54.869475000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:54.970706000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:55.071918000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:55.173113000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:55.274319000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:55.375543000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:55.476738000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:55.577984000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:55.679299000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:55.780511000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:55.881712000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:55.982934000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:56.084171000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:56.185376000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:56.286590000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:56.387793000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:56.489014000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:56.590260000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:56.691613000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:56.792815000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:56.894026000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:56.995213000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:57.096420000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:57.197662000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:57.298883000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 14:52:57.382244000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:231	连接状态变更	{"old_state": "Connecting", "new_state": "Disconnected"}
2025-05-25 14:52:57.382359000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:67	连接控制平面失败:	{"error": "创建连接失败: 连接超时: context deadline exceeded"}
main.main
	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:67
runtime.main
	/usr/local/go/src/runtime/proc.go:283
2025-05-25 15:40:40.806677000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:40	Agent 启动中...
2025-05-25 15:40:40.821672000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent ID:	{"agent_id": "26088ec6-f8f4-4a40-81c9-db5c983dc423"}
2025-05-25 15:40:40.821712000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:50	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-25 15:40:40.821737000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:231	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-25 15:40:40.821885000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:40.922989000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:41.024161000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:41.125301000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:41.226548000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:41.327783000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:41.429059000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:41.530293000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:41.631556000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:41.732873000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:41.834079000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:41.935348000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:42.036642000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:42.137878000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:42.239109000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:42.340349000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:42.441552000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:42.542851000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:42.644097000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:42.745342000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:42.846544000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:42.947780000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:43.048980000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:43.150185000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:43.251392000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:43.352169000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:43.453393000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:43.554634000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:43.655860000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:43.757148000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:43.857848000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:43.959104000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:44.060376000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:44.161642000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:44.262932000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:44.364253000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:44.465582000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:44.566888000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:44.667276000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:44.768604000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:44.869918000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:44.971243000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:45.072609000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:45.174040000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:45.275418000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:45.376752000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:45.477972000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:45.579223000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:45.680522000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:45.781787000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:45.883165000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:45.984556000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:46.085903000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:46.187216000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:46.288586000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:46.389841000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:46.491071000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:46.592303000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:46.693541000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:46.794828000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:46.896149000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:46.997410000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:47.098654000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:47.199938000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:47.301234000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:47.402463000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:47.503707000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:47.604939000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:47.706169000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:47.807443000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:47.908874000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:48.010081000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:48.111296000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:48.212495000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:48.313669000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:48.414877000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:48.516093000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:48.617300000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:48.718528000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:48.819762000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:48.921149000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:49.022504000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:49.123830000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:49.225099000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:49.326376000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:49.427629000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:49.528885000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:49.630159000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:49.731434000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:49.832696000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:49.933836000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:50.033967000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:50.135087000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:50.236319000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:50.337420000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:50.437785000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:50.538953000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:50.640129000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:50.741364000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:40:50.823296000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:231	连接状态变更	{"old_state": "Connecting", "new_state": "Disconnected"}
2025-05-25 15:40:50.823545000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:67	连接控制平面失败:	{"error": "创建连接失败: 连接超时: context deadline exceeded"}
main.main
	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:67
runtime.main
	/usr/local/go/src/runtime/proc.go:283
2025-05-25 15:47:24.298132000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:40	Agent 启动中...
2025-05-25 15:47:24.395817000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent ID:	{"agent_id": "c1169546-c941-473b-84d8-2a7e18ac3a0f"}
2025-05-25 15:47:24.395866000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:50	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-25 15:47:24.395901000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:231	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-25 15:47:24.395996000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:24.497051000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:24.597857000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:24.699028000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:24.799819000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:24.901553000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:25.002833000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:25.104165000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:25.205502000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:25.306433000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:25.407462000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:25.508760000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:25.610222000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:25.711503000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:25.811860000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:25.912496000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:26.013653000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:26.115653000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:26.216819000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:26.318087000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:26.419517000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:26.520842000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:26.622104000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:26.723407000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:26.824189000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:26.925446000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:27.026924000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:27.128202000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:27.228921000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:27.330211000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:27.431536000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:27.532861000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:27.633437000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:27.734807000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:27.836121000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:27.937363000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:28.038700000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:28.140081000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:28.241397000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:28.342703000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:28.444030000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:28.545179000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:28.646367000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:28.747788000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:28.849092000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:28.950362000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:29.051683000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:29.152998000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:29.254309000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:29.355693000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:29.457036000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:29.558422000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:29.659740000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:29.761107000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:29.862429000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:29.963037000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:30.064341000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:30.165718000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:30.267048000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:30.368381000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:30.469683000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:30.571000000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:30.672312000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:30.773641000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:30.874957000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:30.976275000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:31.077647000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:31.178954000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:31.280281000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:31.381602000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:31.483000000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:31.584361000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:31.685681000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:31.786997000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:31.888377000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:31.989638000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:32.090969000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:32.192275000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:32.293481000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:32.436539000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:32.537693000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:32.638843000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:32.740193000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:32.840727000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:32.941936000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:33.043336000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:33.144665000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:33.245907000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:33.347090000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:33.448209000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:33.549390000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:33.650567000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:33.751684000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:33.851898000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:33.952826000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:34.053921000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:34.154242000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:34.255396000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:34.356550000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:158	检查连接状态	{"state": "IDLE"}
2025-05-25 15:47:34.397027000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:231	连接状态变更	{"old_state": "Connecting", "new_state": "Disconnected"}
2025-05-25 15:47:34.397087000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:67	连接控制平面失败:	{"error": "创建连接失败: 连接超时: context deadline exceeded"}
main.main
	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:67
runtime.main
	/usr/local/go/src/runtime/proc.go:283
2025-05-25 15:49:55.079371000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:40	Agent 启动中...
2025-05-25 15:49:55.101183000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent ID:	{"agent_id": "72f420a6-03dd-4447-8990-8d4812ae23d4"}
2025-05-25 15:49:55.101248000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:50	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-25 15:49:55.101291000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-25 15:49:55.125461000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Connecting", "new_state": "Connected"}
2025-05-25 15:49:55.125534000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:136	连接已建立	{"server": "localhost:50051"}
2025-05-25 15:49:55.166573000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:86	Agent 注册成功
2025-05-25 15:49:55.166646000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:581	日志转发器正在启动...
2025-05-25 15:49:55.166663000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connecting", "timestamp": "2025-05-25 15:49:55.101290000"}
2025-05-25 15:49:55.166683000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-25 15:49:55.125459000"}
2025-05-25 15:49:55.166843000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:220	任务流已连接
2025-05-25 15:49:55.166875000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:298	指标流已连接
2025-05-25 15:49:55.166883000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:360	日志流已连接
2025-05-25 15:49:55.167069000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "/var/log/system.log", "source": "system"}
2025-05-25 15:49:55.167134000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent.log"}
2025-05-25 15:49:55.167159000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent_test.log"}
2025-05-25 15:49:55.167206000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/control_plane_test.log", "source": "agent"}
2025-05-25 15:49:55.167252000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/plane.log", "source": "agent"}
2025-05-25 15:49:55.167259000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:594	日志转发器启动完成	{"active_watchers": 3}
2025-05-25 15:50:10.169512000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 2, "dropped": 0}
2025-05-25 15:50:15.171485000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 3, "dropped": 0}
2025-05-25 16:10:45.433677000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 4, "dropped": 0}
2025-05-25 16:20:56.094735000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 5, "dropped": 0}
2025-05-25 16:43:06.557702000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 6, "dropped": 0}
2025-05-25 17:05:17.336997000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 7, "dropped": 0}
2025-05-25 17:22:45.430309000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:40	Agent 启动中...
2025-05-25 17:22:45.453019000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent ID:	{"agent_id": "06ae1344-7fff-475d-8aee-6d2cd9df1c53"}
2025-05-25 17:22:45.453117000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:50	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-25 17:22:45.453163000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-25 17:22:45.496728000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Connecting", "new_state": "Connected"}
2025-05-25 17:22:45.496770000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:136	连接已建立	{"server": "localhost:50051"}
2025-05-25 17:22:45.501751000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:86	Agent 注册成功
2025-05-25 17:22:45.501808000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:581	日志转发器正在启动...
2025-05-25 17:22:45.501931000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connecting", "timestamp": "2025-05-25 17:22:45.453162000"}
2025-05-25 17:22:45.501946000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-25 17:22:45.496727000"}
2025-05-25 17:22:45.502062000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:298	指标流已连接
2025-05-25 17:22:45.502081000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:220	任务流已连接
2025-05-25 17:22:45.502101000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "/var/log/system.log", "source": "system"}
2025-05-25 17:22:45.502153000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:360	日志流已连接
2025-05-25 17:22:45.502266000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent.log"}
2025-05-25 17:22:45.502284000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent_test.log"}
2025-05-25 17:22:45.502330000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/control_plane_test.log", "source": "agent"}
2025-05-25 17:22:45.502389000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/plane.log", "source": "agent"}
2025-05-25 17:22:45.502400000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:594	日志转发器启动完成	{"active_watchers": 3}
2025-05-25 17:23:00.504221000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 2, "dropped": 0}
2025-05-25 17:23:10.508437000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 3, "dropped": 0}
2025-05-25 17:29:35.756975000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 4, "dropped": 0}
2025-05-25 19:54:35.380116000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:40	Agent 启动中...
2025-05-25 19:54:35.402721000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent ID:	{"agent_id": "c34e5940-164e-4f07-8068-e4dafd95c1a5"}
2025-05-25 19:54:35.402781000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:50	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-25 19:54:35.402822000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-25 19:54:35.656878000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Connecting", "new_state": "Connected"}
2025-05-25 19:54:35.656958000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:136	连接已建立	{"server": "localhost:50051"}
2025-05-25 19:54:36.039552000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:86	Agent 注册成功
2025-05-25 19:54:36.039655000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:581	日志转发器正在启动...
2025-05-25 19:54:36.039704000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connecting", "timestamp": "2025-05-25 19:54:35.402822000"}
2025-05-25 19:54:36.039739000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-25 19:54:35.656875000"}
2025-05-25 19:54:36.039749000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:220	任务流已连接
2025-05-25 19:54:36.039836000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:298	指标流已连接
2025-05-25 19:54:36.039870000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:360	日志流已连接
2025-05-25 19:54:36.040334000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "/var/log/system.log", "source": "system"}
2025-05-25 19:54:36.040406000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent.log"}
2025-05-25 19:54:36.040431000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent_test.log"}
2025-05-25 19:54:36.040479000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/control_plane_test.log", "source": "agent"}
2025-05-25 19:54:36.040525000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/plane.log", "source": "agent"}
2025-05-25 19:54:36.040534000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:594	日志转发器启动完成	{"active_watchers": 3}
2025-05-25 19:54:51.041742000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 3, "dropped": 0}
2025-05-25 19:54:56.041995000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 4, "dropped": 0}
2025-05-25 19:58:19.100789000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:176	接收到退出信号，正在关闭 Agent...
2025-05-25 19:58:19.118019000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:398	日志流上下文取消，正在关闭...
2025-05-25 19:58:19.118081000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:284	任务流正在关闭...
2025-05-25 19:58:19.118090000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:338	指标流上下文取消，正在关闭...
2025-05-25 19:58:19.121095000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:286	所有采集器已停止.
2025-05-25 19:58:19.124310000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:251	停止接收任务配置，流已关闭或上下文已取消:	{"error": "rpc error: code = Canceled desc = context canceled"}
2025-05-25 19:58:26.594116000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 17, "dropped": 0}
2025-05-25 19:58:35.650034000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:225	健康检查失败，开始重连	{"error": "连接状态异常: IDLE"}
2025-05-25 19:58:35.651406000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Reconnecting", "timestamp": "2025-05-25 19:58:35.651388000"}
2025-05-25 19:58:35.651421000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Connected", "new_state": "Reconnecting"}
2025-05-25 19:58:35.755748000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 1, "server": "localhost:50051"}
2025-05-25 19:58:45.756832000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 1}
2025-05-25 19:58:46.759117000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 2, "server": "localhost:50051"}
2025-05-25 19:58:56.760045000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:309	重连失败，等待重试	{"error": "连接失败: context deadline exceeded", "backoff": 2}
2025-05-25 19:58:58.761936000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:302	尝试重连	{"attempt": 3, "server": "localhost:50051"}
2025-05-25 20:20:58.248413000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:40	Agent 启动中...
2025-05-25 20:20:58.393908000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent ID:	{"agent_id": "e5f30ce7-8f0b-46bb-90ac-d23d6073926f"}
2025-05-25 20:20:58.394039000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:50	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-25 20:20:58.394085000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-25 20:20:58.399178000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Connecting", "new_state": "Connected"}
2025-05-25 20:20:58.399225000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:136	连接已建立	{"server": "localhost:50051"}
2025-05-25 20:20:58.684487000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:86	Agent 注册成功
2025-05-25 20:20:58.684667000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:581	日志转发器正在启动...
2025-05-25 20:20:58.684755000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:298	指标流已连接
2025-05-25 20:20:58.684796000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:220	任务流已连接
2025-05-25 20:20:58.684879000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:360	日志流已连接
2025-05-25 20:20:58.684906000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "/var/log/system.log", "source": "system"}
2025-05-25 20:20:58.684796000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connecting", "timestamp": "2025-05-25 20:20:58.394084000"}
2025-05-25 20:20:58.684985000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-25 20:20:58.399177000"}
2025-05-25 20:20:58.685235000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent.log"}
2025-05-25 20:20:58.685260000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent_test.log"}
2025-05-25 20:20:58.685341000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/control_plane_test.log", "source": "agent"}
2025-05-25 20:20:58.685465000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/plane.log", "source": "agent"}
2025-05-25 20:20:58.685578000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:594	日志转发器启动完成	{"active_watchers": 3}
2025-05-25 20:21:13.686747000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 3, "dropped": 0}
2025-05-25 20:21:18.688715000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 4, "dropped": 0}
2025-05-25 20:26:40.233120000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:254	接收任务配置失败:	{"error": "rpc error: code = Unavailable desc = error reading from server: EOF"}
main.startTaskStream.func3
	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:254
2025-05-25 20:27:12.247042000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:40	Agent 启动中...
2025-05-25 20:27:12.256732000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent ID:	{"agent_id": "d0a9d1e7-3045-432f-9d06-636124456daa"}
2025-05-25 20:27:12.256756000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:50	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-25 20:27:12.256800000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-25 20:27:12.259834000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Connecting", "new_state": "Connected"}
2025-05-25 20:27:12.259864000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:136	连接已建立	{"server": "localhost:50051"}
2025-05-25 20:27:12.262114000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:86	Agent 注册成功
2025-05-25 20:27:12.262171000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:581	日志转发器正在启动...
2025-05-25 20:27:12.262302000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connecting", "timestamp": "2025-05-25 20:27:12.256799000"}
2025-05-25 20:27:12.262417000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-25 20:27:12.259833000"}
2025-05-25 20:27:12.262428000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:298	指标流已连接
2025-05-25 20:27:12.262480000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:360	日志流已连接
2025-05-25 20:27:12.262552000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:220	任务流已连接
2025-05-25 20:27:12.262673000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "/var/log/system.log", "source": "system"}
2025-05-25 20:27:12.262829000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent.log"}
2025-05-25 20:27:12.262851000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent_test.log"}
2025-05-25 20:27:12.262945000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/control_plane_test.log", "source": "agent"}
2025-05-25 20:27:12.263120000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/plane.log", "source": "agent"}
2025-05-25 20:27:12.263136000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:594	日志转发器启动完成	{"active_watchers": 3}
2025-05-25 20:27:27.265045000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 4, "dropped": 0}
2025-05-25 20:27:32.269162000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 5, "dropped": 0}
2025-05-25 20:32:32.394495000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 6, "dropped": 0}
2025-05-25 20:35:12.443828000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 7, "dropped": 0}
2025-05-25 20:38:52.493497000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 8, "dropped": 0}
2025-05-26 09:19:34.915098000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:40	Agent 启动中...
2025-05-26 09:19:34.920611000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent ID:	{"agent_id": "b421ec9c-5a3b-4b52-8193-0e42ba58f063"}
2025-05-26 09:19:34.920717000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:50	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-26 09:19:34.920771000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-26 09:19:34.925709000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Connecting", "new_state": "Connected"}
2025-05-26 09:19:34.925787000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:136	连接已建立	{"server": "localhost:50051"}
2025-05-26 09:19:34.950343000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:86	Agent 注册成功
2025-05-26 09:19:34.950510000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:581	日志转发器正在启动...
2025-05-26 09:19:34.950717000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:220	任务流已连接
2025-05-26 09:19:34.950721000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:298	指标流已连接
2025-05-26 09:19:34.950690000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connecting", "timestamp": "2025-05-26 09:19:34.920770000"}
2025-05-26 09:19:34.950813000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:360	日志流已连接
2025-05-26 09:19:34.950830000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-26 09:19:34.925707000"}
2025-05-26 09:19:34.951291000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "/var/log/system.log", "source": "system"}
2025-05-26 09:19:34.951616000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent.log"}
2025-05-26 09:19:34.951663000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent_test.log"}
2025-05-26 09:19:34.951876000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/control_plane_test.log", "source": "agent"}
2025-05-26 09:19:34.951979000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/plane.log", "source": "agent"}
2025-05-26 09:19:34.951993000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:594	日志转发器启动完成	{"active_watchers": 3}
2025-05-26 09:19:49.952608000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1, "dropped": 0}
2025-05-26 09:19:54.953656000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 2, "dropped": 0}
2025-05-26 09:20:54.961976000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 4, "dropped": 0}
2025-05-26 09:31:15.470641000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 7, "dropped": 0}
2025-05-26 09:33:25.896471000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 9, "dropped": 0}
2025-05-26 09:41:26.378288000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 12, "dropped": 0}
2025-05-26 09:48:06.638632000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 14, "dropped": 0}
2025-05-26 09:53:47.407029000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 15, "dropped": 0}
2025-05-26 09:58:27.873681000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 17, "dropped": 0}
2025-05-26 10:04:58.129663000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 18, "dropped": 0}
2025-05-26 10:08:48.285839000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 20, "dropped": 0}
2025-05-26 10:15:48.659317000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 23, "dropped": 0}
2025-05-26 10:17:48.804985000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 25, "dropped": 0}
2025-05-26 10:19:09.210144000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 27, "dropped": 0}
2025-05-26 10:56:39.773580000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:40	Agent 启动中...
2025-05-26 10:56:39.804907000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent ID:	{"agent_id": "bdaf92ef-438d-4798-9aa9-4eb62e979c37"}
2025-05-26 10:56:39.804972000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:50	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-26 10:56:39.805011000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-26 10:56:39.809799000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Connecting", "new_state": "Connected"}
2025-05-26 10:56:39.809865000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:136	连接已建立	{"server": "localhost:50051"}
2025-05-26 10:56:39.857531000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:86	Agent 注册成功
2025-05-26 10:56:39.857623000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:581	日志转发器正在启动...
2025-05-26 10:56:39.857745000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connecting", "timestamp": "2025-05-26 10:56:39.805010000"}
2025-05-26 10:56:39.857765000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:118	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-26 10:56:39.809797000"}
2025-05-26 10:56:39.857808000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:298	指标流已连接
2025-05-26 10:56:39.857729000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:220	任务流已连接
2025-05-26 10:56:39.857890000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "/var/log/system.log", "source": "system"}
2025-05-26 10:56:39.857876000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:360	日志流已连接
2025-05-26 10:56:39.858109000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent.log"}
2025-05-26 10:56:39.858136000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent_test.log"}
2025-05-26 10:56:39.858204000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/control_plane_test.log", "source": "agent"}
2025-05-26 10:56:39.858257000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/plane.log", "source": "agent"}
2025-05-26 10:56:39.858269000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:594	日志转发器启动完成	{"active_watchers": 3}
2025-05-26 10:56:54.859294000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 3, "dropped": 0}
2025-05-26 14:21:42.787899000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent 启动中...
2025-05-26 14:21:42.820190000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:42	Agent ID:	{"agent_id": "559575c1-863a-4566-8669-4be872f3697e"}
2025-05-26 14:21:42.820266000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:51	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-26 14:21:42.820294000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-26 14:21:43.004054000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Connecting", "new_state": "Connected"}
2025-05-26 14:21:43.004133000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:136	连接已建立	{"server": "localhost:50051"}
2025-05-26 14:21:43.268643000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:87	Agent 注册成功
2025-05-26 14:21:43.268919000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:581	日志转发器正在启动...
2025-05-26 14:21:43.269253000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:129	连接状态变更事件	{"state": "Connecting", "timestamp": "2025-05-26 14:21:42.820293000"}
2025-05-26 14:21:43.269497000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:129	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-26 14:21:43.004051000"}
2025-05-26 14:21:43.269371000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:320	指标流已连接
2025-05-26 14:21:43.269371000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:242	任务流已连接
2025-05-26 14:21:43.268973000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:436	启动插件更新流监听
2025-05-26 14:21:43.269465000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:382	日志流已连接
2025-05-26 14:21:43.269650000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:126	启动插件更新流监听
2025-05-26 14:21:43.270709000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "/var/log/system.log", "source": "system"}
2025-05-26 14:21:43.270856000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent.log"}
2025-05-26 14:21:43.270875000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent_test.log"}
2025-05-26 14:21:43.270947000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/control_plane_test.log", "source": "agent"}
2025-05-26 14:21:43.271018000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/plane.log", "source": "agent"}
2025-05-26 14:21:43.271028000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:594	日志转发器启动完成	{"active_watchers": 3}
2025-05-26 14:21:58.271367000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 4, "dropped": 0}
2025-05-26 14:22:03.272405000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 5, "dropped": 0}
2025-05-26 14:23:03.283714000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 7, "dropped": 0}
2025-05-26 14:26:03.712453000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 9, "dropped": 0}
2025-05-26 14:26:14.085256000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 11, "dropped": 0}
2025-05-26 14:27:54.155179000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 13, "dropped": 0}
2025-05-26 14:49:13.130013000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent 启动中...
2025-05-26 14:49:13.146650000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:42	Agent ID:	{"agent_id": "0088ab7b-0ecc-45fe-b1b4-9373401a3663"}
2025-05-26 14:49:13.146814000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:51	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-26 14:49:13.146948000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-26 14:49:13.261902000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Connecting", "new_state": "Connected"}
2025-05-26 14:49:13.261969000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:136	连接已建立	{"server": "localhost:50051"}
2025-05-26 14:49:13.413770000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:87	Agent 注册成功
2025-05-26 14:49:13.413921000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:465	开始初始化插件管理器
2025-05-26 14:49:13.414043000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:469	预加载插件	{"device_type": "system"}
2025-05-26 14:49:13.414090000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "system"}
2025-05-26 14:49:13.414164000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:43	请求插件	{"plugin_name": "system-collector", "version": "", "device_type": "system"}
2025-05-26 14:49:13.431875000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:108	从控制平面请求插件失败，尝试本地缓存	{"plugin_name": "system-collector", "error": "插件请求被拒绝: 插件不可用: 未找到兼容的插件: system-collector (device_type: system, arch: arm64, os: darwin)"}
2025-05-26 14:49:13.431969000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:118	尝试从本地缓存加载插件	{"device_type": "system"}
2025-05-26 14:49:13.432007000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:472	预加载插件失败，将使用内置采集器作为回退	{"device_type": "system", "error": "插件不存在且无法从控制平面获取: plugins/cache/system-collector.so"}
2025-05-26 14:49:13.432019000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:469	预加载插件	{"device_type": "mysql"}
2025-05-26 14:49:13.432027000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "mysql"}
2025-05-26 14:49:13.432036000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:43	请求插件	{"plugin_name": "mysql-collector", "version": "", "device_type": "mysql"}
2025-05-26 14:49:13.432834000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:108	从控制平面请求插件失败，尝试本地缓存	{"plugin_name": "mysql-collector", "error": "插件请求被拒绝: 插件不可用: 未找到兼容的插件: mysql-collector (device_type: mysql, arch: arm64, os: darwin)"}
2025-05-26 14:49:13.432868000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:118	尝试从本地缓存加载插件	{"device_type": "mysql"}
2025-05-26 14:49:13.432893000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:472	预加载插件失败，将使用内置采集器作为回退	{"device_type": "mysql", "error": "插件不存在且无法从控制平面获取: plugins/cache/mysql-collector.so"}
2025-05-26 14:49:13.432904000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:469	预加载插件	{"device_type": "redis"}
2025-05-26 14:49:13.432911000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "redis"}
2025-05-26 14:49:13.432919000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:43	请求插件	{"plugin_name": "redis-collector", "version": "", "device_type": "redis"}
2025-05-26 14:49:13.433570000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:108	从控制平面请求插件失败，尝试本地缓存	{"plugin_name": "redis-collector", "error": "插件请求被拒绝: 插件不可用: 未找到兼容的插件: redis-collector (device_type: redis, arch: arm64, os: darwin)"}
2025-05-26 14:49:13.433626000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:118	尝试从本地缓存加载插件	{"device_type": "redis"}
2025-05-26 14:49:13.433653000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:472	预加载插件失败，将使用内置采集器作为回退	{"device_type": "redis", "error": "插件不存在且无法从控制平面获取: plugins/cache/redis-collector.so"}
2025-05-26 14:49:13.433663000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:469	预加载插件	{"device_type": "docker"}
2025-05-26 14:49:13.433670000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "docker"}
2025-05-26 14:49:13.433679000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:43	请求插件	{"plugin_name": "docker-collector", "version": "", "device_type": "docker"}
2025-05-26 14:49:13.434103000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:108	从控制平面请求插件失败，尝试本地缓存	{"plugin_name": "docker-collector", "error": "插件请求被拒绝: 插件不可用: 未找到兼容的插件: docker-collector (device_type: docker, arch: arm64, os: darwin)"}
2025-05-26 14:49:13.434126000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:118	尝试从本地缓存加载插件	{"device_type": "docker"}
2025-05-26 14:49:13.434142000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:472	预加载插件失败，将使用内置采集器作为回退	{"device_type": "docker", "error": "插件不存在且无法从控制平面获取: plugins/cache/docker-collector.so"}
2025-05-26 14:49:13.434152000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:482	插件管理器初始化完成
2025-05-26 14:49:13.434200000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:581	日志转发器正在启动...
2025-05-26 14:49:13.434374000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Connecting", "timestamp": "2025-05-26 14:49:13.146947000"}
2025-05-26 14:49:13.434301000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:442	启动插件更新流监听
2025-05-26 14:49:13.434407000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:126	启动插件更新流监听
2025-05-26 14:49:13.434453000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:388	日志流已连接
2025-05-26 14:49:13.434469000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:326	指标流已连接
2025-05-26 14:49:13.434505000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "/var/log/system.log", "source": "system"}
2025-05-26 14:49:13.434392000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-26 14:49:13.261899000"}
2025-05-26 14:49:13.434521000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:248	任务流已连接
2025-05-26 14:49:13.434608000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent.log"}
2025-05-26 14:49:13.434630000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent_test.log"}
2025-05-26 14:49:13.434755000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/control_plane_test.log", "source": "agent"}
2025-05-26 14:49:13.434843000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/plane.log", "source": "agent"}
2025-05-26 14:49:13.434860000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:594	日志转发器启动完成	{"active_watchers": 3}
2025-05-26 14:49:28.437207000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 4, "dropped": 0}
2025-05-26 14:49:38.442002000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 5, "dropped": 0}
2025-05-26 14:49:38.460694000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:169	插件更新流错误:	{"error": "启动插件更新流失败: 接收插件更新通知失败: rpc error: code = Unavailable desc = error reading from server: read tcp [::1]:63032->[::1]:50051: read: connection reset by peer"}
main.main.func4
	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:169
2025-05-26 14:49:38.460676000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:282	接收任务配置失败:	{"error": "rpc error: code = Unavailable desc = error reading from server: read tcp [::1]:63032->[::1]:50051: read: connection reset by peer"}
main.startTaskStream.func3
	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:282
2025-05-26 15:06:36.110020000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent 启动中...
2025-05-26 15:06:36.568151000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:42	Agent ID:	{"agent_id": "4b083da0-ac23-4869-b265-870c3bd4e496"}
2025-05-26 15:06:36.568238000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:51	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-26 15:06:36.568290000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-26 15:06:37.111044000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Connecting", "new_state": "Connected"}
2025-05-26 15:06:37.111137000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:136	连接已建立	{"server": "localhost:50051"}
2025-05-26 15:06:37.235087000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:87	Agent 注册成功
2025-05-26 15:06:37.235158000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:465	开始初始化插件管理器
2025-05-26 15:06:37.235170000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:469	预加载插件	{"device_type": "system"}
2025-05-26 15:06:37.235180000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "system"}
2025-05-26 15:06:37.235192000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:43	请求插件	{"plugin_name": "system-collector", "version": "", "device_type": "system"}
2025-05-26 15:06:37.260400000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:108	从控制平面请求插件失败，尝试本地缓存	{"plugin_name": "system-collector", "error": "插件请求被拒绝: 加载插件文件失败: 插件文件不存在: plugins/storage/plugins/build/system-collector.so"}
2025-05-26 15:06:37.260477000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:118	尝试从本地缓存加载插件	{"device_type": "system"}
2025-05-26 15:06:37.260516000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:472	预加载插件失败，将使用内置采集器作为回退	{"device_type": "system", "error": "插件不存在且无法从控制平面获取: plugins/cache/system-collector.so"}
2025-05-26 15:06:37.260529000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:469	预加载插件	{"device_type": "mysql"}
2025-05-26 15:06:37.260538000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "mysql"}
2025-05-26 15:06:37.260548000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:43	请求插件	{"plugin_name": "mysql-collector", "version": "", "device_type": "mysql"}
2025-05-26 15:06:37.261184000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:108	从控制平面请求插件失败，尝试本地缓存	{"plugin_name": "mysql-collector", "error": "插件请求被拒绝: 加载插件文件失败: 插件文件不存在: plugins/storage/plugins/build/mysql-collector.so"}
2025-05-26 15:06:37.261314000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:118	尝试从本地缓存加载插件	{"device_type": "mysql"}
2025-05-26 15:06:37.261336000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:472	预加载插件失败，将使用内置采集器作为回退	{"device_type": "mysql", "error": "插件不存在且无法从控制平面获取: plugins/cache/mysql-collector.so"}
2025-05-26 15:06:37.261348000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:469	预加载插件	{"device_type": "redis"}
2025-05-26 15:06:37.261356000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "redis"}
2025-05-26 15:06:37.261366000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:43	请求插件	{"plugin_name": "redis-collector", "version": "", "device_type": "redis"}
2025-05-26 15:06:37.261782000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:108	从控制平面请求插件失败，尝试本地缓存	{"plugin_name": "redis-collector", "error": "插件请求被拒绝: 插件不可用: 未找到兼容的插件: redis-collector (device_type: redis, arch: arm64, os: darwin)"}
2025-05-26 15:06:37.261807000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:118	尝试从本地缓存加载插件	{"device_type": "redis"}
2025-05-26 15:06:37.261921000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:472	预加载插件失败，将使用内置采集器作为回退	{"device_type": "redis", "error": "插件不存在且无法从控制平面获取: plugins/cache/redis-collector.so"}
2025-05-26 15:06:37.261942000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:469	预加载插件	{"device_type": "docker"}
2025-05-26 15:06:37.261953000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "docker"}
2025-05-26 15:06:37.261962000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:43	请求插件	{"plugin_name": "docker-collector", "version": "", "device_type": "docker"}
2025-05-26 15:06:37.262288000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:108	从控制平面请求插件失败，尝试本地缓存	{"plugin_name": "docker-collector", "error": "插件请求被拒绝: 插件不可用: 未找到兼容的插件: docker-collector (device_type: docker, arch: arm64, os: darwin)"}
2025-05-26 15:06:37.262313000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:118	尝试从本地缓存加载插件	{"device_type": "docker"}
2025-05-26 15:06:37.262328000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:472	预加载插件失败，将使用内置采集器作为回退	{"device_type": "docker", "error": "插件不存在且无法从控制平面获取: plugins/cache/docker-collector.so"}
2025-05-26 15:06:37.262338000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:482	插件管理器初始化完成
2025-05-26 15:06:37.262436000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Connecting", "timestamp": "2025-05-26 15:06:36.568289000"}
2025-05-26 15:06:37.262468000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-26 15:06:37.111041000"}
2025-05-26 15:06:37.262458000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:442	启动插件更新流监听
2025-05-26 15:06:37.262488000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:126	启动插件更新流监听
2025-05-26 15:06:37.262540000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:581	日志转发器正在启动...
2025-05-26 15:06:37.262593000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:326	指标流已连接
2025-05-26 15:06:37.262700000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:248	任务流已连接
2025-05-26 15:06:37.262715000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:388	日志流已连接
2025-05-26 15:06:37.262958000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "/var/log/system.log", "source": "system"}
2025-05-26 15:06:37.263074000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent.log"}
2025-05-26 15:06:37.263103000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent_test.log"}
2025-05-26 15:06:37.263188000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/control_plane_test.log", "source": "agent"}
2025-05-26 15:06:37.263631000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/plane.log", "source": "agent"}
2025-05-26 15:06:37.263675000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:594	日志转发器启动完成	{"active_watchers": 3}
2025-05-26 15:06:52.264970000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 4, "dropped": 0}
2025-05-26 15:06:57.267842000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 5, "dropped": 0}
2025-05-26 15:27:22.268038000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent 启动中...
2025-05-26 15:27:22.269311000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:42	Agent ID:	{"agent_id": "28738140-0f0d-43d7-af08-84d96dabb9f3"}
2025-05-26 15:27:22.269328000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:51	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-26 15:27:22.269363000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-26 15:27:22.272650000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Connecting", "new_state": "Connected"}
2025-05-26 15:27:22.272703000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:136	连接已建立	{"server": "localhost:50051"}
2025-05-26 15:27:22.536894000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:87	Agent 注册成功
2025-05-26 15:27:22.536942000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:465	开始初始化插件管理器
2025-05-26 15:27:22.536954000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:469	预加载插件	{"device_type": "system"}
2025-05-26 15:27:22.536963000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "system"}
2025-05-26 15:27:22.536973000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:43	请求插件	{"plugin_name": "system-collector", "version": "", "device_type": "system"}
2025-05-26 15:27:22.537638000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:108	从控制平面请求插件失败，尝试本地缓存	{"plugin_name": "system-collector", "error": "插件请求被拒绝: 加载插件文件失败: 插件文件不存在: plugins/storage/plugins/build/system-collector.so"}
2025-05-26 15:27:22.537661000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:118	尝试从本地缓存加载插件	{"device_type": "system"}
2025-05-26 15:27:22.537679000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:472	预加载插件失败，将使用内置采集器作为回退	{"device_type": "system", "error": "插件不存在且无法从控制平面获取: plugins/cache/system-collector.so"}
2025-05-26 15:27:22.537690000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:469	预加载插件	{"device_type": "mysql"}
2025-05-26 15:27:22.537698000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "mysql"}
2025-05-26 15:27:22.537706000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:43	请求插件	{"plugin_name": "mysql-collector", "version": "", "device_type": "mysql"}
2025-05-26 15:27:22.538159000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:108	从控制平面请求插件失败，尝试本地缓存	{"plugin_name": "mysql-collector", "error": "插件请求被拒绝: 加载插件文件失败: 插件文件不存在: plugins/storage/plugins/build/mysql-collector.so"}
2025-05-26 15:27:22.538195000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:118	尝试从本地缓存加载插件	{"device_type": "mysql"}
2025-05-26 15:27:22.538230000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:472	预加载插件失败，将使用内置采集器作为回退	{"device_type": "mysql", "error": "插件不存在且无法从控制平面获取: plugins/cache/mysql-collector.so"}
2025-05-26 15:27:22.538241000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:469	预加载插件	{"device_type": "email"}
2025-05-26 15:27:22.538249000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "email"}
2025-05-26 15:27:22.538257000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:43	请求插件	{"plugin_name": "email-collector", "version": "", "device_type": "email"}
2025-05-26 15:27:22.538516000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:108	从控制平面请求插件失败，尝试本地缓存	{"plugin_name": "email-collector", "error": "插件请求被拒绝: 插件不可用: 未找到兼容的插件: email-collector (device_type: email, arch: arm64, os: darwin)"}
2025-05-26 15:27:22.538555000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:118	尝试从本地缓存加载插件	{"device_type": "email"}
2025-05-26 15:27:22.538570000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:472	预加载插件失败，将使用内置采集器作为回退	{"device_type": "email", "error": "插件不存在且无法从控制平面获取: plugins/cache/email-collector.so"}
2025-05-26 15:27:22.538579000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:469	预加载插件	{"device_type": "simple"}
2025-05-26 15:27:22.538586000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "simple"}
2025-05-26 15:27:22.538593000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:43	请求插件	{"plugin_name": "simple-collector", "version": "", "device_type": "simple"}
2025-05-26 15:27:22.538969000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:108	从控制平面请求插件失败，尝试本地缓存	{"plugin_name": "simple-collector", "error": "插件请求被拒绝: 插件不可用: 未找到兼容的插件: simple-collector (device_type: simple, arch: arm64, os: darwin)"}
2025-05-26 15:27:22.538988000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:118	尝试从本地缓存加载插件	{"device_type": "simple"}
2025-05-26 15:27:22.539001000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:472	预加载插件失败，将使用内置采集器作为回退	{"device_type": "simple", "error": "插件不存在且无法从控制平面获取: plugins/cache/simple-collector.so"}
2025-05-26 15:27:22.539010000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:482	插件管理器初始化完成
2025-05-26 15:27:22.539351000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Connecting", "timestamp": "2025-05-26 15:27:22.269363000"}
2025-05-26 15:27:22.539408000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-26 15:27:22.272648000"}
2025-05-26 15:27:22.539438000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:442	启动插件更新流监听
2025-05-26 15:27:22.539449000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:581	日志转发器正在启动...
2025-05-26 15:27:22.539485000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:126	启动插件更新流监听
2025-05-26 15:27:22.539689000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:388	日志流已连接
2025-05-26 15:27:22.539662000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:326	指标流已连接
2025-05-26 15:27:22.539746000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:248	任务流已连接
2025-05-26 15:27:22.539984000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "/var/log/system.log", "source": "system"}
2025-05-26 15:27:22.540337000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent.log"}
2025-05-26 15:27:22.540358000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent_test.log"}
2025-05-26 15:27:22.540433000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/control_plane_test.log", "source": "agent"}
2025-05-26 15:27:22.540506000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/plane.log", "source": "agent"}
2025-05-26 15:27:22.540519000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:594	日志转发器启动完成	{"active_watchers": 3}
2025-05-26 15:27:37.541334000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 2, "dropped": 0}
2025-05-26 15:27:42.541672000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 4, "dropped": 0}
2025-05-26 15:38:19.693496000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent 启动中...
2025-05-26 15:38:19.697816000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:42	Agent ID:	{"agent_id": "158b853c-b9ea-482f-a21d-c0a50a269416"}
2025-05-26 15:38:19.697839000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:51	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-26 15:38:19.697890000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-26 15:38:19.701098000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:208	连接状态变更	{"old_state": "Connecting", "new_state": "Connected"}
2025-05-26 15:38:19.701150000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:136	连接已建立	{"server": "localhost:50051"}
2025-05-26 15:38:19.709724000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:87	Agent 注册成功
2025-05-26 15:38:19.709774000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:465	开始初始化插件管理器
2025-05-26 15:38:19.709785000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:469	预加载插件	{"device_type": "system"}
2025-05-26 15:38:19.709795000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "system"}
2025-05-26 15:38:19.709804000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:43	请求插件	{"plugin_name": "system-collector", "version": "", "device_type": "system"}
2025-05-26 15:38:19.840549000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:108	从控制平面请求插件失败，尝试本地缓存	{"plugin_name": "system-collector", "error": "请求插件失败: rpc error: code = ResourceExhausted desc = grpc: received message larger than max (12021828 vs. 4194304)"}
2025-05-26 15:38:19.840606000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:118	尝试从本地缓存加载插件	{"device_type": "system"}
2025-05-26 15:38:19.840635000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:472	预加载插件失败，将使用内置采集器作为回退	{"device_type": "system", "error": "插件不存在且无法从控制平面获取: plugins/cache/system-collector.so"}
2025-05-26 15:38:19.840649000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:469	预加载插件	{"device_type": "mysql"}
2025-05-26 15:38:19.840658000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "mysql"}
2025-05-26 15:38:19.840666000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:43	请求插件	{"plugin_name": "mysql-collector", "version": "", "device_type": "mysql"}
2025-05-26 15:38:19.977625000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:108	从控制平面请求插件失败，尝试本地缓存	{"plugin_name": "mysql-collector", "error": "请求插件失败: rpc error: code = ResourceExhausted desc = grpc: received message larger than max (12041905 vs. 4194304)"}
2025-05-26 15:38:19.977689000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:118	尝试从本地缓存加载插件	{"device_type": "mysql"}
2025-05-26 15:38:19.977720000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:472	预加载插件失败，将使用内置采集器作为回退	{"device_type": "mysql", "error": "插件不存在且无法从控制平面获取: plugins/cache/mysql-collector.so"}
2025-05-26 15:38:19.977732000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:482	插件管理器初始化完成
2025-05-26 15:38:19.978094000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Connecting", "timestamp": "2025-05-26 15:38:19.697890000"}
2025-05-26 15:38:19.978334000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-26 15:38:19.701096000"}
2025-05-26 15:38:19.978357000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:326	指标流已连接
2025-05-26 15:38:19.978181000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:581	日志转发器正在启动...
2025-05-26 15:38:19.978325000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:248	任务流已连接
2025-05-26 15:38:19.978359000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:388	日志流已连接
2025-05-26 15:38:19.978155000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:442	启动插件更新流监听
2025-05-26 15:38:19.978582000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:126	启动插件更新流监听
2025-05-26 15:38:19.979715000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "/var/log/system.log", "source": "system"}
2025-05-26 15:38:19.979826000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent.log"}
2025-05-26 15:38:19.979854000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent_test.log"}
2025-05-26 15:38:19.979948000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/control_plane_test.log", "source": "agent"}
2025-05-26 15:38:19.980014000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/plane.log", "source": "agent"}
2025-05-26 15:38:19.980025000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:594	日志转发器启动完成	{"active_watchers": 3}
2025-05-26 15:38:34.980037000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1, "dropped": 0}
2025-05-26 15:38:39.980524000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 2, "dropped": 0}
2025-05-26 15:39:19.986409000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 3, "dropped": 0}
2025-05-26 16:03:03.866598000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent 启动中...
2025-05-26 16:03:03.870915000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:42	Agent ID:	{"agent_id": "73aa5d48-ce11-42c4-ac1a-cb3caa989c3e"}
2025-05-26 16:03:03.870943000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:51	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-26 16:03:03.870980000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-26 16:03:13.871953000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Connecting", "new_state": "Disconnected"}
2025-05-26 16:03:13.872925000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:68	连接控制平面失败:	{"error": "创建连接失败: 连接超时"}
main.main
	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:68
runtime.main
	/usr/local/go/src/runtime/proc.go:283
2025-05-26 16:07:37.743390000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent 启动中...
2025-05-26 16:07:37.747328000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:42	Agent ID:	{"agent_id": "ccb7ef36-84f1-43d5-831a-e94baa12202c"}
2025-05-26 16:07:37.747349000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:51	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-26 16:07:37.747390000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:241	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-26 16:07:47.747908000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:241	连接状态变更	{"old_state": "Connecting", "new_state": "Disconnected"}
2025-05-26 16:07:47.748053000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:68	连接控制平面失败:	{"error": "创建连接失败: 等待连接就绪超时"}
main.main
	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:68
runtime.main
	/usr/local/go/src/runtime/proc.go:283
2025-05-26 16:14:52.827901000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent 启动中...
2025-05-26 16:14:52.832398000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:42	Agent ID:	{"agent_id": "e09b4005-653d-4455-a89b-f46eaf9882f2"}
2025-05-26 16:14:52.832444000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:51	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-26 16:14:52.832475000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-26 16:14:52.836708000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Connecting", "new_state": "Connected"}
2025-05-26 16:14:52.836761000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:136	连接已建立	{"server": "localhost:50051"}
2025-05-26 16:14:52.854239000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:87	Agent 注册成功
2025-05-26 16:14:52.854306000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:465	开始初始化插件管理器
2025-05-26 16:14:52.854319000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:469	预加载插件	{"device_type": "system"}
2025-05-26 16:14:52.854329000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "system"}
2025-05-26 16:14:52.854339000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:42	请求插件	{"plugin_name": "system-collector", "version": "", "device_type": "system"}
2025-05-26 16:14:53.095256000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:219	插件保存成功	{"path": "plugins/cache/system-collector-1.0.0.so", "size": 12021554}
2025-05-26 16:14:53.095339000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:133	从二进制数据加载插件	{"device_type": "system", "plugin_name": "system-collector", "plugin_version": "1.0.0", "plugin_path": "plugins/cache/system-collector-1.0.0.so"}
2025-05-26 16:14:53.136763000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:214	插件校验和	{"path": "plugins/cache/system-collector-1.0.0.so", "checksum": "57bc07fd3f74a35f3e68af5edd67044c9d94403ea89997ee6b53dc16549f4f6c"}
2025-05-26 16:14:53.450938000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:472	预加载插件失败，将使用内置采集器作为回退	{"device_type": "system", "error": "打开插件失败: plugin.Open(\"plugins/cache/system-collector-1.0.0\"): plugin was built with a different version of package internal/abi"}
2025-05-26 16:14:53.451017000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:469	预加载插件	{"device_type": "mysql"}
2025-05-26 16:14:53.451038000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "mysql"}
2025-05-26 16:14:53.451056000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:42	请求插件	{"plugin_name": "mysql-collector", "version": "", "device_type": "mysql"}
2025-05-26 16:14:53.660456000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:219	插件保存成功	{"path": "plugins/cache/mysql-collector-1.0.0.so", "size": 12041634}
2025-05-26 16:14:53.660509000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:133	从二进制数据加载插件	{"device_type": "mysql", "plugin_name": "mysql-collector", "plugin_version": "1.0.0", "plugin_path": "plugins/cache/mysql-collector-1.0.0.so"}
2025-05-26 16:14:53.703719000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:214	插件校验和	{"path": "plugins/cache/mysql-collector-1.0.0.so", "checksum": "b5d4d0a231d2cf2ba09c5cdebd90c090e58a026c33491a55eb73ec99ae50dff6"}
2025-05-26 16:14:54.016715000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:472	预加载插件失败，将使用内置采集器作为回退	{"device_type": "mysql", "error": "打开插件失败: plugin.Open(\"plugins/cache/mysql-collector-1.0.0\"): plugin was built with a different version of package internal/abi"}
2025-05-26 16:14:54.016801000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:482	插件管理器初始化完成
2025-05-26 16:14:54.016930000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:581	日志转发器正在启动...
2025-05-26 16:14:54.017036000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:442	启动插件更新流监听
2025-05-26 16:14:54.017156000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Connecting", "timestamp": "2025-05-26 16:14:52.832474000"}
2025-05-26 16:14:54.017206000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:125	启动插件更新流监听
2025-05-26 16:14:54.017227000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-26 16:14:52.836707000"}
2025-05-26 16:14:54.017302000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "/var/log/system.log", "source": "system"}
2025-05-26 16:14:54.017296000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:326	指标流已连接
2025-05-26 16:14:54.017290000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:248	任务流已连接
2025-05-26 16:14:54.017292000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:388	日志流已连接
2025-05-26 16:14:54.017459000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent.log"}
2025-05-26 16:14:54.017491000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent_test.log"}
2025-05-26 16:14:54.017654000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/control_plane_test.log", "source": "agent"}
2025-05-26 16:14:54.017792000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/plane.log", "source": "agent"}
2025-05-26 16:14:54.017816000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:594	日志转发器启动完成	{"active_watchers": 3}
2025-05-26 16:15:09.018725000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 4, "dropped": 0}
2025-05-26 16:15:14.019257000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 5, "dropped": 0}
2025-05-26 17:06:38.666810000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent 启动中...
2025-05-26 17:06:38.679828000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:42	Agent ID:	{"agent_id": "052a304e-0a1c-473f-bc2e-f80bd94c60aa"}
2025-05-26 17:06:38.679903000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:51	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-26 17:06:38.679967000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-26 17:06:38.687794000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Connecting", "new_state": "Connected"}
2025-05-26 17:06:38.687894000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:136	连接已建立	{"server": "localhost:50051"}
2025-05-26 17:06:38.715375000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:87	Agent 注册成功
2025-05-26 17:06:38.715464000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:465	开始初始化插件管理器
2025-05-26 17:06:38.715481000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:469	预加载插件	{"device_type": "system"}
2025-05-26 17:06:38.715493000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "system"}
2025-05-26 17:06:38.715506000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:41	请求插件	{"plugin_name": "system-collector", "version": "", "device_type": "system"}
2025-05-26 17:06:38.716619000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:108	从控制平面请求插件失败，尝试本地缓存	{"plugin_name": "system-collector", "error": "插件请求被拒绝: 插件文件验证失败: 插件文件不存在: plugins/storage/plugins/build/system-collector.so"}
2025-05-26 17:06:38.716695000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:118	尝试从本地缓存加载插件	{"device_type": "system"}
2025-05-26 17:06:38.716724000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:472	预加载插件失败，将使用内置采集器作为回退	{"device_type": "system", "error": "插件不存在且无法从控制平面获取: plugins/cache/system-collector.so"}
2025-05-26 17:06:38.716737000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:469	预加载插件	{"device_type": "mysql"}
2025-05-26 17:06:38.716746000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "mysql"}
2025-05-26 17:06:38.716756000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:41	请求插件	{"plugin_name": "mysql-collector", "version": "", "device_type": "mysql"}
2025-05-26 17:06:38.717337000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:108	从控制平面请求插件失败，尝试本地缓存	{"plugin_name": "mysql-collector", "error": "插件请求被拒绝: 插件文件验证失败: 插件文件不存在: plugins/storage/plugins/build/mysql-collector.so"}
2025-05-26 17:06:38.717387000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:118	尝试从本地缓存加载插件	{"device_type": "mysql"}
2025-05-26 17:06:38.717411000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:472	预加载插件失败，将使用内置采集器作为回退	{"device_type": "mysql", "error": "插件不存在且无法从控制平面获取: plugins/cache/mysql-collector.so"}
2025-05-26 17:06:38.717496000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:482	插件管理器初始化完成
2025-05-26 17:06:38.717723000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Connecting", "timestamp": "2025-05-26 17:06:38.679966000"}
2025-05-26 17:06:38.717821000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-26 17:06:38.687791000"}
2025-05-26 17:06:38.717836000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:388	日志流已连接
2025-05-26 17:06:38.717786000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:326	指标流已连接
2025-05-26 17:06:38.717682000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:442	启动插件更新流监听
2025-05-26 17:06:38.717886000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:133	启动插件更新流监听
2025-05-26 17:06:38.717865000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:581	日志转发器正在启动...
2025-05-26 17:06:38.717918000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:248	任务流已连接
2025-05-26 17:06:38.718173000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "/var/log/system.log", "source": "system"}
2025-05-26 17:06:38.718384000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent.log"}
2025-05-26 17:06:38.718440000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent_test.log"}
2025-05-26 17:06:38.718517000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/control_plane_test.log", "source": "agent"}
2025-05-26 17:06:38.718720000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/plane.log", "source": "agent"}
2025-05-26 17:06:38.718736000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:594	日志转发器启动完成	{"active_watchers": 3}
2025-05-26 17:06:53.719897000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 2, "dropped": 0}
2025-05-26 17:06:58.817416000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 3, "dropped": 0}
2025-05-26 17:07:07.160593000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:169	插件更新流错误:	{"error": "启动插件更新流失败: 接收插件更新通知失败: rpc error: code = Unavailable desc = error reading from server: EOF"}
main.main.func4
	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:169
2025-05-26 17:07:07.160585000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:282	接收任务配置失败:	{"error": "rpc error: code = Unavailable desc = error reading from server: EOF"}
main.startTaskStream.func3
	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:282
2025-05-26 17:10:01.777460000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent 启动中...
2025-05-26 17:10:01.812501000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:42	Agent ID:	{"agent_id": "a02537b9-0ced-450e-b1cd-2e6480a50aae"}
2025-05-26 17:10:01.812556000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:51	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-26 17:10:01.812589000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-26 17:10:01.815588000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Connecting", "new_state": "Connected"}
2025-05-26 17:10:01.815664000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:136	连接已建立	{"server": "localhost:50051"}
2025-05-26 17:10:01.935749000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:87	Agent 注册成功
2025-05-26 17:10:01.935812000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:465	开始初始化插件管理器
2025-05-26 17:10:01.935825000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:469	预加载插件	{"device_type": "system"}
2025-05-26 17:10:01.935836000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "system"}
2025-05-26 17:10:01.935849000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:41	请求插件	{"plugin_name": "system-collector", "version": "", "device_type": "system"}
2025-05-26 17:10:01.936405000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:108	从控制平面请求插件失败，尝试本地缓存	{"plugin_name": "system-collector", "error": "插件请求被拒绝: 插件文件验证失败: 插件文件不存在: plugins/storage/plugins/build/system-collector.so"}
2025-05-26 17:10:01.936428000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:118	尝试从本地缓存加载插件	{"device_type": "system"}
2025-05-26 17:10:01.936444000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:472	预加载插件失败，将使用内置采集器作为回退	{"device_type": "system", "error": "插件不存在且无法从控制平面获取: plugins/cache/system-collector.so"}
2025-05-26 17:10:01.936454000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:469	预加载插件	{"device_type": "mysql"}
2025-05-26 17:10:01.936462000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "mysql"}
2025-05-26 17:10:01.936470000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:41	请求插件	{"plugin_name": "mysql-collector", "version": "", "device_type": "mysql"}
2025-05-26 17:10:01.936896000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:108	从控制平面请求插件失败，尝试本地缓存	{"plugin_name": "mysql-collector", "error": "插件请求被拒绝: 插件文件验证失败: 插件文件不存在: plugins/storage/plugins/build/mysql-collector.so"}
2025-05-26 17:10:01.936968000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:118	尝试从本地缓存加载插件	{"device_type": "mysql"}
2025-05-26 17:10:01.936990000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:472	预加载插件失败，将使用内置采集器作为回退	{"device_type": "mysql", "error": "插件不存在且无法从控制平面获取: plugins/cache/mysql-collector.so"}
2025-05-26 17:10:01.937000000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:482	插件管理器初始化完成
2025-05-26 17:10:01.937455000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:581	日志转发器正在启动...
2025-05-26 17:10:01.937571000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:248	任务流已连接
2025-05-26 17:10:01.937650000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:388	日志流已连接
2025-05-26 17:10:01.937711000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:326	指标流已连接
2025-05-26 17:10:01.937446000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:442	启动插件更新流监听
2025-05-26 17:10:01.937824000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:133	启动插件更新流监听
2025-05-26 17:10:01.937299000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Connecting", "timestamp": "2025-05-26 17:10:01.812588000"}
2025-05-26 17:10:01.937873000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-26 17:10:01.815587000"}
2025-05-26 17:10:01.938286000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "/var/log/system.log", "source": "system"}
2025-05-26 17:10:01.938534000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent.log"}
2025-05-26 17:10:01.938554000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent_test.log"}
2025-05-26 17:10:01.938687000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/control_plane_test.log", "source": "agent"}
2025-05-26 17:10:01.938775000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/plane.log", "source": "agent"}
2025-05-26 17:10:01.938804000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:594	日志转发器启动完成	{"active_watchers": 3}
2025-05-26 17:10:16.939424000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 3, "dropped": 0}
2025-05-26 17:10:21.942016000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 4, "dropped": 0}
2025-05-26 17:25:52.572973000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 5, "dropped": 0}
2025-05-26 17:30:02.876422000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 35, "dropped": 0}
2025-05-26 17:30:18.152110000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent 启动中...
2025-05-26 17:30:18.161874000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:42	Agent ID:	{"agent_id": "ac29c7b4-f8eb-4b05-b9b4-b857f81e1225"}
2025-05-26 17:30:18.162032000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:51	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-26 17:30:18.162100000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-26 17:30:18.165137000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Connecting", "new_state": "Disconnected"}
2025-05-26 17:30:18.165293000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:68	连接控制平面失败:	{"error": "创建连接失败: 连接失败，状态: TRANSIENT_FAILURE"}
main.main
	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:68
runtime.main
	/usr/local/go/src/runtime/proc.go:283
2025-05-26 18:25:04.366980000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent 启动中...
2025-05-26 18:25:04.396313000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:42	Agent ID:	{"agent_id": "099a0a42-6341-47f7-9830-e4d99f0a87f7"}
2025-05-26 18:25:04.396431000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:51	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-26 18:25:04.396452000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-26 18:25:04.401951000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Connecting", "new_state": "Connected"}
2025-05-26 18:25:04.402016000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:136	连接已建立	{"server": "localhost:50051"}
2025-05-26 18:25:04.424637000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:87	Agent 注册成功
2025-05-26 18:25:04.424709000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:509	开始初始化插件管理器
2025-05-26 18:25:04.424721000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:513	预加载插件	{"device_type": "system"}
2025-05-26 18:25:04.424731000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "system"}
2025-05-26 18:25:04.424741000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:41	请求插件	{"plugin_name": "system-collector", "version": "", "device_type": "system"}
2025-05-26 18:25:04.425728000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:108	从控制平面请求插件失败，尝试本地缓存	{"plugin_name": "system-collector", "error": "插件请求被拒绝: 插件文件验证失败: 插件文件不存在: plugins/build/plugins/build/system-collector-1.0.0.so"}
2025-05-26 18:25:04.425776000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:118	尝试从本地缓存加载插件	{"device_type": "system"}
2025-05-26 18:25:04.425799000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:516	预加载插件失败，将使用内置采集器作为回退	{"device_type": "system", "error": "插件不存在且无法从控制平面获取: plugins/cache/system-collector.so"}
2025-05-26 18:25:04.425811000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:513	预加载插件	{"device_type": "mysql"}
2025-05-26 18:25:04.425819000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "mysql"}
2025-05-26 18:25:04.425827000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:41	请求插件	{"plugin_name": "mysql-collector", "version": "", "device_type": "mysql"}
2025-05-26 18:25:04.426840000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:108	从控制平面请求插件失败，尝试本地缓存	{"plugin_name": "mysql-collector", "error": "插件请求被拒绝: 插件文件验证失败: 插件文件不存在: plugins/build/plugins/build/mysql-collector-1.0.0.so"}
2025-05-26 18:25:04.426904000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:118	尝试从本地缓存加载插件	{"device_type": "mysql"}
2025-05-26 18:25:04.426937000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:516	预加载插件失败，将使用内置采集器作为回退	{"device_type": "mysql", "error": "插件不存在且无法从控制平面获取: plugins/cache/mysql-collector.so"}
2025-05-26 18:25:04.426950000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:526	插件管理器初始化完成
2025-05-26 18:25:04.427119000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:486	启动插件更新流监听
2025-05-26 18:25:04.427148000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:133	启动插件更新流监听
2025-05-26 18:25:04.427317000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:370	指标流已连接
2025-05-26 18:25:04.427092000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Connecting", "timestamp": "2025-05-26 18:25:04.396451000"}
2025-05-26 18:25:04.427383000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-26 18:25:04.401948000"}
2025-05-26 18:25:04.427338000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:581	日志转发器正在启动...
2025-05-26 18:25:04.427409000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:248	任务流已连接
2025-05-26 18:25:04.427772000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:432	日志流已连接
2025-05-26 18:25:04.427812000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:174	Task manager started	{"worker_count": 5}
2025-05-26 18:25:04.427828000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 3}
2025-05-26 18:25:04.427847000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 1}
2025-05-26 18:25:04.427863000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 2}
2025-05-26 18:25:04.427871000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_processors.go:174	Analysis result processor started
2025-05-26 18:25:04.427820000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 0}
2025-05-26 18:25:04.427925000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 4}
2025-05-26 18:25:04.427990000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_processors.go:16	Metric data processor started
2025-05-26 18:25:04.428795000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "/var/log/system.log", "source": "system"}
2025-05-26 18:25:04.428958000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent.log"}
2025-05-26 18:25:04.428976000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent_test.log"}
2025-05-26 18:25:04.429511000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/control_plane_test.log", "source": "agent"}
2025-05-26 18:25:04.429717000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/plane.log", "source": "agent"}
2025-05-26 18:25:04.429732000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:594	日志转发器启动完成	{"active_watchers": 3}
2025-05-26 18:25:19.429452000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 1, "dropped": 0}
2025-05-26 18:25:24.430715000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 2, "dropped": 0}
2025-05-26 18:26:06.442845000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent 启动中...
2025-05-26 18:26:06.443146000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:42	Agent ID:	{"agent_id": "05cbe938-21b6-4d80-84d1-733e0104b532"}
2025-05-26 18:26:06.443160000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:51	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-26 18:26:06.443172000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-26 18:26:06.446508000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Connecting", "new_state": "Connected"}
2025-05-26 18:26:06.446581000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:136	连接已建立	{"server": "localhost:50051"}
2025-05-26 18:26:06.517754000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:87	Agent 注册成功
2025-05-26 18:26:06.517874000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:509	开始初始化插件管理器
2025-05-26 18:26:06.517889000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:513	预加载插件	{"device_type": "system"}
2025-05-26 18:26:06.517899000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "system"}
2025-05-26 18:26:17.864412000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:41	请求插件	{"plugin_name": "system-collector", "version": "", "device_type": "system"}
2025-05-26 18:36:57.212899000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent 启动中...
2025-05-26 18:36:57.220514000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:42	Agent ID:	{"agent_id": "93c6d27e-9d57-463c-a099-6871c74f169d"}
2025-05-26 18:36:57.220547000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:51	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-26 18:36:57.220559000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-26 18:36:57.226424000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Connecting", "new_state": "Connected"}
2025-05-26 18:36:57.226483000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:136	连接已建立	{"server": "localhost:50051"}
2025-05-26 18:36:57.227859000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:87	Agent 注册成功
2025-05-26 18:36:57.227882000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:509	开始初始化插件管理器
2025-05-26 18:36:57.227897000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:513	预加载插件	{"device_type": "system"}
2025-05-26 18:36:57.227918000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "system"}
2025-05-26 18:36:57.227938000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:41	请求插件	{"plugin_name": "system-collector", "version": "", "device_type": "system"}
2025-05-26 18:36:57.270803000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:133	从插件路径加载插件	{"device_type": "system", "plugin_name": "system-collector", "plugin_version": "1.0.0", "plugin_path": "plugins/build/system-collector-1.0.0.so", "absolute_path": "plugins/build/system-collector-1.0.0.so"}
2025-05-26 18:36:57.277735000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:221	插件校验和	{"path": "plugins/build/system-collector-1.0.0.so", "checksum": "ad255f3f9c60db3d7b42c512076a51300fd50c485f1a4929199b20a8be465367"}
2025-05-26 18:36:57.787842000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:516	预加载插件失败，将使用内置采集器作为回退	{"device_type": "system", "error": "查找Plugin符号失败: plugin: symbol Plugin not found in plugin plugin/unnamed-d7b104d47bb5065ae93c675806ac37c5e52c28d7"}
2025-05-26 18:36:57.787947000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:513	预加载插件	{"device_type": "mysql"}
2025-05-26 18:36:57.787961000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "mysql"}
2025-05-26 18:36:57.787977000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:41	请求插件	{"plugin_name": "mysql-collector", "version": "", "device_type": "mysql"}
2025-05-26 18:36:57.842871000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:133	从插件路径加载插件	{"device_type": "mysql", "plugin_name": "mysql-collector", "plugin_version": "1.0.0", "plugin_path": "plugins/build/mysql-collector-1.0.0.so", "absolute_path": "plugins/build/mysql-collector-1.0.0.so"}
2025-05-26 18:36:57.850747000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:221	插件校验和	{"path": "plugins/build/mysql-collector-1.0.0.so", "checksum": "52bc04503c34dcbc88c163390a617fc4c3280d64ca40acbcd8b01ae061e6a0db"}
2025-05-26 18:36:58.270719000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:516	预加载插件失败，将使用内置采集器作为回退	{"device_type": "mysql", "error": "查找Plugin符号失败: plugin: symbol Plugin not found in plugin plugin/unnamed-41d949cce9b3a0ff5da506aed461008e6b9d8ff3"}
2025-05-26 18:36:58.270799000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:526	插件管理器初始化完成
2025-05-26 18:36:58.270876000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Connecting", "timestamp": "2025-05-26 18:36:57.220559000"}
2025-05-26 18:36:58.270927000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-26 18:36:57.226423000"}
2025-05-26 18:36:58.270918000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:486	启动插件更新流监听
2025-05-26 18:36:58.270971000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:133	启动插件更新流监听
2025-05-26 18:36:58.270988000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:581	日志转发器正在启动...
2025-05-26 18:36:58.271434000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:432	日志流已连接
2025-05-26 18:36:58.271587000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:370	指标流已连接
2025-05-26 18:36:58.271558000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:248	任务流已连接
2025-05-26 18:36:58.271673000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:174	Task manager started	{"worker_count": 5}
2025-05-26 18:36:58.271728000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 4}
2025-05-26 18:36:58.271782000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 1}
2025-05-26 18:36:58.271764000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 0}
2025-05-26 18:36:58.271703000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 2}
2025-05-26 18:36:58.271804000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 3}
2025-05-26 18:36:58.271836000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_processors.go:16	Metric data processor started
2025-05-26 18:36:58.271863000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_processors.go:174	Analysis result processor started
2025-05-26 18:36:58.272744000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "/var/log/system.log", "source": "system"}
2025-05-26 18:36:58.272887000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent.log"}
2025-05-26 18:36:58.272915000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent_test.log"}
2025-05-26 18:36:58.273006000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/control_plane_test.log", "source": "agent"}
2025-05-26 18:36:58.273073000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/plane.log", "source": "agent"}
2025-05-26 18:36:58.273085000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:594	日志转发器启动完成	{"active_watchers": 3}
2025-05-26 18:37:04.802105000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:204	接收到退出信号，正在关闭 Agent...
2025-05-26 18:37:04.802226000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:317	Task worker stopped due to context cancellation	{"worker_id": 1}
2025-05-26 18:37:04.802233000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:317	Task worker stopped due to context cancellation	{"worker_id": 4}
2025-05-26 18:37:04.802262000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_processors.go:33	Metric data processor stopped
2025-05-26 18:37:04.802272000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_processors.go:179	Analysis result processor stopped
2025-05-26 18:37:04.802279000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:317	Task worker stopped due to context cancellation	{"worker_id": 3}
2025-05-26 18:37:04.802286000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:317	Task worker stopped due to context cancellation	{"worker_id": 2}
2025-05-26 18:37:04.802324000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:470	日志流上下文取消，正在关闭...
2025-05-26 18:37:04.802277000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:304	停止接收任务配置，流已关闭或上下文已取消:	{"error": "rpc error: code = Canceled desc = context canceled"}
2025-05-26 18:37:04.802316000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:410	指标流上下文取消，正在关闭...
2025-05-26 18:37:04.802337000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:356	任务流正在关闭...
2025-05-26 18:37:04.802376000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:199	Task manager stopped
2025-05-26 18:37:04.802384000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:358	任务管理器已停止.
2025-05-26 18:37:04.802367000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:317	Task worker stopped due to context cancellation	{"worker_id": 0}
2025-05-26 18:37:04.802351000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:169	插件更新流错误:	{"error": "启动插件更新流失败: 接收插件更新通知失败: rpc error: code = Canceled desc = context canceled"}
main.main.func4
	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:169
2025-05-26 18:37:08.272222000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 33, "dropped": 0}
2025-05-26 18:37:27.227518000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:256	健康检查失败，开始重连	{"error": "连接状态异常: IDLE"}
2025-05-26 18:37:27.227925000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Connected", "new_state": "Reconnecting"}
2025-05-26 18:37:27.228033000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Reconnecting", "timestamp": "2025-05-26 18:37:27.227871000"}
2025-05-26 18:37:27.228347000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 1, "server": "localhost:50051"}
2025-05-26 18:37:27.231364000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 1}
2025-05-26 18:37:28.232317000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 2, "server": "localhost:50051"}
2025-05-26 18:37:28.234735000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 2}
2025-05-26 18:37:30.235153000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 3, "server": "localhost:50051"}
2025-05-26 18:37:30.236301000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 4}
2025-05-26 18:37:34.237404000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 4, "server": "localhost:50051"}
2025-05-26 18:37:34.238733000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 8}
2025-05-26 18:37:42.239947000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 5, "server": "localhost:50051"}
2025-05-26 18:37:42.242483000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 16}
2025-05-26 18:37:58.243574000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 6, "server": "localhost:50051"}
2025-05-26 18:37:58.245271000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 32}
2025-05-26 18:38:30.246258000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 7, "server": "localhost:50051"}
2025-05-26 18:38:30.248159000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 64}
2025-05-26 18:39:34.248071000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 8, "server": "localhost:50051"}
2025-05-26 18:39:34.350627000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 128}
2025-05-26 18:40:24.757211000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent 启动中...
2025-05-26 18:40:24.765924000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:42	Agent ID:	{"agent_id": "829330cb-53d4-426d-922c-14a1fd427a1b"}
2025-05-26 18:40:24.765970000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:51	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-26 18:40:24.765983000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-26 18:40:24.770795000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Connecting", "new_state": "Connected"}
2025-05-26 18:40:24.770891000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:136	连接已建立	{"server": "localhost:50051"}
2025-05-26 18:40:24.772325000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:87	Agent 注册成功
2025-05-26 18:40:24.772357000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:509	开始初始化插件管理器
2025-05-26 18:40:24.772366000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:513	预加载插件	{"device_type": "system"}
2025-05-26 18:40:24.772385000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "system"}
2025-05-26 18:40:24.772405000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:41	请求插件	{"plugin_name": "system-collector", "version": "", "device_type": "system"}
2025-05-26 18:40:24.820926000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:133	从插件路径加载插件	{"device_type": "system", "plugin_name": "system-collector", "plugin_version": "1.0.0", "plugin_path": "plugins/build/system-collector-1.0.0.so", "absolute_path": "plugins/build/system-collector-1.0.0.so"}
2025-05-26 18:40:24.829327000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:259	插件校验和	{"path": "plugins/build/system-collector-1.0.0.so", "checksum": "ad255f3f9c60db3d7b42c512076a51300fd50c485f1a4929199b20a8be465367"}
2025-05-26 18:40:24.849792000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:516	预加载插件失败，将使用内置采集器作为回退	{"device_type": "system", "error": "打开插件失败: plugin.Open(\"plugins/build/system-collector-1.0.0\"): plugin was built with a different version of package aiops/plugins/interface"}
2025-05-26 18:40:24.849861000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:513	预加载插件	{"device_type": "mysql"}
2025-05-26 18:40:24.849873000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "mysql"}
2025-05-26 18:40:24.849884000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:41	请求插件	{"plugin_name": "mysql-collector", "version": "", "device_type": "mysql"}
2025-05-26 18:40:24.898670000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:133	从插件路径加载插件	{"device_type": "mysql", "plugin_name": "mysql-collector", "plugin_version": "1.0.0", "plugin_path": "plugins/build/mysql-collector-1.0.0.so", "absolute_path": "plugins/build/mysql-collector-1.0.0.so"}
2025-05-26 18:40:24.906349000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:259	插件校验和	{"path": "plugins/build/mysql-collector-1.0.0.so", "checksum": "52bc04503c34dcbc88c163390a617fc4c3280d64ca40acbcd8b01ae061e6a0db"}
2025-05-26 18:40:24.912865000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:516	预加载插件失败，将使用内置采集器作为回退	{"device_type": "mysql", "error": "打开插件失败: plugin.Open(\"plugins/build/mysql-collector-1.0.0\"): plugin was built with a different version of package aiops/plugins/interface"}
2025-05-26 18:40:24.912932000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:526	插件管理器初始化完成
2025-05-26 18:40:24.912993000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Connecting", "timestamp": "2025-05-26 18:40:24.765983000"}
2025-05-26 18:40:24.913061000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-26 18:40:24.770792000"}
2025-05-26 18:40:24.913055000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:486	启动插件更新流监听
2025-05-26 18:40:24.913074000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:581	日志转发器正在启动...
2025-05-26 18:40:24.913208000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:432	日志流已连接
2025-05-26 18:40:24.913137000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:133	启动插件更新流监听
2025-05-26 18:40:24.913247000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:248	任务流已连接
2025-05-26 18:40:24.913233000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:370	指标流已连接
2025-05-26 18:40:24.913328000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:174	Task manager started	{"worker_count": 5}
2025-05-26 18:40:24.913422000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 0}
2025-05-26 18:40:24.913435000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 4}
2025-05-26 18:40:24.913440000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_processors.go:174	Analysis result processor started
2025-05-26 18:40:24.913461000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_processors.go:16	Metric data processor started
2025-05-26 18:40:24.913462000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 3}
2025-05-26 18:40:24.913480000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 1}
2025-05-26 18:40:24.913502000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 2}
2025-05-26 18:40:24.915481000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "/var/log/system.log", "source": "system"}
2025-05-26 18:40:24.915746000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent.log"}
2025-05-26 18:40:24.915791000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent_test.log"}
2025-05-26 18:40:24.915864000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/control_plane_test.log", "source": "agent"}
2025-05-26 18:40:24.915916000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/plane.log", "source": "agent"}
2025-05-26 18:40:24.915964000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:594	日志转发器启动完成	{"active_watchers": 3}
2025-05-26 18:40:32.524119000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:204	接收到退出信号，正在关闭 Agent...
2025-05-26 18:40:32.524249000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:317	Task worker stopped due to context cancellation	{"worker_id": 0}
2025-05-26 18:40:32.524332000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:317	Task worker stopped due to context cancellation	{"worker_id": 2}
2025-05-26 18:40:32.524351000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:470	日志流上下文取消，正在关闭...
2025-05-26 18:40:32.524311000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_processors.go:33	Metric data processor stopped
2025-05-26 18:40:32.524344000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:169	插件更新流错误:	{"error": "启动插件更新流失败: 接收插件更新通知失败: rpc error: code = Canceled desc = context canceled"}
main.main.func4
	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:169
2025-05-26 18:40:32.524352000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:410	指标流上下文取消，正在关闭...
2025-05-26 18:40:32.524371000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:317	Task worker stopped due to context cancellation	{"worker_id": 1}
2025-05-26 18:40:32.524384000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:356	任务流正在关闭...
2025-05-26 18:40:32.524373000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:304	停止接收任务配置，流已关闭或上下文已取消:	{"error": "rpc error: code = Canceled desc = context canceled"}
2025-05-26 18:40:32.524405000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_processors.go:179	Analysis result processor stopped
2025-05-26 18:40:32.524500000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:199	Task manager stopped
2025-05-26 18:40:32.524601000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:358	任务管理器已停止.
2025-05-26 18:40:32.524412000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:317	Task worker stopped due to context cancellation	{"worker_id": 4}
2025-05-26 18:40:32.524278000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:317	Task worker stopped due to context cancellation	{"worker_id": 3}
2025-05-26 18:40:33.310039000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 84, "dropped": 0}
2025-05-26 18:40:34.914155000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 7, "dropped": 0}
2025-05-26 18:40:38.311355000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 116, "dropped": 0}
2025-05-26 18:40:39.915253000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 35, "dropped": 0}
2025-05-26 18:40:54.771888000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:256	健康检查失败，开始重连	{"error": "连接状态异常: IDLE"}
2025-05-26 18:40:54.773512000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Connected", "new_state": "Reconnecting"}
2025-05-26 18:40:54.773535000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Reconnecting", "timestamp": "2025-05-26 18:40:54.773491000"}
2025-05-26 18:40:54.773800000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 1, "server": "localhost:50051"}
2025-05-26 18:40:54.775202000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 1}
2025-05-26 18:40:55.776389000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 2, "server": "localhost:50051"}
2025-05-26 18:40:55.780698000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 2}
2025-05-26 18:40:57.780877000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 3, "server": "localhost:50051"}
2025-05-26 18:40:57.782336000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 4}
2025-05-26 18:41:01.783405000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 4, "server": "localhost:50051"}
2025-05-26 18:41:01.785197000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 8}
2025-05-26 18:41:09.786282000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 5, "server": "localhost:50051"}
2025-05-26 18:41:09.796231000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 16}
2025-05-26 18:41:25.796758000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 6, "server": "localhost:50051"}
2025-05-26 18:41:25.798572000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 32}
2025-05-26 18:41:42.438190000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 9, "server": "localhost:50051"}
2025-05-26 18:41:43.050799000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 256}
2025-05-26 18:41:59.174448000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 7, "server": "localhost:50051"}
2025-05-26 18:42:02.419476000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 64}
2025-05-26 18:43:06.468755000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 8, "server": "localhost:50051"}
2025-05-26 18:43:06.798567000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 128}
2025-05-26 18:43:23.850977000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent 启动中...
2025-05-26 18:43:23.851636000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:42	Agent ID:	{"agent_id": "c845e20b-244e-416f-8db1-46c43be776b0"}
2025-05-26 18:43:23.851656000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:51	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-26 18:43:23.851667000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-26 18:43:23.854223000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Connecting", "new_state": "Connected"}
2025-05-26 18:43:23.854277000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:136	连接已建立	{"server": "localhost:50051"}
2025-05-26 18:43:23.856558000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:87	Agent 注册成功
2025-05-26 18:43:23.856600000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:509	开始初始化插件管理器
2025-05-26 18:43:23.856609000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:513	预加载插件	{"device_type": "system"}
2025-05-26 18:43:23.856629000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "system"}
2025-05-26 18:43:23.856642000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:41	请求插件	{"plugin_name": "system-collector", "version": "", "device_type": "system"}
2025-05-26 18:43:23.906414000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:133	从插件路径加载插件	{"device_type": "system", "plugin_name": "system-collector", "plugin_version": "1.0.0", "plugin_path": "plugins/build/system-collector-1.0.0.so", "absolute_path": "plugins/build/system-collector-1.0.0.so"}
2025-05-26 18:43:23.914584000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:259	插件校验和	{"path": "plugins/build/system-collector-1.0.0.so", "checksum": "f0ac593c08a16ea451635452f8efb309710bf7239c40654b9812caab44753f6d"}
2025-05-26 18:43:24.322737000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:238	插件加载成功	{"plugin_id": "system", "plugin_path": "plugins/build/system-collector-1.0.0.so"}
2025-05-26 18:43:24.322793000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:523	插件预加载成功	{"device_type": "system"}
2025-05-26 18:43:24.322803000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:513	预加载插件	{"device_type": "mysql"}
2025-05-26 18:43:24.322812000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "mysql"}
2025-05-26 18:43:24.322823000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:41	请求插件	{"plugin_name": "mysql-collector", "version": "", "device_type": "mysql"}
2025-05-26 18:43:24.371328000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:133	从插件路径加载插件	{"device_type": "mysql", "plugin_name": "mysql-collector", "plugin_version": "1.0.0", "plugin_path": "plugins/build/mysql-collector-1.0.0.so", "absolute_path": "plugins/build/mysql-collector-1.0.0.so"}
2025-05-26 18:43:24.379056000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:259	插件校验和	{"path": "plugins/build/mysql-collector-1.0.0.so", "checksum": "20e8b76acad66a8fa1bc98a7ca6c178e90eea267397d6165952bff6fa6489048"}
2025-05-26 18:43:24.656256000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:238	插件加载成功	{"plugin_id": "mysql", "plugin_path": "plugins/build/mysql-collector-1.0.0.so"}
2025-05-26 18:43:24.656312000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:523	插件预加载成功	{"device_type": "mysql"}
2025-05-26 18:43:24.656321000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:526	插件管理器初始化完成
2025-05-26 18:43:24.656362000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Connecting", "timestamp": "2025-05-26 18:43:23.851666000"}
2025-05-26 18:43:24.656394000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-26 18:43:23.854222000"}
2025-05-26 18:43:24.656413000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:581	日志转发器正在启动...
2025-05-26 18:43:24.656420000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:486	启动插件更新流监听
2025-05-26 18:43:24.656513000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:133	启动插件更新流监听
2025-05-26 18:43:24.656477000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:432	日志流已连接
2025-05-26 18:43:24.656477000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:370	指标流已连接
2025-05-26 18:43:24.656564000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:248	任务流已连接
2025-05-26 18:43:24.656623000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:174	Task manager started	{"worker_count": 5}
2025-05-26 18:43:24.656642000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 0}
2025-05-26 18:43:24.656683000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_processors.go:174	Analysis result processor started
2025-05-26 18:43:24.656671000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 1}
2025-05-26 18:43:24.656674000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 3}
2025-05-26 18:43:24.656680000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 4}
2025-05-26 18:43:24.656638000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 2}
2025-05-26 18:43:24.656692000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_processors.go:16	Metric data processor started
2025-05-26 18:43:24.657080000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "/var/log/system.log", "source": "system"}
2025-05-26 18:43:24.657177000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent.log"}
2025-05-26 18:43:24.657195000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent_test.log"}
2025-05-26 18:43:24.657259000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/control_plane_test.log", "source": "agent"}
2025-05-26 18:43:24.657301000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/plane.log", "source": "agent"}
2025-05-26 18:43:24.657312000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:594	日志转发器启动完成	{"active_watchers": 3}
2025-05-26 18:43:24.977433000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 89, "dropped": 0}
2025-05-26 18:43:31.734230000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:204	接收到退出信号，正在关闭 Agent...
2025-05-26 18:43:31.734337000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_processors.go:33	Metric data processor stopped
2025-05-26 18:43:31.734359000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:304	停止接收任务配置，流已关闭或上下文已取消:	{"error": "rpc error: code = Canceled desc = context canceled"}
2025-05-26 18:43:31.734377000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:317	Task worker stopped due to context cancellation	{"worker_id": 2}
2025-05-26 18:43:31.734386000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:317	Task worker stopped due to context cancellation	{"worker_id": 4}
2025-05-26 18:43:31.734390000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:317	Task worker stopped due to context cancellation	{"worker_id": 3}
2025-05-26 18:43:31.734393000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:317	Task worker stopped due to context cancellation	{"worker_id": 1}
2025-05-26 18:43:31.734367000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:356	任务流正在关闭...
2025-05-26 18:43:31.734421000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:317	Task worker stopped due to context cancellation	{"worker_id": 0}
2025-05-26 18:43:31.734425000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:410	指标流上下文取消，正在关闭...
2025-05-26 18:43:31.734429000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:470	日志流上下文取消，正在关闭...
2025-05-26 18:43:31.734427000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:169	插件更新流错误:	{"error": "启动插件更新流失败: 接收插件更新通知失败: rpc error: code = Canceled desc = context canceled"}
main.main.func4
	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:169
2025-05-26 18:43:31.734415000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_processors.go:179	Analysis result processor stopped
2025-05-26 18:43:31.734554000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:199	Task manager stopped
2025-05-26 18:43:31.734775000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:358	任务管理器已停止.
2025-05-26 18:43:33.475191000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 170, "dropped": 0}
2025-05-26 18:43:34.980154000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 121, "dropped": 0}
2025-05-26 18:43:38.803508000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 202, "dropped": 0}
2025-05-26 18:43:39.658697000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 34, "dropped": 0}
2025-05-26 18:43:53.855222000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:256	健康检查失败，开始重连	{"error": "连接状态异常: IDLE"}
2025-05-26 18:43:53.921384000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Connected", "new_state": "Reconnecting"}
2025-05-26 18:43:53.921408000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Reconnecting", "timestamp": "2025-05-26 18:43:53.921378000"}
2025-05-26 18:43:53.921541000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 1, "server": "localhost:50051"}
2025-05-26 18:43:53.922346000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 1}
2025-05-26 18:43:54.923425000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 2, "server": "localhost:50051"}
2025-05-26 18:43:54.924540000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 2}
2025-05-26 18:43:56.925171000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 3, "server": "localhost:50051"}
2025-05-26 18:43:56.927251000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 4}
2025-05-26 18:44:00.928370000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 4, "server": "localhost:50051"}
2025-05-26 18:44:00.930166000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 8}
2025-05-26 18:44:08.931115000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 5, "server": "localhost:50051"}
2025-05-26 18:44:08.932348000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 16}
2025-05-26 18:44:24.933487000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 6, "server": "localhost:50051"}
2025-05-26 18:44:24.938005000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 32}
2025-05-26 18:44:44.668139000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 35, "dropped": 0}
2025-05-26 18:44:44.994401000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 122, "dropped": 0}
2025-05-26 18:44:48.822015000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 203, "dropped": 0}
2025-05-26 18:44:56.939157000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 7, "server": "localhost:50051"}
2025-05-26 18:44:57.200756000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 64}
2025-05-26 18:45:14.799230000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 9, "server": "localhost:50051"}
2025-05-26 18:45:14.801188000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 256}
2025-05-26 18:45:32.073172000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent 启动中...
2025-05-26 18:45:32.074032000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:42	Agent ID:	{"agent_id": "c8b1cefb-8aa1-4f95-9103-8e8f2d0cc2f2"}
2025-05-26 18:45:32.074050000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:51	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-26 18:45:32.074062000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-26 18:45:32.077324000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Connecting", "new_state": "Connected"}
2025-05-26 18:45:32.077352000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:136	连接已建立	{"server": "localhost:50051"}
2025-05-26 18:45:32.079598000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:87	Agent 注册成功
2025-05-26 18:45:32.079642000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:509	开始初始化插件管理器
2025-05-26 18:45:32.079653000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:513	预加载插件	{"device_type": "system"}
2025-05-26 18:45:32.079664000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "system"}
2025-05-26 18:45:32.079678000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:41	请求插件	{"plugin_name": "system-collector", "version": "", "device_type": "system"}
2025-05-26 18:45:32.130667000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:133	从插件路径加载插件	{"device_type": "system", "plugin_name": "system-collector", "plugin_version": "1.0.0", "plugin_path": "plugins/build/system-collector-1.0.0.so", "absolute_path": "plugins/build/system-collector-1.0.0.so"}
2025-05-26 18:45:32.139442000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:259	插件校验和	{"path": "plugins/build/system-collector-1.0.0.so", "checksum": "f0ac593c08a16ea451635452f8efb309710bf7239c40654b9812caab44753f6d"}
2025-05-26 18:45:32.148552000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:238	插件加载成功	{"plugin_id": "system", "plugin_path": "plugins/build/system-collector-1.0.0.so"}
2025-05-26 18:45:32.148635000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:523	插件预加载成功	{"device_type": "system"}
2025-05-26 18:45:32.148658000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:513	预加载插件	{"device_type": "mysql"}
2025-05-26 18:45:32.148696000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "mysql"}
2025-05-26 18:45:32.148714000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:41	请求插件	{"plugin_name": "mysql-collector", "version": "", "device_type": "mysql"}
2025-05-26 18:45:32.194929000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:133	从插件路径加载插件	{"device_type": "mysql", "plugin_name": "mysql-collector", "plugin_version": "1.0.0", "plugin_path": "plugins/build/mysql-collector-1.0.0.so", "absolute_path": "plugins/build/mysql-collector-1.0.0.so"}
2025-05-26 18:45:32.203684000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:259	插件校验和	{"path": "plugins/build/mysql-collector-1.0.0.so", "checksum": "20e8b76acad66a8fa1bc98a7ca6c178e90eea267397d6165952bff6fa6489048"}
2025-05-26 18:45:32.213259000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:238	插件加载成功	{"plugin_id": "mysql", "plugin_path": "plugins/build/mysql-collector-1.0.0.so"}
2025-05-26 18:45:32.213313000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:523	插件预加载成功	{"device_type": "mysql"}
2025-05-26 18:45:32.213322000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:526	插件管理器初始化完成
2025-05-26 18:45:32.213366000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Connecting", "timestamp": "2025-05-26 18:45:32.074062000"}
2025-05-26 18:45:32.213398000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-26 18:45:32.077323000"}
2025-05-26 18:45:32.213410000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:581	日志转发器正在启动...
2025-05-26 18:45:32.213445000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:432	日志流已连接
2025-05-26 18:45:32.213442000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:486	启动插件更新流监听
2025-05-26 18:45:32.213474000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:248	任务流已连接
2025-05-26 18:45:32.213553000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:174	Task manager started	{"worker_count": 5}
2025-05-26 18:45:32.213534000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:133	启动插件更新流监听
2025-05-26 18:45:32.213770000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 2}
2025-05-26 18:45:32.213503000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:370	指标流已连接
2025-05-26 18:45:32.213807000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "/var/log/system.log", "source": "system"}
2025-05-26 18:45:32.213868000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_processors.go:174	Analysis result processor started
2025-05-26 18:45:32.213905000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent.log"}
2025-05-26 18:45:32.213913000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 3}
2025-05-26 18:45:32.213925000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent_test.log"}
2025-05-26 18:45:32.213782000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 0}
2025-05-26 18:45:32.213991000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/control_plane_test.log", "source": "agent"}
2025-05-26 18:45:32.213885000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 1}
2025-05-26 18:45:32.214037000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/plane.log", "source": "agent"}
2025-05-26 18:45:32.214051000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:594	日志转发器启动完成	{"active_watchers": 3}
2025-05-26 18:45:32.214017000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 4}
2025-05-26 18:45:32.214099000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_processors.go:16	Metric data processor started
2025-05-26 18:45:34.903201000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 89, "dropped": 0}
2025-05-26 18:45:35.003493000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 176, "dropped": 0}
2025-05-26 18:45:38.831491000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 257, "dropped": 0}
2025-05-26 18:45:39.847533000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:204	接收到退出信号，正在关闭 Agent...
2025-05-26 18:45:39.847664000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:169	插件更新流错误:	{"error": "启动插件更新流失败: 接收插件更新通知失败: rpc error: code = Canceled desc = context canceled"}
main.main.func4
	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:169
2025-05-26 18:45:39.847732000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:317	Task worker stopped due to context cancellation	{"worker_id": 2}
2025-05-26 18:45:39.847756000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:470	日志流上下文取消，正在关闭...
2025-05-26 18:45:39.847722000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:410	指标流上下文取消，正在关闭...
2025-05-26 18:45:39.847780000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:356	任务流正在关闭...
2025-05-26 18:45:39.847948000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:199	Task manager stopped
2025-05-26 18:45:39.847778000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:304	停止接收任务配置，流已关闭或上下文已取消:	{"error": "rpc error: code = Canceled desc = context canceled"}
2025-05-26 18:45:39.848006000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:358	任务管理器已停止.
2025-05-26 18:45:39.847801000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_processors.go:179	Analysis result processor stopped
2025-05-26 18:45:39.847800000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_processors.go:33	Metric data processor stopped
2025-05-26 18:45:39.847804000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:317	Task worker stopped due to context cancellation	{"worker_id": 1}
2025-05-26 18:45:39.847796000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:317	Task worker stopped due to context cancellation	{"worker_id": 3}
2025-05-26 18:45:39.847795000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:317	Task worker stopped due to context cancellation	{"worker_id": 4}
2025-05-26 18:45:39.847795000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:317	Task worker stopped due to context cancellation	{"worker_id": 0}
2025-05-26 18:45:44.905619000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 121, "dropped": 0}
2025-05-26 18:45:45.005447000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 208, "dropped": 0}
2025-05-26 18:45:47.215424000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 36, "dropped": 0}
2025-05-26 18:45:48.875518000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 289, "dropped": 0}
2025-05-26 18:45:59.083013000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 10, "server": "localhost:50051"}
2025-05-26 18:45:59.555586000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 300}
2025-05-26 18:46:01.201339000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 8, "server": "localhost:50051"}
2025-05-26 18:46:01.203959000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 128}
2025-05-26 18:46:02.078629000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:256	健康检查失败，开始重连	{"error": "连接状态异常: IDLE"}
2025-05-26 18:46:02.078852000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Reconnecting", "timestamp": "2025-05-26 18:46:02.078786000"}
2025-05-26 18:46:02.078894000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Connected", "new_state": "Reconnecting"}
2025-05-26 18:46:02.079167000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 1, "server": "localhost:50051"}
2025-05-26 18:46:02.080726000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 1}
2025-05-26 18:46:03.082026000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 2, "server": "localhost:50051"}
2025-05-26 18:46:03.084284000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 2}
2025-05-26 18:46:05.085011000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 3, "server": "localhost:50051"}
2025-05-26 18:46:05.088067000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 4}
2025-05-26 18:46:09.089264000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 4, "server": "localhost:50051"}
2025-05-26 18:46:09.091661000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 8}
2025-05-26 18:46:17.092852000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 5, "server": "localhost:50051"}
2025-05-26 18:46:17.094920000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 16}
2025-05-26 18:46:33.095920000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 6, "server": "localhost:50051"}
2025-05-26 18:46:33.098311000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 32}
2025-05-26 18:47:05.098542000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 7, "server": "localhost:50051"}
2025-05-26 18:47:05.109026000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 64}
2025-05-26 18:48:09.108859000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 8, "server": "localhost:50051"}
2025-05-26 18:48:09.124385000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 128}
2025-05-26 18:48:09.203664000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 9, "server": "localhost:50051"}
2025-05-26 18:48:09.206017000	[33mwarn[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:340	重连失败，等待重试	{"error": "连接失败，状态: TRANSIENT_FAILURE", "backoff": 256}
2025-05-26 18:48:52.213487000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:272	检测到文件被截断，重新开始读取	{"file": "logs/control_plane_test.log"}
2025-05-26 18:48:54.655702000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:272	检测到文件被截断，重新开始读取	{"file": "logs/control_plane_test.log"}
2025-05-26 18:48:54.656329000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 221, "dropped": 0}
2025-05-26 18:48:54.928994000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:272	检测到文件被截断，重新开始读取	{"file": "logs/control_plane_test.log"}
2025-05-26 18:48:54.929983000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 308, "dropped": 0}
2025-05-26 18:48:54.938287000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 233, "dropped": 0}
2025-05-26 18:48:55.040202000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 320, "dropped": 0}
2025-05-26 18:48:58.300643000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:272	检测到文件被截断，重新开始读取	{"file": "logs/control_plane_test.log"}
2025-05-26 18:48:58.311327000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 389, "dropped": 0}
2025-05-26 18:48:58.910635000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 401, "dropped": 0}
2025-05-26 18:49:02.213658000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 136, "dropped": 0}
2025-05-26 18:49:02.246728000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 148, "dropped": 0}
2025-05-26 18:49:04.676678000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:41	Agent 启动中...
2025-05-26 18:49:04.677188000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:42	Agent ID:	{"agent_id": "d8fe7b8a-63be-47a9-b625-b0bcc3afc86b"}
2025-05-26 18:49:04.677201000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:51	控制平面地址:	{"server_address": "localhost:50051"}
2025-05-26 18:49:04.677214000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Disconnected", "new_state": "Connecting"}
2025-05-26 18:49:04.679486000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Connecting", "new_state": "Connected"}
2025-05-26 18:49:04.679514000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:136	连接已建立	{"server": "localhost:50051"}
2025-05-26 18:49:04.681555000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:87	Agent 注册成功
2025-05-26 18:49:04.681584000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:509	开始初始化插件管理器
2025-05-26 18:49:04.681593000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:513	预加载插件	{"device_type": "system"}
2025-05-26 18:49:04.681603000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "system"}
2025-05-26 18:49:04.681613000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:41	请求插件	{"plugin_name": "system-collector", "version": "", "device_type": "system"}
2025-05-26 18:49:04.729790000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:133	从插件路径加载插件	{"device_type": "system", "plugin_name": "system-collector", "plugin_version": "1.0.0", "plugin_path": "plugins/build/system-collector-1.0.0.so", "absolute_path": "plugins/build/system-collector-1.0.0.so"}
2025-05-26 18:49:04.738653000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:259	插件校验和	{"path": "plugins/build/system-collector-1.0.0.so", "checksum": "f0ac593c08a16ea451635452f8efb309710bf7239c40654b9812caab44753f6d"}
2025-05-26 18:49:04.745989000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:238	插件加载成功	{"plugin_id": "system", "plugin_path": "plugins/build/system-collector-1.0.0.so"}
2025-05-26 18:49:04.746034000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:523	插件预加载成功	{"device_type": "system"}
2025-05-26 18:49:04.746044000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:513	预加载插件	{"device_type": "mysql"}
2025-05-26 18:49:04.746052000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:96	向控制平面请求插件	{"device_type": "mysql"}
2025-05-26 18:49:04.746060000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:41	请求插件	{"plugin_name": "mysql-collector", "version": "", "device_type": "mysql"}
2025-05-26 18:49:04.791146000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:133	从插件路径加载插件	{"device_type": "mysql", "plugin_name": "mysql-collector", "plugin_version": "1.0.0", "plugin_path": "plugins/build/mysql-collector-1.0.0.so", "absolute_path": "plugins/build/mysql-collector-1.0.0.so"}
2025-05-26 18:49:04.800066000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:259	插件校验和	{"path": "plugins/build/mysql-collector-1.0.0.so", "checksum": "20e8b76acad66a8fa1bc98a7ca6c178e90eea267397d6165952bff6fa6489048"}
2025-05-26 18:49:04.808995000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_manager.go:238	插件加载成功	{"plugin_id": "mysql", "plugin_path": "plugins/build/mysql-collector-1.0.0.so"}
2025-05-26 18:49:04.809057000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:523	插件预加载成功	{"device_type": "mysql"}
2025-05-26 18:49:04.809072000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:526	插件管理器初始化完成
2025-05-26 18:49:04.809135000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Connecting", "timestamp": "2025-05-26 18:49:04.677214000"}
2025-05-26 18:49:04.809163000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-26 18:49:04.679485000"}
2025-05-26 18:49:04.809175000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:432	日志流已连接
2025-05-26 18:49:04.809175000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:370	指标流已连接
2025-05-26 18:49:04.809185000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:581	日志转发器正在启动...
2025-05-26 18:49:04.809219000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:248	任务流已连接
2025-05-26 18:49:04.809168000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:486	启动插件更新流监听
2025-05-26 18:49:04.809308000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/plugin_client.go:133	启动插件更新流监听
2025-05-26 18:49:04.809321000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 0}
2025-05-26 18:49:04.809332000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "/var/log/system.log", "source": "system"}
2025-05-26 18:49:04.809243000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:174	Task manager started	{"worker_count": 5}
2025-05-26 18:49:04.809255000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_processors.go:16	Metric data processor started
2025-05-26 18:49:04.809262000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 3}
2025-05-26 18:49:04.809554000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent.log"}
2025-05-26 18:49:04.809579000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:526	跳过Agent自身日志文件	{"file": "logs/agent_test.log"}
2025-05-26 18:49:04.809638000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/control_plane_test.log", "source": "agent"}
2025-05-26 18:49:04.809264000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 4}
2025-05-26 18:49:04.809260000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 2}
2025-05-26 18:49:04.809272000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_manager.go:311	Task worker started	{"worker_id": 1}
2025-05-26 18:49:04.809273000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/task_processors.go:174	Analysis result processor started
2025-05-26 18:49:04.809690000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:197	添加日志文件监控	{"file": "logs/plane.log", "source": "agent"}
2025-05-26 18:49:04.809698000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:594	日志转发器启动完成	{"active_watchers": 3}
2025-05-26 18:49:05.042385000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 372, "dropped": 0}
2025-05-26 18:49:08.913141000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 453, "dropped": 0}
2025-05-26 18:49:12.247906000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 200, "dropped": 0}
2025-05-26 18:49:14.810382000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 6, "dropped": 0}
2025-05-26 18:49:14.942851000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 285, "dropped": 0}
2025-05-26 18:49:15.046104000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 374, "dropped": 0}
2025-05-26 18:49:18.914749000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 455, "dropped": 0}
2025-05-26 18:49:22.250797000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 202, "dropped": 0}
2025-05-26 18:49:24.945318000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 287, "dropped": 0}
2025-05-26 18:49:29.812316000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 8, "dropped": 0}
2025-05-26 18:49:30.800546000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 10, "server": "localhost:50051"}
2025-05-26 18:49:30.856522000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Reconnecting", "new_state": "Connected"}
2025-05-26 18:49:30.856560000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-26 18:49:30.856498000"}
2025-05-26 18:49:30.856866000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:368	重连成功	{"server": "localhost:50051"}
2025-05-26 18:50:17.124813000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 9, "server": "localhost:50051"}
2025-05-26 18:50:17.179085000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Reconnecting", "new_state": "Connected"}
2025-05-26 18:50:17.179140000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-26 18:50:17.179075000"}
2025-05-26 18:50:17.179194000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:368	重连成功	{"server": "localhost:50051"}
2025-05-26 18:50:59.515652000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 11, "server": "localhost:50051"}
2025-05-26 18:50:59.750706000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Reconnecting", "new_state": "Connected"}
2025-05-26 18:50:59.750768000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:368	重连成功	{"server": "localhost:50051"}
2025-05-26 18:50:59.776640000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-26 18:50:59.750700000"}
2025-05-26 18:52:25.160706000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:333	尝试重连	{"attempt": 10, "server": "localhost:50051"}
2025-05-26 18:52:25.262699000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:239	连接状态变更	{"old_state": "Reconnecting", "new_state": "Connected"}
2025-05-26 18:52:25.262730000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/connection_manager.go:368	重连成功	{"server": "localhost:50051"}
2025-05-26 18:52:25.262732000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:135	连接状态变更事件	{"state": "Connected", "timestamp": "2025-05-26 18:52:25.262698000"}
2025-05-26 18:54:32.273177000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 214, "dropped": 0}
2025-05-26 18:54:34.823531000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 35, "dropped": 0}
2025-05-26 18:54:34.964802000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 314, "dropped": 0}
2025-05-26 18:54:35.062179000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 401, "dropped": 0}
2025-05-26 18:54:38.937432000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 482, "dropped": 0}
2025-05-26 18:54:42.291824000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/agent/internal/log_forwarder.go:471	刷新日志缓冲区	{"forwarded": 229, "dropped": 0}
2025-05-26 18:55:21.210304000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:169	插件更新流错误:	{"error": "启动插件更新流失败: 接收插件更新通知失败: rpc error: code = Unavailable desc = error reading from server: EOF"}
main.main.func4
	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:169
2025-05-26 18:55:21.210410000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:307	接收任务配置失败:	{"error": "rpc error: code = Unavailable desc = error reading from server: EOF"}
main.startTaskStream.func4
	/Volumes/data/Code/Go/src/aiops/agent/cmd/main.go:307
