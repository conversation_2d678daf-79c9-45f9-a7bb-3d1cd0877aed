load conf file: config/control_plane.yaml
2025-05-26 18:56:18.638969000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:24	DevInsight Control Plane 正在启动...
2025/05/26 18:56:18 配置加载完成: HTTP端口=8080, gRPC端口=50051, 数据库路径=data/control_plane.db?_busy_timeout=5000, SMTP服务器=:587, 告警阈值=90.00
2025-05-26 18:56:18.639441000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:18	初始化数据库	{"dbPath": "data/control_plane.db?_busy_timeout=5000"}
2025-05-26 18:56:18.747348000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:32	数据库连接成功
2025-05-26 18:56:18.747400000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:49	SQLite WAL 模式启用成功
2025-05-26 18:56:18.747431000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:53	开始执行数据库迁移
2025-05-26 18:56:18.752870000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/database/sqlite.go:68	数据库迁移完成
2025-05-26 18:56:18.752940000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:35	数据库初始化成功
2025-05-26 18:56:18.752952000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:60	控制平面插件分发模式：仅负责插件管理和分发，不加载本地插件
2025-05-26 18:56:18.752987000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:657	开始自动发现插件	{"plugin_dir": "./plugins/build"}
2025-05-26 18:56:18.753038000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:700	扫描插件目录	{"dir": "./plugins/build"}
2025-05-26 18:56:18.873828000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:798	自动注册插件成功	{"plugin_id": "email-alerter-1.0.0", "name": "email-alerter", "version": "1.0.0", "device_types": ["general"], "path": "plugins/build/email-alerter-1.0.0.so"}
2025-05-26 18:56:18.881206000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:769	插件已存在，跳过注册	{"plugin_id": "email-alerter-1.0.0"}
2025-05-26 18:56:19.015226000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:798	自动注册插件成功	{"plugin_id": "enhanced-analyzer-1.0.0", "name": "enhanced-analyzer", "version": "1.0.0", "device_types": ["general"], "path": "plugins/build/enhanced-analyzer-1.0.0.so"}
2025-05-26 18:56:19.021530000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:769	插件已存在，跳过注册	{"plugin_id": "enhanced-analyzer-1.0.0"}
2025-05-26 18:56:19.027666000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:798	自动注册插件成功	{"plugin_id": "mysql-collector-1.0.0", "name": "mysql-collector", "version": "1.0.0", "device_types": ["mysql"], "path": "plugins/build/mysql-collector-1.0.0.so"}
2025-05-26 18:56:19.034353000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:769	插件已存在，跳过注册	{"plugin_id": "mysql-collector-1.0.0"}
2025-05-26 18:56:19.158015000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:798	自动注册插件成功	{"plugin_id": "simple-analyzer-1.0.0", "name": "simple-analyzer", "version": "1.0.0", "device_types": ["general"], "path": "plugins/build/simple-analyzer-1.0.0.so"}
2025-05-26 18:56:19.164434000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:769	插件已存在，跳过注册	{"plugin_id": "simple-analyzer-1.0.0"}
2025-05-26 18:56:19.173527000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:798	自动注册插件成功	{"plugin_id": "system-collector-1.0.0", "name": "system-collector", "version": "1.0.0", "device_types": ["system"], "path": "plugins/build/system-collector-1.0.0.so"}
2025-05-26 18:56:19.180072000	[35mdebug[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:769	插件已存在，跳过注册	{"plugin_id": "system-collector-1.0.0"}
2025-05-26 18:56:19.180157000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:686	插件自动发现完成	{"total_registered": 10, "total_plugins": 5}
2025-05-26 18:56:19.181768000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/user_service.go:229	管理员用户已存在，跳过初始化
2025-05-26 18:56:19.182150000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:83	启动 gRPC 服务器	{"监听地址": "0.0.0.0:50051"}
[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] GET    /api/health               --> aiops/control_plane/internal/transport/http.(*Server).handleHealth-fm (2 handlers)
[GIN-debug] POST   /api/auth/login           --> aiops/control_plane/internal/transport/http.(*Server).handleLogin-fm (2 handlers)
[GIN-debug] POST   /api/auth/logout          --> aiops/control_plane/internal/transport/http.(*Server).handleLogout-fm (3 handlers)
[GIN-debug] GET    /api/v1/agents            --> aiops/control_plane/internal/transport/http.(*Server).handleGetAllAgents-fm (3 handlers)
[GIN-debug] GET    /api/v1/agents/:id        --> aiops/control_plane/internal/transport/http.(*Server).handleGetAgent-fm (3 handlers)
[GIN-debug] DELETE /api/v1/agents/:id        --> aiops/control_plane/internal/transport/http.(*Server).handleDeleteAgent-fm (3 handlers)
[GIN-debug] GET    /api/v1/devices           --> aiops/control_plane/internal/transport/http.(*Server).handleGetAllDevices-fm (3 handlers)
[GIN-debug] POST   /api/v1/devices           --> aiops/control_plane/internal/transport/http.(*Server).handleCreateDevice-fm (3 handlers)
[GIN-debug] GET    /api/v1/devices/:id       --> aiops/control_plane/internal/transport/http.(*Server).handleGetDevice-fm (3 handlers)
[GIN-debug] PUT    /api/v1/devices/:id       --> aiops/control_plane/internal/transport/http.(*Server).handleUpdateDevice-fm (3 handlers)
[GIN-debug] DELETE /api/v1/devices/:id       --> aiops/control_plane/internal/transport/http.(*Server).handleDeleteDevice-fm (3 handlers)
[GIN-debug] GET    /api/v1/tasks             --> aiops/control_plane/internal/transport/http.(*Server).handleGetAllTasks-fm (3 handlers)
[GIN-debug] POST   /api/v1/tasks             --> aiops/control_plane/internal/transport/http.(*Server).handleCreateTask-fm (3 handlers)
[GIN-debug] GET    /api/v1/tasks/:id         --> aiops/control_plane/internal/transport/http.(*Server).handleGetTask-fm (3 handlers)
[GIN-debug] PUT    /api/v1/tasks/:id         --> aiops/control_plane/internal/transport/http.(*Server).handleUpdateTask-fm (3 handlers)
[GIN-debug] DELETE /api/v1/tasks/:id         --> aiops/control_plane/internal/transport/http.(*Server).handleDeleteTask-fm (3 handlers)
[GIN-debug] POST   /api/v1/tasks/:id/enable  --> aiops/control_plane/internal/transport/http.(*Server).handleEnableTask-fm (3 handlers)
[GIN-debug] POST   /api/v1/tasks/:id/disable --> aiops/control_plane/internal/transport/http.(*Server).handleDisableTask-fm (3 handlers)
[GIN-debug] GET    /api/v1/metrics/device/:deviceId --> aiops/control_plane/internal/transport/http.(*Server).handleGetDeviceMetrics-fm (3 handlers)
[GIN-debug] GET    /api/v1/metrics/device/:deviceId/key/:key --> aiops/control_plane/internal/transport/http.(*Server).handleGetMetricByKey-fm (3 handlers)
[GIN-debug] GET    /api/v1/metrics/device/:deviceId/latest --> aiops/control_plane/internal/transport/http.(*Server).handleGetLatestDeviceMetrics-fm (3 handlers)
[GIN-debug] GET    /api/v1/supported-metrics --> aiops/control_plane/internal/transport/http.(*Server).handleGetAllSupportedMetrics-fm (3 handlers)
[GIN-debug] POST   /api/v1/supported-metrics --> aiops/control_plane/internal/transport/http.(*Server).handleCreateSupportedMetric-fm (3 handlers)
[GIN-debug] GET    /api/v1/supported-metrics/:id --> aiops/control_plane/internal/transport/http.(*Server).handleGetSupportedMetric-fm (3 handlers)
[GIN-debug] PUT    /api/v1/supported-metrics/:id --> aiops/control_plane/internal/transport/http.(*Server).handleUpdateSupportedMetric-fm (3 handlers)
[GIN-debug] DELETE /api/v1/supported-metrics/:id --> aiops/control_plane/internal/transport/http.(*Server).handleDeleteSupportedMetric-fm (3 handlers)
[GIN-debug] GET    /api/v1/supported-metrics/collector/:type --> aiops/control_plane/internal/transport/http.(*Server).handleGetSupportedMetricsByCollectorType-fm (3 handlers)
[GIN-debug] GET    /api/v1/logs/device/:deviceId --> aiops/control_plane/internal/transport/http.(*Server).handleGetDeviceLogs-fm (3 handlers)
[GIN-debug] GET    /api/v1/logs              --> aiops/control_plane/internal/transport/http.(*Server).handleGetLogs-fm (3 handlers)
[GIN-debug] GET    /api/v1/logs/sources      --> aiops/control_plane/internal/transport/http.(*Server).handleGetLogSources-fm (3 handlers)
[GIN-debug] DELETE /api/v1/logs/cleanup      --> aiops/control_plane/internal/transport/http.(*Server).handleCleanupOldLogs-fm (4 handlers)
[GIN-debug] GET    /api/v1/alerts/rules      --> aiops/control_plane/internal/transport/http.(*Server).handleGetAllAlertRules-fm (3 handlers)
[GIN-debug] POST   /api/v1/alerts/rules      --> aiops/control_plane/internal/transport/http.(*Server).handleCreateAlertRule-fm (3 handlers)
[GIN-debug] GET    /api/v1/alerts/rules/:id  --> aiops/control_plane/internal/transport/http.(*Server).handleGetAlertRule-fm (3 handlers)
[GIN-debug] PUT    /api/v1/alerts/rules/:id  --> aiops/control_plane/internal/transport/http.(*Server).handleUpdateAlertRule-fm (3 handlers)
[GIN-debug] DELETE /api/v1/alerts/rules/:id  --> aiops/control_plane/internal/transport/http.(*Server).handleDeleteAlertRule-fm (3 handlers)
[GIN-debug] GET    /api/v1/alerts/events     --> aiops/control_plane/internal/transport/http.(*Server).handleGetAlertEvents-fm (3 handlers)
[GIN-debug] GET    /api/v1/alerts/events/active --> aiops/control_plane/internal/transport/http.(*Server).handleGetActiveAlerts-fm (3 handlers)
[GIN-debug] POST   /api/v1/alerts/events/:id/resolve --> aiops/control_plane/internal/transport/http.(*Server).handleResolveAlert-fm (3 handlers)
[GIN-debug] GET    /api/v1/users             --> aiops/control_plane/internal/transport/http.(*Server).handleGetAllUsers-fm (4 handlers)
[GIN-debug] POST   /api/v1/users             --> aiops/control_plane/internal/transport/http.(*Server).handleCreateUser-fm (4 handlers)
[GIN-debug] GET    /api/v1/users/:id         --> aiops/control_plane/internal/transport/http.(*Server).handleGetUser-fm (4 handlers)
[GIN-debug] PUT    /api/v1/users/:id         --> aiops/control_plane/internal/transport/http.(*Server).handleUpdateUser-fm (4 handlers)
[GIN-debug] DELETE /api/v1/users/:id         --> aiops/control_plane/internal/transport/http.(*Server).handleDeleteUser-fm (4 handlers)
[GIN-debug] POST   /api/v1/users/:id/password --> aiops/control_plane/internal/transport/http.(*Server).handleChangePassword-fm (4 handlers)
[GIN-debug] GET    /api/v1/plugins/registry  --> aiops/control_plane/internal/transport/http.(*PluginDistributionHandler).ListRegisteredPlugins-fm (3 handlers)
[GIN-debug] POST   /api/v1/plugins/registry  --> aiops/control_plane/internal/transport/http.(*PluginDistributionHandler).RegisterPlugin-fm (3 handlers)
[GIN-debug] GET    /api/v1/plugins/registry/:id --> aiops/control_plane/internal/transport/http.(*PluginDistributionHandler).GetRegisteredPlugin-fm (3 handlers)
[GIN-debug] DELETE /api/v1/plugins/registry/:id --> aiops/control_plane/internal/transport/http.(*PluginDistributionHandler).UnregisterPlugin-fm (3 handlers)
[GIN-debug] GET    /api/v1/plugins/distribution/status --> aiops/control_plane/internal/transport/http.(*PluginDistributionHandler).GetDistributionStatus-fm (3 handlers)
[GIN-debug] GET    /api/v1/plugins/distribution/agents/:agentId --> aiops/control_plane/internal/transport/http.(*PluginDistributionHandler).GetAgentPlugins-fm (3 handlers)
[GIN-debug] GET    /api/v1/plugins/distribution/health --> aiops/control_plane/internal/transport/http.(*PluginDistributionHandler).CheckDistributionHealth-fm (3 handlers)
2025-05-26 18:56:19.182695000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:83	启动 HTTP API 服务器	{"监听地址": "0.0.0.0:8080"}
2025-05-26 18:56:19.182726000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:114	DevInsight Control Plane 已启动
2025-05-26 18:56:19.182744000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:115	HTTP 服务监听于:	{"地址": "http://localhost:8080"}
2025-05-26 18:56:19.182753000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:116	gRPC 服务监听于:	{"地址": "localhost:50051"}
2025-05-26 18:56:19.182763000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:117	按 Ctrl+C 退出
2025-05-26 18:56:21.474006000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:103	接收到 Agent 注册请求	{"agentID": "7623a7b2-bd99-4537-8606-c6251fdecbd9", "IP": "*************"}
2025-05-26 18:56:21.474040000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:51	注册 Agent: 	{"AgentID": "7623a7b2-bd99-4537-8606-c6251fdecbd9", "IP": "*************", "支持的采集器类型": ["system", "mysql"]}
2025-05-26 18:56:21.474221000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:42	trace	{"error": "record not found", "elapsed": "0.152ms", "rows": 0, "sql": "SELECT * FROM `agents` WHERE agent_id = \"7623a7b2-bd99-4537-8606-c6251fdecbd9\" AND `agents`.`deleted_at` IS NULL ORDER BY `agents`.`id` LIMIT 1"}
aiops/control_plane/internal/repository.(*agentRepositoryImpl).GetAgentByID
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/repository/agent_repo.go:42
aiops/control_plane/internal/service.(*AgentService).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:55
aiops/control_plane/internal/transport/grpc.(*Server).RegisterAgent
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:104
aiops/pkg/proto._AgentService_RegisterAgent_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:228
google.golang.org/grpc.(*Server).processUnaryRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1405
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1815
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-26 18:56:21.474507000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:74	新 Agent 注册成功	{"agentID": "7623a7b2-bd99-4537-8606-c6251fdecbd9"}
2025-05-26 18:56:21.474830000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:50	收到插件请求	{"agent_id": "7623a7b2-bd99-4537-8606-c6251fdecbd9", "plugin_name": "system-collector", "plugin_version": "", "device_type": "system", "architecture": "arm64", "os": "darwin"}
2025-05-26 18:56:21.496504000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:370	记录插件分发事件	{"agent_id": "7623a7b2-bd99-4537-8606-c6251fdecbd9", "plugin_name": "system-collector", "version": "1.0.0", "status": "success"}
2025-05-26 18:56:21.534950000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:50	收到插件请求	{"agent_id": "7623a7b2-bd99-4537-8606-c6251fdecbd9", "plugin_name": "mysql-collector", "plugin_version": "", "device_type": "mysql", "architecture": "arm64", "os": "darwin"}
2025-05-26 18:56:21.554932000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_registry_service.go:370	记录插件分发事件	{"agent_id": "7623a7b2-bd99-4537-8606-c6251fdecbd9", "plugin_name": "mysql-collector", "version": "1.0.0", "status": "success"}
2025-05-26 18:56:21.592886000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:139	建立插件更新推送流连接
2025-05-26 18:56:21.592921000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:129	Agent 连接到任务流	{"agentID": "7623a7b2-bd99-4537-8606-c6251fdecbd9"}
2025-05-26 18:56:21.593105000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:160	Agent 流注册成功	{"agentID": "7623a7b2-bd99-4537-8606-c6251fdecbd9"}
2025-05-26 18:56:21.593862000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/agent_service.go:207	更新任务状态	{"agentID": "7623a7b2-bd99-4537-8606-c6251fdecbd9", "taskID": "7623a7b2-bd99-4537-8606-c6251fdecbd9", "状态": "connected"}
2025-05-26 18:56:31.594723000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:278	Agent 连接到日志数据流	{"agentID": "7623a7b2-bd99-4537-8606-c6251fdecbd9"}
2025-05-26 18:56:39.243827000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:125	正在关闭服务...
2025-05-26 18:56:39.243935000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:97	正在停止 HTTP API 服务器
2025-05-26 18:56:39.244095000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/http/server.go:106	HTTP API 服务器已停止
2025-05-26 18:56:39.244157000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:96	正在停止 gRPC 服务器
2025-05-26 18:56:39.244223000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:228	接收指标数据失败	{"error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamMetricData
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:228
aiops/pkg/proto._AgentService_StreamMetricData_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:248
google.golang.org/grpc.(*Server).processStreamingRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1695
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1819
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-26 18:56:39.244344000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:199	stream context 已完成	{"agentID": "7623a7b2-bd99-4537-8606-c6251fdecbd9"}
2025-05-26 18:56:39.244355000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/service/plugin_distribution_service.go:146	插件更新推送流连接断开
2025-05-26 18:56:39.244369000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:162	接收任务状态失败	{"agentID": "7623a7b2-bd99-4537-8606-c6251fdecbd9", "error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamCollectorTasks.func1
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:162
2025-05-26 18:56:39.244252000	[31merror[0m	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:271	接收日志数据失败	{"error": "rpc error: code = Canceled desc = context canceled"}
aiops/control_plane/internal/transport/grpc.(*Server).StreamLogData
	/Volumes/data/Code/Go/src/aiops/control_plane/internal/transport/grpc/server.go:271
aiops/pkg/proto._AgentService_StreamLogData_Handler
	/Volumes/data/Code/Go/src/aiops/pkg/proto/devinsight_grpc.pb.go:255
google.golang.org/grpc.(*Server).processStreamingRPC
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1695
google.golang.org/grpc.(*Server).handleStream
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1819
google.golang.org/grpc.(*Server).serveStreams.func2.1
	/Volumes/data/Code/Go/pkg/mod/google.golang.org/grpc@v1.72.1/server.go:1035
2025-05-26 18:56:39.256299000	[34minfo[0m	/Volumes/data/Code/Go/src/aiops/control_plane/cmd/main.go:134	DevInsight Control Plane 已关闭
