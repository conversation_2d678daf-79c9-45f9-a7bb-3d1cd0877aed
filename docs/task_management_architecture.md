# 新任务管理系统架构文档

## 概述

新的任务管理系统已经成功实现，支持多种类型的插件协调和任务执行。系统设计采用了统一的任务管理框架，能够处理采集、分析、警报和处理等不同类型的任务。

## 核心组件

### 1. TaskManager (任务管理器)
- **位置**: `/agent/internal/task_manager.go`
- **功能**: 统一的任务管理和调度
- **特性**:
  - 支持多种任务类型 (CollectionTask, AnalysisTask, AlertingTask, ProcessingTask)
  - 工作器池异步执行
  - 任务状态回调机制
  - 向后兼容原有接口

### 2. TaskExecutors (任务执行器)
- **位置**: `/agent/internal/task_executors.go`
- **功能**: 专门的任务执行逻辑
- **特性**:
  - 针对每种任务类型的专门执行器
  - 插件协调和数据流管理
  - 阈值检查和警报触发

### 3. TaskProcessors (任务处理器)
- **位置**: `/agent/internal/task_processors.go`
- **功能**: 数据处理和实时分析
- **特性**:
  - 批处理指标数据
  - 实时异常检测
  - 分析结果处理

### 4. PluginCoordinator (插件协调器)
- **位置**: `/agent/internal/plugin_coordinator.go`
- **功能**: 插件间协调和规则执行
- **特性**:
  - 分析链和警报链管理
  - 协调规则配置
  - 插件间数据传递

## 任务类型详解

### 1. 采集任务 (CollectionTask)
```go
type TaskConfig struct {
    ID:       "collection_task_001"
    Type:     CollectionTask
    Priority: 5
    Config: map[string]interface{}{
        "device_name":       "server-001",
        "frequency_seconds": 30,
        "collect_items":     []string{"cpu_usage", "memory_usage"},
    }
}
```

### 2. 分析任务 (AnalysisTask)
```go
type TaskConfig struct {
    ID:   "analysis_task_001"
    Type: AnalysisTask
    AnalysisConfig: &AnalysisTaskConfig{
        AnalyzerPluginID: "enhanced-analyzer",
        DataSources:      []string{"collection_task_001"},
        Thresholds: map[string]float64{
            "cpu_usage": 80.0,
        },
        AlerterConfigs: []AlerterConfig{...},
    }
}
```

### 3. 警报任务 (AlertingTask)
```go
type TaskConfig struct {
    ID:   "alerting_task_001"
    Type: AlertingTask
    AlertingConfig: &AlertingTaskConfig{
        AlerterPluginID: "email-alerter",
        Rules: []AlertRule{
            {
                Metric:    "cpu_usage",
                Operator:  ">",
                Threshold: 90.0,
                Severity:  "critical",
            },
        },
    }
}
```

### 4. 处理任务 (ProcessingTask)
```go
type TaskConfig struct {
    ID:   "processing_task_001"
    Type: ProcessingTask
    Config: map[string]interface{}{
        "processor_type": "data_enricher",
        "input_sources":  []string{"collection_task_001"},
    }
}
```

## 插件协调机制

### 分析链 (AnalysisChain)
分析链定义了一系列分析器、警报器和处理器的执行顺序：

```go
type AnalysisChain struct {
    ID:         "comprehensive_analysis"
    Analyzers:  []string{"enhanced-analyzer", "simple-analyzer"}
    Alerters:   []string{"email-alerter"}
    Processors: []string{"data_enricher"}
    Config:     map[string]interface{}{
        "parallel_execution": true,
        "timeout_seconds": 60,
    }
}
```

### 警报链 (AlertingChain)
警报链支持多级警报升级：

```go
type AlertingChain struct {
    ID:       "escalation_chain"
    Alerters: []string{"email-alerter", "slack-alerter", "pager-duty-alerter"}
    Rules: []CoordinationRule{
        {
            Severity:      "warning",
            DelaySeconds: 0,
            Targets:      []string{"email-alerter"},
        },
        {
            Severity:      "critical",
            DelaySeconds: 900,
            Targets:      []string{"email-alerter", "slack-alerter", "pager-duty-alerter"},
        },
    }
}
```

### 协调规则 (CoordinationRule)
协调规则定义了插件间的触发条件：

```go
type CoordinationRule struct {
    ID:          "analysis_to_alert_chain"
    TriggerType: "analysis_result"
    Conditions: []RuleCondition{
        {
            Field:    "anomaly_score",
            Operator: ">",
            Value:    0.8,
        },
    }
    Actions: []RuleAction{
        {
            Type:         "trigger_alerter",
            TargetPlugin: "email-alerter",
            Config: map[string]interface{}{
                "severity": "warning",
            },
        },
    }
}
```

## 数据流处理

### 指标数据流
1. **采集** → CollectorPlugin 收集指标数据
2. **传输** → MetricDataChannel 传输到处理器
3. **批处理** → MetricDataProcessor 批量处理
4. **分析** → AnalyzerPlugin 执行分析
5. **警报** → AlerterPlugin 触发警报

### 分析结果流
1. **分析** → AnalyzerPlugin 产生分析结果
2. **传输** → AnalysisResultChannel 传输结果
3. **处理** → AnalysisResultProcessor 处理结果
4. **协调** → PluginCoordinator 执行协调规则
5. **响应** → 触发相应的警报或处理动作

## API 接口

### TaskManager 主要方法
```go
// 启动和停止
func (tm *TaskManager) Start(ctx context.Context) error
func (tm *TaskManager) Stop() error

// 任务管理
func (tm *TaskManager) SubmitTask(taskConfig *TaskConfig) error
func (tm *TaskManager) CancelTask(taskID string) error
func (tm *TaskManager) GetTaskStatus(taskID string) (*TaskExecutionContext, bool)
func (tm *TaskManager) ListActiveTasks() map[string]*TaskExecutionContext

// 兼容接口
func (tm *TaskManager) StartCollector(taskConfig *pb.CollectorTaskConfig, statusCallback StatusCallback) error
func (tm *TaskManager) StopCollector(taskID string) error

// 数据通道
func (tm *TaskManager) GetMetricDataChannel() <-chan *pb.MetricData
func (tm *TaskManager) GetAnalysisResultChannel() <-chan *AnalysisResult
func (tm *TaskManager) GetAlertEventChannel() <-chan *plugininterface.AlertEvent

// 状态回调
func (tm *TaskManager) SetStatusCallback(callback StatusCallback)
```

## 配置示例

完整的配置示例请参考 `/config/task_examples.yaml`，包含：
- 采集任务配置
- 分析任务配置
- 警报任务配置
- 处理任务配置
- 协调规则配置
- 插件链配置

## 测试和验证

### 1. 编译测试
```bash
cd /Volumes/data/Code/Go/src/aiops
go build ./...
```

### 2. 功能测试
```bash
./test_task_management.sh
```

### 3. 示例程序
```bash
go run examples/task_management_example.go
```

## 系统特性总结

### ✅ 已完成功能
1. **统一任务管理框架** - 支持多种任务类型的统一管理
2. **插件协调机制** - 实现分析器和警报器之间的协调
3. **实时数据处理** - 批处理和实时异常检测
4. **阈值检查和警报** - 自动阈值检查和警报触发
5. **任务链支持** - 分析链和警报链的执行
6. **向后兼容** - 保持与原有接口的兼容性
7. **状态管理** - 完整的任务状态跟踪和回调

### 🚀 系统优势
- **可扩展性**: 易于添加新的任务类型和插件
- **灵活性**: 支持复杂的任务协调和数据流
- **可靠性**: 完善的错误处理和状态管理
- **性能**: 异步执行和批处理优化
- **易用性**: 简单的API和丰富的配置选项

### 📋 后续优化方向
1. 添加任务调度策略（优先级、依赖关系）
2. 实现任务持久化和恢复
3. 添加更多的协调规则类型
4. 实现插件热更新支持
5. 添加任务执行指标和监控
6. 实现分布式任务管理

## 使用指南

1. **创建任务**: 使用 `SubmitTask()` 方法提交任务配置
2. **监控状态**: 通过状态回调或 `GetTaskStatus()` 监控任务状态
3. **处理数据**: 监听数据通道获取实时结果
4. **配置协调**: 使用配置文件定义插件协调规则
5. **管理任务**: 使用 `CancelTask()` 和 `ListActiveTasks()` 管理任务

系统现在已经可以完整支持多种类型的插件协调和任务管理需求！
