package main

import (
	"flag"
	"os"
	"os/signal"
	"syscall"

	"aiops/agent/internal/config"
	"aiops/agent/internal/pipeline"
	cf "aiops/pkg/config"
	"aiops/pkg/log"

	"go.uber.org/zap"
)

var (
	serverAddr = flag.String("server", "localhost:50051", "控制平面服务器地址")
	agentID    = flag.String("agent-id", "", "代理ID，如果为空则自动生成")
	pluginsDir = flag.String("plugins-dir", "plugins/build", "插件目录路径")
)

func main() {
	flag.Parse()

	// 加载配置
	cfg := config.New()
	if *agentID != "" {
		cfg.AgentID = *agentID
	}
	logConfig := cf.NewConfig("config/agent_config.yaml")

	// 初始化日志
	logger := log.NewLog(logConfig)
	logger.Info("DevInsight Agent 启动中...",
		zap.String("agent_id", cfg.AgentID),
		zap.String("version", "2.0.0-pipeline"))

	// 使用配置中的服务器地址，如果命令行指定了则优先使用命令行参数
	serverAddress := cfg.Connection.ServerAddr
	if *serverAddr != "localhost:50051" {
		serverAddress = *serverAddr
	}

	logger.Info("控制平面地址:", zap.String("server_address", serverAddress))

	// 创建插件管理器
	pluginManager := pipeline.NewPluginManager(*pluginsDir, logger.Logger)
	if err := pluginManager.Initialize(); err != nil {
		logger.Error("初始化插件管理器失败:", zap.Error(err))
		// 继续运行，可能有内置插件可用
	}

	// 创建Control Plane客户端
	controlPlaneClient := pipeline.NewControlPlaneClient(serverAddress, cfg.AgentID, logger.Logger)
	if err := controlPlaneClient.Connect(); err != nil {
		logger.Error("连接控制平面失败:", zap.Error(err))
		return
	}
	defer controlPlaneClient.Disconnect()

	// 创建流水线管理器
	pipelineManager := pipeline.NewManager(cfg.AgentID, logger.Logger)
	pipelineManager.SetControlPlaneClient(controlPlaneClient)
	pipelineManager.SetPluginManager(pluginManager)

	// 启动流水线管理器
	if err := pipelineManager.Start(); err != nil {
		logger.Error("启动流水线管理器失败:", zap.Error(err))
		return
	}
	defer pipelineManager.Stop()

	// 启动心跳
	controlPlaneClient.StartHeartbeat()

	logger.Info("DevInsight Agent 启动完成，等待流水线配置...")

	// 等待退出信号
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)

	<-sigCh
	logger.Info("接收到退出信号，正在关闭 Agent...")

	// 优雅关闭
	logger.Info("DevInsight Agent 已关闭")
}
