package internal

import (
	"context"
	"fmt"
	"sync"
	"time"

	"aiops/pkg/log"
	pb "aiops/pkg/proto"

	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

// ConnectionConfig 连接配置
type ConnectionConfig struct {
	ServerAddr          string        `yaml:"server_addr"`           // 服务器地址
	InitialBackoff      time.Duration `yaml:"initial_backoff"`       // 初始退避时间
	MaxBackoff          time.Duration `yaml:"max_backoff"`           // 最大退避时间
	BackoffMultiplier   float64       `yaml:"backoff_multiplier"`    // 退避倍数
	MaxRetries          int           `yaml:"max_retries"`           // 最大重试次数(-1表示无限重试)
	HealthCheckInterval time.Duration `yaml:"health_check_interval"` // 健康检查间隔
	ConnectTimeout      time.Duration `yaml:"connect_timeout"`       // 连接超时
}

// DefaultConnectionConfig 默认连接配置
func DefaultConnectionConfig() ConnectionConfig {
	return ConnectionConfig{
		ServerAddr:          "localhost:50051",
		InitialBackoff:      time.Second,
		MaxBackoff:          time.Minute * 5,
		BackoffMultiplier:   2.0,
		MaxRetries:          -1, // 无限重试
		HealthCheckInterval: time.Second * 30,
		ConnectTimeout:      time.Second * 10,
	}
}

// ConnectionState 连接状态
type ConnectionState int

const (
	Disconnected ConnectionState = iota
	Connecting
	Connected
	Reconnecting
)

func (cs ConnectionState) String() string {
	switch cs {
	case Disconnected:
		return "Disconnected"
	case Connecting:
		return "Connecting"
	case Connected:
		return "Connected"
	case Reconnecting:
		return "Reconnecting"
	default:
		return "Unknown"
	}
}

// ConnectionEvent 连接事件
type ConnectionEvent struct {
	State     ConnectionState
	Error     error
	Timestamp time.Time
}

// ConnectionManager 连接管理器
type ConnectionManager struct {
	config     ConnectionConfig
	logger     *log.Logger
	conn       *grpc.ClientConn
	client     pb.AgentServiceClient
	state      ConnectionState
	stateMutex sync.RWMutex

	// 重连机制
	retryCount  int
	lastAttempt time.Time

	// 事件通道
	eventCh chan ConnectionEvent

	// 控制
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
}

// NewConnectionManager 创建连接管理器
func NewConnectionManager(config ConnectionConfig, logger *log.Logger) *ConnectionManager {
	ctx, cancel := context.WithCancel(context.Background())

	return &ConnectionManager{
		config:  config,
		logger:  logger,
		state:   Disconnected,
		eventCh: make(chan ConnectionEvent, 100),
		ctx:     ctx,
		cancel:  cancel,
	}
}

// Connect 建立连接
func (cm *ConnectionManager) Connect() error {
	cm.stateMutex.Lock()
	defer cm.stateMutex.Unlock()

	if cm.state == Connected {
		return nil
	}

	cm.setState(Connecting)

	// 创建连接
	conn, err := cm.createConnection()
	if err != nil {
		cm.setState(Disconnected)
		return fmt.Errorf("创建连接失败: %w", err)
	}

	cm.conn = conn
	cm.client = pb.NewAgentServiceClient(conn)
	cm.setState(Connected)
	cm.retryCount = 0

	// 启动健康检查
	cm.wg.Add(1)
	go cm.healthCheckLoop()

	cm.logger.Info("连接已建立", zap.String("server", cm.config.ServerAddr))

	return nil
}

// createConnection 创建gRPC连接
func (cm *ConnectionManager) createConnection() (*grpc.ClientConn, error) {
	ctx, cancel := context.WithTimeout(cm.ctx, cm.config.ConnectTimeout)
	defer cancel()

	// 设置最大消息大小为 50MB 以支持插件下载
	maxMsgSize := 50 * 1024 * 1024 // 50MB

	// 使用 DialContext，但移除已弃用的 WithBlock 选项
	conn, err := grpc.DialContext(
		ctx,
		cm.config.ServerAddr,
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithDefaultCallOptions(
			grpc.MaxCallRecvMsgSize(maxMsgSize),
			grpc.MaxCallSendMsgSize(maxMsgSize),
		),
	)
	if err != nil {
		return nil, fmt.Errorf("连接失败: %w", err)
	}

	// 手动等待连接就绪（替代 WithBlock 的功能）
	for {
		state := conn.GetState()
		if state.String() == "READY" {
			break
		}
		if state.String() == "TRANSIENT_FAILURE" || state.String() == "SHUTDOWN" {
			conn.Close()
			return nil, fmt.Errorf("连接失败，状态: %s", state.String())
		}

		// 等待状态变化或超时
		if !conn.WaitForStateChange(ctx, state) {
			conn.Close()
			return nil, fmt.Errorf("连接超时")
		}

		select {
		case <-ctx.Done():
			conn.Close()
			return nil, fmt.Errorf("连接超时: %w", ctx.Err())
		default:
		}
	}

	return conn, nil
}

// GetClient 获取gRPC客户端
func (cm *ConnectionManager) GetClient() (pb.AgentServiceClient, error) {
	cm.stateMutex.RLock()
	defer cm.stateMutex.RUnlock()

	if cm.state != Connected {
		return nil, fmt.Errorf("连接未建立，当前状态: %s", cm.state)
	}

	return cm.client, nil
}

// GetConnection 获取gRPC连接
func (cm *ConnectionManager) GetConnection() (*grpc.ClientConn, error) {
	cm.stateMutex.RLock()
	defer cm.stateMutex.RUnlock()

	if cm.state != Connected {
		return nil, fmt.Errorf("连接未建立，当前状态: %s", cm.state)
	}

	return cm.conn, nil
}

// GetState 获取连接状态
func (cm *ConnectionManager) GetState() ConnectionState {
	cm.stateMutex.RLock()
	defer cm.stateMutex.RUnlock()
	return cm.state
}

// setState 设置连接状态并发送事件
func (cm *ConnectionManager) setState(newState ConnectionState) {
	oldState := cm.state
	cm.state = newState

	if oldState != newState {
		event := ConnectionEvent{
			State:     newState,
			Timestamp: time.Now(),
		}

		select {
		case cm.eventCh <- event:
		default:
			// 事件通道已满，丢弃旧事件
		}

		cm.logger.Info("连接状态变更",
			zap.String("old_state", oldState.String()),
			zap.String("new_state", newState.String()))
	}
}

// healthCheckLoop 健康检查循环
func (cm *ConnectionManager) healthCheckLoop() {
	defer cm.wg.Done()

	ticker := time.NewTicker(cm.config.HealthCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if err := cm.checkHealth(); err != nil {
				cm.logger.Warn("健康检查失败，开始重连", zap.Error(err))
				cm.startReconnect()
				return
			}
		case <-cm.ctx.Done():
			return
		}
	}
}

// checkHealth 检查连接健康状态
func (cm *ConnectionManager) checkHealth() error {
	cm.stateMutex.RLock()
	conn := cm.conn
	cm.stateMutex.RUnlock()

	if conn == nil {
		return fmt.Errorf("连接为空")
	}

	state := conn.GetState()
	if state.String() == "READY" {
		return nil
	}

	return fmt.Errorf("连接状态异常: %s", state.String())
}

// startReconnect 开始重连流程
func (cm *ConnectionManager) startReconnect() {
	cm.stateMutex.Lock()
	defer cm.stateMutex.Unlock()

	if cm.state == Reconnecting {
		return // 已在重连中
	}

	cm.setState(Reconnecting)

	// 关闭当前连接
	if cm.conn != nil {
		cm.conn.Close()
		cm.conn = nil
		cm.client = nil
	}

	// 启动重连协程
	cm.wg.Add(1)
	go cm.reconnectLoop()
}

// reconnectLoop 重连循环
func (cm *ConnectionManager) reconnectLoop() {
	defer cm.wg.Done()

	backoff := cm.config.InitialBackoff

	for {
		select {
		case <-cm.ctx.Done():
			return
		default:
		}

		// 检查重试次数限制
		if cm.config.MaxRetries > 0 && cm.retryCount >= cm.config.MaxRetries {
			cm.logger.Error("重连失败，已达到最大重试次数",
				zap.Int("max_retries", cm.config.MaxRetries))
			cm.stateMutex.Lock()
			cm.setState(Disconnected)
			cm.stateMutex.Unlock()
			return
		}

		cm.retryCount++
		cm.lastAttempt = time.Now()

		cm.logger.Info("尝试重连",
			zap.Int("attempt", cm.retryCount),
			zap.String("server", cm.config.ServerAddr))

		// 创建新连接
		conn, err := cm.createConnection()
		if err != nil {
			cm.logger.Warn("重连失败，等待重试",
				zap.Error(err),
				zap.Duration("backoff", backoff))

			// 等待退避时间
			select {
			case <-time.After(backoff):
			case <-cm.ctx.Done():
				return
			}

			// 增加退避时间
			backoff = time.Duration(float64(backoff) * cm.config.BackoffMultiplier)
			if backoff > cm.config.MaxBackoff {
				backoff = cm.config.MaxBackoff
			}

			continue
		}

		// 连接成功
		cm.stateMutex.Lock()
		cm.conn = conn
		cm.client = pb.NewAgentServiceClient(conn)
		cm.setState(Connected)
		cm.retryCount = 0
		cm.stateMutex.Unlock()

		cm.logger.Info("重连成功", zap.String("server", cm.config.ServerAddr))

		// 重新启动健康检查
		cm.wg.Add(1)
		go cm.healthCheckLoop()

		return
	}
}

// GetEventChannel 获取事件通道
func (cm *ConnectionManager) GetEventChannel() <-chan ConnectionEvent {
	return cm.eventCh
}

// Close 关闭连接管理器
func (cm *ConnectionManager) Close() {
	cm.cancel()

	// 关闭连接
	cm.stateMutex.Lock()
	if cm.conn != nil {
		cm.conn.Close()
		cm.conn = nil
		cm.client = nil
	}
	cm.setState(Disconnected)
	cm.stateMutex.Unlock()

	// 等待所有协程结束
	cm.wg.Wait()

	// 关闭事件通道
	close(cm.eventCh)

	cm.logger.Info("连接管理器已关闭")
}

// IsConnected 检查是否已连接
func (cm *ConnectionManager) IsConnected() bool {
	return cm.GetState() == Connected
}

// GetStats 获取连接统计信息
func (cm *ConnectionManager) GetStats() map[string]interface{} {
	cm.stateMutex.RLock()
	defer cm.stateMutex.RUnlock()

	return map[string]interface{}{
		"state":                 cm.state.String(),
		"retry_count":           cm.retryCount,
		"last_attempt":          cm.lastAttempt,
		"server_addr":           cm.config.ServerAddr,
		"health_check_interval": cm.config.HealthCheckInterval,
	}
}
