package internal

import (
	"context"
	"fmt"
	"strconv"
	"time"

	pb "aiops/pkg/proto"
	plugininterface "aiops/plugins/interface"

	"go.uber.org/zap"
)

// executeCollectionTask 执行采集任务
func (tm *TaskManager) executeCollectionTask(ctx context.Context, taskConfig *TaskConfig, execCtx *TaskExecutionContext) error {
	// 提取设备类型
	deviceType, ok := taskConfig.Config["device_type"].(string)
	if !ok {
		return fmt.Errorf("device_type not found in task config")
	}

	// 确保插件已加载
	if err := tm.pluginManager.EnsurePluginLoaded(deviceType); err != nil {
		return fmt.Errorf("failed to load plugin for device type %s: %w", deviceType, err)
	}

	// 获取采集器插件
	collector, err := tm.pluginManager.GetCollectorPlugin(deviceType)
	if err != nil {
		return fmt.Errorf("failed to get collector plugin: %w", err)
	}

	// 构建采集器任务配置
	collectorTaskConfig := &pb.CollectorTaskConfig{
		TaskId:           taskConfig.ID,
		DeviceId:         getStringFromConfig(taskConfig.Config, "device_id"),
		DeviceName:       getStringFromConfig(taskConfig.Config, "device_name"),
		DeviceType:       deviceType,
		Host:             getStringFromConfig(taskConfig.Config, "host"),
		Port:             getInt32FromConfig(taskConfig.Config, "port"),
		Username:         getStringFromConfig(taskConfig.Config, "username"),
		Password:         getStringFromConfig(taskConfig.Config, "password"),
		ConnectParams:    getStringMapFromConfig(taskConfig.Config, "connect_params"),
		FrequencySeconds: getInt64FromConfig(taskConfig.Config, "frequency_seconds"),
		CollectItems:     getStringSliceFromConfig(taskConfig.Config, "collect_items"),
		IsEnabled:        getBoolFromConfig(taskConfig.Config, "is_enabled"),
	}

	// 启动连续采集
	frequency := time.Duration(collectorTaskConfig.FrequencySeconds) * time.Second
	if frequency == 0 {
		frequency = 30 * time.Second // 默认30秒
	}

	ticker := time.NewTicker(frequency)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-ticker.C:
			// 执行单次采集
			metrics, err := collector.CollectMetrics(ctx, collectorTaskConfig)
			if err != nil {
				tm.logger.Error("Failed to collect metrics",
					zap.String("task_id", taskConfig.ID),
					zap.Error(err))
				continue
			}

			// 发送指标数据到处理通道
			for _, metric := range metrics {
				select {
				case tm.metricDataCh <- metric:
				case <-ctx.Done():
					return ctx.Err()
				default:
					tm.logger.Warn("Metric data channel is full, dropping metric",
						zap.String("task_id", taskConfig.ID))
				}
			}

			tm.logger.Debug("Collected metrics",
				zap.String("task_id", taskConfig.ID),
				zap.Int("metric_count", len(metrics)))
		}
	}
}

// executeAnalysisTask 执行分析任务
func (tm *TaskManager) executeAnalysisTask(ctx context.Context, taskConfig *TaskConfig, execCtx *TaskExecutionContext) error {
	analysisConfig := taskConfig.AnalysisConfig
	if analysisConfig == nil {
		return fmt.Errorf("analysis config is required for analysis task")
	}

	// 获取分析器插件
	analyzer, err := tm.getAnalyzerPlugin(analysisConfig.AnalyzerPluginID)
	if err != nil {
		return fmt.Errorf("failed to get analyzer plugin: %w", err)
	}

	// 创建分析数据收集器
	dataCollector := make([]*pb.MetricData, 0)

	// 收集分析数据（这里简化为从内存收集，实际应该从数据源收集）
	ticker := time.NewTicker(10 * time.Second) // 每10秒分析一次
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case metricData := <-tm.metricDataCh:
			// 收集指标数据
			if tm.shouldAnalyzeMetric(metricData, analysisConfig.DataSources) {
				dataCollector = append(dataCollector, metricData)
			}
		case <-ticker.C:
			if len(dataCollector) > 0 {
				// 执行分析
				result, err := analyzer.AnalyzeMetrics(ctx, dataCollector)
				if err != nil {
					tm.logger.Error("Analysis failed",
						zap.String("task_id", taskConfig.ID),
						zap.Error(err))
					continue
				}

				// 检查阈值并触发警报
				analysisResult := tm.processAnalysisResult(taskConfig.ID, result, analysisConfig)

				// 发送分析结果
				select {
				case tm.analysisResultCh <- analysisResult:
				case <-ctx.Done():
					return ctx.Err()
				default:
					tm.logger.Warn("Analysis result channel is full",
						zap.String("task_id", taskConfig.ID))
				}

				// 清空数据收集器
				dataCollector = dataCollector[:0]
			}
		}
	}
}

// executeAlertingTask 执行警报任务
func (tm *TaskManager) executeAlertingTask(ctx context.Context, taskConfig *TaskConfig, execCtx *TaskExecutionContext) error {
	alertingConfig := taskConfig.AlertingConfig
	if alertingConfig == nil {
		return fmt.Errorf("alerting config is required for alerting task")
	}

	// 获取警报器插件
	alerter, err := tm.getAlerterPlugin(alertingConfig.AlerterPluginID)
	if err != nil {
		return fmt.Errorf("failed to get alerter plugin: %w", err)
	}

	// 监听警报事件
	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case alertEvent := <-tm.alertEventCh:
			// 评估警报规则
			if tm.shouldTriggerAlert(alertEvent, alertingConfig.Rules) {
				if err := alerter.SendAlert(ctx, alertEvent); err != nil {
					tm.logger.Error("Failed to send alert",
						zap.String("task_id", taskConfig.ID),
						zap.String("alert_id", alertEvent.ID),
						zap.Error(err))
				} else {
					tm.logger.Info("Alert sent successfully",
						zap.String("task_id", taskConfig.ID),
						zap.String("alert_id", alertEvent.ID))
				}
			}
		}
	}
}

// executeProcessingTask 执行处理任务
func (tm *TaskManager) executeProcessingTask(ctx context.Context, taskConfig *TaskConfig, execCtx *TaskExecutionContext) error {
	// 处理任务的实现（这里可以根据需要扩展）
	tm.logger.Info("Processing task executed",
		zap.String("task_id", taskConfig.ID))
	return nil
}

// processAnalysisResult 处理分析结果并检查阈值
func (tm *TaskManager) processAnalysisResult(taskID string, result *plugininterface.AnalysisResult, config *AnalysisTaskConfig) *AnalysisResult {
	analysisResult := &AnalysisResult{
		TaskID:          taskID,
		Timestamp:       time.Now(),
		Anomalies:       make([]*plugininterface.Anomaly, 0),
		Metrics:         make(map[string]float64),
		AlertsTriggered: make([]string, 0),
	}

	if result != nil {
		analysisResult.Confidence = result.Confidence
		analysisResult.Severity = result.Severity

		// 复制异常信息
		if result.Anomalies != nil {
			analysisResult.Anomalies = append(analysisResult.Anomalies, result.Anomalies...)
		}
	}

	// 检查阈值并触发警报
	for metricKey, threshold := range config.Thresholds {
		if value, exists := analysisResult.Metrics[metricKey]; exists {
			if value > threshold {
				// 创建警报事件
				alertEvent := &plugininterface.AlertEvent{
					ID:             fmt.Sprintf("alert_%s_%s_%d", taskID, metricKey, time.Now().Unix()),
					Title:          fmt.Sprintf("Threshold exceeded for %s", metricKey),
					Description:    fmt.Sprintf("Value %f exceeds threshold %f", value, threshold),
					Level:          "warning",
					MetricKey:      metricKey,
					CurrentValue:   value,
					ThresholdValue: threshold,
					Source:         "analysis_task",
					Timestamp:      time.Now(),
				}

				// 为每个配置的警报器发送警报
				for _, alerterConfig := range config.AlerterConfigs {
					tm.triggerAlert(alertEvent, alerterConfig)
					analysisResult.AlertsTriggered = append(analysisResult.AlertsTriggered, alerterConfig.PluginID)
				}
			}
		}
	}

	return analysisResult
}

// triggerAlert 触发警报
func (tm *TaskManager) triggerAlert(alertEvent *plugininterface.AlertEvent, alerterConfig AlerterConfig) {
	// 检查警报条件
	if tm.evaluateAlertConditions(alertEvent, alerterConfig.Conditions) {
		select {
		case tm.alertEventCh <- alertEvent:
			tm.logger.Info("Alert triggered",
				zap.String("alert_id", alertEvent.ID),
				zap.String("alerter_plugin", alerterConfig.PluginID))
		default:
			tm.logger.Warn("Alert event channel is full, dropping alert",
				zap.String("alert_id", alertEvent.ID))
		}
	}
}

// evaluateAlertConditions 评估警报条件
func (tm *TaskManager) evaluateAlertConditions(alertEvent *plugininterface.AlertEvent, conditions map[string]interface{}) bool {
	// 简化的条件评估逻辑
	if minSeverity, exists := conditions["min_severity"]; exists {
		if severityStr, ok := minSeverity.(string); ok {
			if !tm.compareSeverity(alertEvent.Level, severityStr) {
				return false
			}
		}
	}

	return true
}

// compareSeverity 比较严重性级别
func (tm *TaskManager) compareSeverity(current, min string) bool {
	severityLevels := map[string]int{
		"info":     1,
		"warning":  2,
		"error":    3,
		"critical": 4,
	}

	currentLevel, currentExists := severityLevels[current]
	minLevel, minExists := severityLevels[min]

	if !currentExists || !minExists {
		return false
	}

	return currentLevel >= minLevel
}

// shouldAnalyzeMetric 判断是否应该分析这个指标
func (tm *TaskManager) shouldAnalyzeMetric(metric *pb.MetricData, dataSources []string) bool {
	if len(dataSources) == 0 {
		return true // 如果没有指定数据源，分析所有指标
	}

	for _, source := range dataSources {
		if metric.DeviceId == source || metric.MetricKey == source {
			return true
		}
	}

	return false
}

// shouldTriggerAlert 判断是否应该触发警报
func (tm *TaskManager) shouldTriggerAlert(alertEvent *plugininterface.AlertEvent, rules []AlertRule) bool {
	for _, rule := range rules {
		if tm.evaluateAlertRule(alertEvent, rule) {
			return true
		}
	}
	return false
}

// evaluateAlertRule 评估警报规则
func (tm *TaskManager) evaluateAlertRule(alertEvent *plugininterface.AlertEvent, rule AlertRule) bool {
	// 简化的规则评估逻辑
	if rule.Condition == "threshold_exceeded" {
		return alertEvent.CurrentValue > rule.Threshold
	}

	if rule.Condition == "severity_match" {
		return alertEvent.Level == rule.Config["severity"]
	}

	return false
}

// 插件获取辅助函数
func (tm *TaskManager) getAnalyzerPlugin(pluginID string) (plugininterface.Analyzer, error) {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	if analyzer, exists := tm.analyzerPlugins[pluginID]; exists {
		return analyzer, nil
	}

	// 尝试从插件管理器加载
	// 这里需要根据实际的插件管理器实现来调整
	return nil, fmt.Errorf("analyzer plugin not found: %s", pluginID)
}

func (tm *TaskManager) getAlerterPlugin(pluginID string) (plugininterface.Alerter, error) {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	if alerter, exists := tm.alerterPlugins[pluginID]; exists {
		return alerter, nil
	}

	// 尝试从插件管理器加载
	return nil, fmt.Errorf("alerter plugin not found: %s", pluginID)
}

// 配置提取辅助函数
func getStringFromConfig(config map[string]interface{}, key string) string {
	if value, ok := config[key].(string); ok {
		return value
	}
	return ""
}

func getInt32FromConfig(config map[string]interface{}, key string) int32 {
	switch value := config[key].(type) {
	case int:
		return int32(value)
	case int32:
		return value
	case int64:
		return int32(value)
	case float64:
		return int32(value)
	case string:
		if intVal, err := strconv.Atoi(value); err == nil {
			return int32(intVal)
		}
	}
	return 0
}

func getInt64FromConfig(config map[string]interface{}, key string) int64 {
	switch value := config[key].(type) {
	case int:
		return int64(value)
	case int32:
		return int64(value)
	case int64:
		return value
	case float64:
		return int64(value)
	case string:
		if intVal, err := strconv.ParseInt(value, 10, 64); err == nil {
			return intVal
		}
	}
	return 0
}

func getBoolFromConfig(config map[string]interface{}, key string) bool {
	if value, ok := config[key].(bool); ok {
		return value
	}
	return false
}

func getStringMapFromConfig(config map[string]interface{}, key string) map[string]string {
	if value, ok := config[key].(map[string]string); ok {
		return value
	}
	if value, ok := config[key].(map[string]interface{}); ok {
		result := make(map[string]string)
		for k, v := range value {
			if str, ok := v.(string); ok {
				result[k] = str
			}
		}
		return result
	}
	return make(map[string]string)
}

func getStringSliceFromConfig(config map[string]interface{}, key string) []string {
	if value, ok := config[key].([]string); ok {
		return value
	}
	if value, ok := config[key].([]interface{}); ok {
		result := make([]string, 0, len(value))
		for _, v := range value {
			if str, ok := v.(string); ok {
				result = append(result, str)
			}
		}
		return result
	}
	return make([]string, 0)
}
