package internal

import (
	"context"
	"crypto/md5"
	"fmt"
	"io"
	"os"
	"runtime"
	"time"

	"aiops/pkg/proto"

	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/types/known/emptypb"
)

// PluginClient 插件gRPC客户端
type PluginClient struct {
	logger    *zap.Logger
	client    proto.AgentServiceClient
	agentID   string
	conn      *grpc.ClientConn
	pluginDir string
}

// NewPluginClient 创建新的插件客户端
func NewPluginClient(logger *zap.Logger, conn *grpc.ClientConn, agentID, pluginDir string) *PluginClient {
	return &PluginClient{
		logger:    logger,
		client:    proto.NewAgentServiceClient(conn),
		agentID:   agentID,
		conn:      conn,
		pluginDir: pluginDir,
	}
}

// RequestPlugin 向控制平面请求插件
func (c *PluginClient) RequestPlugin(ctx context.Context, pluginName, version, deviceType string) (*PluginInfo, error) {
	c.logger.Info("请求插件",
		zap.String("plugin_name", pluginName),
		zap.String("version", version),
		zap.String("device_type", deviceType))

	req := &proto.PluginRequest{
		AgentId:       c.agentID,
		PluginName:    pluginName,
		PluginVersion: version,
		DeviceType:    deviceType,
		Architecture:  runtime.GOARCH,
		Os:            runtime.GOOS,
	}

	resp, err := c.client.RequestPlugin(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("请求插件失败: %w", err)
	}

	if !resp.Success {
		return nil, fmt.Errorf("插件请求被拒绝: %s", resp.Message)
	}

	// 验证插件路径是否存在
	if resp.PluginPath == "" {
		return nil, fmt.Errorf("插件响应中没有插件路径")
	}

	// 验证插件文件是否存在
	if err := c.verifyPluginFile(resp.PluginPath, resp.Checksum); err != nil {
		return nil, fmt.Errorf("插件文件验证失败: %w", err)
	}

	return &PluginInfo{
		Name:         resp.Metadata.Name,
		Version:      resp.Metadata.Version,
		Path:         resp.PluginPath,
		AbsolutePath: resp.AbsolutePath,
		Checksum:     resp.Checksum,
		Size:         resp.Metadata.SizeBytes,
		Metadata:     resp.Metadata,
		LoadedAt:     time.Now(),
	}, nil
}

// ReportPluginStatus 向控制平面报告插件状态
func (c *PluginClient) ReportPluginStatus(ctx context.Context, statuses []*PluginStatusInfo) error {
	if len(statuses) == 0 {
		return nil
	}

	c.logger.Debug("报告插件状态", zap.Int("plugin_count", len(statuses)))

	protoStatuses := make([]*proto.PluginStatus, 0, len(statuses))
	for _, status := range statuses {
		protoStatus := &proto.PluginStatus{
			PluginName:        status.PluginName,
			Version:           status.Version,
			Status:            status.Status,
			ErrorMessage:      status.ErrorMessage,
			LastUsedTimestamp: status.LastUsedTimestamp,
			LoadTimestamp:     status.LoadTimestamp,
			Metrics:           status.Metrics,
		}
		protoStatuses = append(protoStatuses, protoStatus)
	}

	req := &proto.PluginStatusReport{
		AgentId:        c.agentID,
		PluginStatuses: protoStatuses,
	}

	resp, err := c.client.ReportPluginStatus(ctx, req)
	if err != nil {
		return fmt.Errorf("报告插件状态失败: %w", err)
	}

	if !resp.Success {
		c.logger.Warn("插件状态报告失败", zap.String("message", resp.Message))
	}

	// 处理控制平面建议的操作
	if len(resp.Actions) > 0 {
		c.logger.Info("收到插件操作建议", zap.Strings("actions", resp.Actions))
		// TODO: 实现操作处理逻辑
	}

	return nil
}

// StartPluginUpdateStream 启动插件更新流监听
func (c *PluginClient) StartPluginUpdateStream(ctx context.Context, updateHandler func(*proto.PluginUpdateNotification)) error {
	c.logger.Info("启动插件更新流监听")

	stream, err := c.client.StreamPluginUpdates(ctx, &emptypb.Empty{})
	if err != nil {
		return fmt.Errorf("创建插件更新流失败: %w", err)
	}

	// 接收更新通知
	for {
		notification, err := stream.Recv()
		if err != nil {
			if err == io.EOF {
				c.logger.Info("插件更新流结束")
				break
			}
			return fmt.Errorf("接收插件更新通知失败: %w", err)
		}

		c.logger.Info("收到插件更新通知",
			zap.String("plugin_name", notification.PluginName),
			zap.String("new_version", notification.NewVersion),
			zap.Bool("is_mandatory", notification.IsMandatory))

		if updateHandler != nil {
			updateHandler(notification)
		}
	}

	return nil
}

// verifyPluginFile 验证插件文件是否存在且校验和正确
func (c *PluginClient) verifyPluginFile(pluginPath, expectedChecksum string) error {
	// 检查文件是否存在
	if _, err := os.Stat(pluginPath); os.IsNotExist(err) {
		return fmt.Errorf("插件文件不存在: %s", pluginPath)
	}

	// 读取文件内容
	data, err := os.ReadFile(pluginPath)
	if err != nil {
		return fmt.Errorf("读取插件文件失败: %w", err)
	}

	// 验证校验和
	hash := md5.New()
	if _, err := hash.Write(data); err != nil {
		return err
	}
	actualChecksum := fmt.Sprintf("%x", hash.Sum(nil))

	if actualChecksum != expectedChecksum {
		return fmt.Errorf("校验和不匹配: 期望 %s, 实际 %s", expectedChecksum, actualChecksum)
	}

	return nil
}

// PluginInfo 插件信息（基于路径的分发）
type PluginInfo struct {
	Name         string                `json:"name"`
	Version      string                `json:"version"`
	Path         string                `json:"path"`          // 相对路径
	AbsolutePath string                `json:"absolute_path"` // 绝对路径
	Checksum     string                `json:"checksum"`
	Size         int64                 `json:"size"`
	Metadata     *proto.PluginMetadata `json:"metadata"`
	LoadedAt     time.Time             `json:"loaded_at"`
}

// PluginStatusInfo 插件状态信息
type PluginStatusInfo struct {
	PluginName        string            `json:"plugin_name"`
	Version           string            `json:"version"`
	Status            string            `json:"status"`
	ErrorMessage      string            `json:"error_message,omitempty"`
	LastUsedTimestamp int64             `json:"last_used_timestamp"`
	LoadTimestamp     int64             `json:"load_timestamp"`
	Metrics           map[string]string `json:"metrics,omitempty"`
}
