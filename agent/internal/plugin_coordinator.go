package internal

import (
	"context"
	"fmt"
	"sync"
	"time"

	pb "aiops/pkg/proto"
	plugininterface "aiops/plugins/interface"

	"go.uber.org/zap"
)

// PluginCoordinator 插件协调器
type PluginCoordinator struct {
	mu          sync.RWMutex
	taskManager *TaskManager
	logger      *zap.Logger

	// 插件注册表
	collectorPlugins map[string]plugininterface.Collector
	analyzerPlugins  map[string]plugininterface.Analyzer
	alerterPlugins   map[string]plugininterface.Alerter
	processorPlugins map[string]plugininterface.Processor

	// 插件链
	analysisChains map[string]*AnalysisChain
	alertingChains map[string]*AlertingChain

	// 协调规则
	coordinationRules []*CoordinationRule
}

// AnalysisChain 分析链
type AnalysisChain struct {
	ID         string
	Name       string
	Analyzers  []string // 分析器插件ID列表
	Alerters   []string // 警报器插件ID列表
	Processors []string // 处理器插件ID列表
	Config     map[string]interface{}
	Enabled    bool
}

// AlertingChain 警报链
type AlertingChain struct {
	ID       string
	Name     string
	Alerters []string // 警报器插件ID列表
	Rules    []CoordinationRule
	Config   map[string]interface{}
	Enabled  bool
}

// CoordinationRule 协调规则
type CoordinationRule struct {
	ID          string
	Name        string
	Description string
	Conditions  map[string]interface{}
	Actions     []CoordinationAction
	Priority    int
	Enabled     bool
}

// CoordinationAction 协调动作
type CoordinationAction struct {
	Type   string // "trigger_analysis", "send_alert", "run_processor"
	Target string // 目标插件ID
	Config map[string]interface{}
	Async  bool // 是否异步执行
}

// NewPluginCoordinator 创建插件协调器
func NewPluginCoordinator(taskManager *TaskManager, logger *zap.Logger) *PluginCoordinator {
	return &PluginCoordinator{
		taskManager:       taskManager,
		logger:            logger,
		collectorPlugins:  make(map[string]plugininterface.Collector),
		analyzerPlugins:   make(map[string]plugininterface.Analyzer),
		alerterPlugins:    make(map[string]plugininterface.Alerter),
		processorPlugins:  make(map[string]plugininterface.Processor),
		analysisChains:    make(map[string]*AnalysisChain),
		alertingChains:    make(map[string]*AlertingChain),
		coordinationRules: make([]*CoordinationRule, 0),
	}
}

// RegisterPlugin 注册插件
func (pc *PluginCoordinator) RegisterPlugin(pluginID string, plugin plugininterface.Plugin) error {
	pc.mu.Lock()
	defer pc.mu.Unlock()

	// 根据插件类型注册到对应的映射中
	if collector, ok := plugin.(plugininterface.Collector); ok {
		pc.collectorPlugins[pluginID] = collector
		pc.logger.Info("Registered collector plugin", zap.String("plugin_id", pluginID))
	}

	if analyzer, ok := plugin.(plugininterface.Analyzer); ok {
		pc.analyzerPlugins[pluginID] = analyzer
		pc.taskManager.analyzerPlugins[pluginID] = analyzer
		pc.logger.Info("Registered analyzer plugin", zap.String("plugin_id", pluginID))
	}

	if alerter, ok := plugin.(plugininterface.Alerter); ok {
		pc.alerterPlugins[pluginID] = alerter
		pc.taskManager.alerterPlugins[pluginID] = alerter
		pc.logger.Info("Registered alerter plugin", zap.String("plugin_id", pluginID))
	}

	if processor, ok := plugin.(plugininterface.Processor); ok {
		pc.processorPlugins[pluginID] = processor
		pc.taskManager.processorPlugins[pluginID] = processor
		pc.logger.Info("Registered processor plugin", zap.String("plugin_id", pluginID))
	}

	return nil
}

// UnregisterPlugin 注销插件
func (pc *PluginCoordinator) UnregisterPlugin(pluginID string) error {
	pc.mu.Lock()
	defer pc.mu.Unlock()

	delete(pc.collectorPlugins, pluginID)
	delete(pc.analyzerPlugins, pluginID)
	delete(pc.alerterPlugins, pluginID)
	delete(pc.processorPlugins, pluginID)

	delete(pc.taskManager.analyzerPlugins, pluginID)
	delete(pc.taskManager.alerterPlugins, pluginID)
	delete(pc.taskManager.processorPlugins, pluginID)

	pc.logger.Info("Unregistered plugin", zap.String("plugin_id", pluginID))
	return nil
}

// CreateAnalysisChain 创建分析链
func (pc *PluginCoordinator) CreateAnalysisChain(chain *AnalysisChain) error {
	pc.mu.Lock()
	defer pc.mu.Unlock()

	// 验证分析链中的插件是否存在
	for _, analyzerID := range chain.Analyzers {
		if _, exists := pc.analyzerPlugins[analyzerID]; !exists {
			return fmt.Errorf("analyzer plugin not found: %s", analyzerID)
		}
	}

	for _, alerterID := range chain.Alerters {
		if _, exists := pc.alerterPlugins[alerterID]; !exists {
			return fmt.Errorf("alerter plugin not found: %s", alerterID)
		}
	}

	pc.analysisChains[chain.ID] = chain
	pc.logger.Info("Created analysis chain",
		zap.String("chain_id", chain.ID),
		zap.String("chain_name", chain.Name))

	return nil
}

// CreateAlertingChain 创建警报链
func (pc *PluginCoordinator) CreateAlertingChain(chain *AlertingChain) error {
	pc.mu.Lock()
	defer pc.mu.Unlock()

	// 验证警报链中的插件是否存在
	for _, alerterID := range chain.Alerters {
		if _, exists := pc.alerterPlugins[alerterID]; !exists {
			return fmt.Errorf("alerter plugin not found: %s", alerterID)
		}
	}

	pc.alertingChains[chain.ID] = chain
	pc.logger.Info("Created alerting chain",
		zap.String("chain_id", chain.ID),
		zap.String("chain_name", chain.Name))

	return nil
}

// ExecuteAnalysisChain 执行分析链
func (pc *PluginCoordinator) ExecuteAnalysisChain(ctx context.Context, chainID string, data interface{}) error {
	pc.mu.RLock()
	chain, exists := pc.analysisChains[chainID]
	pc.mu.RUnlock()

	if !exists {
		return fmt.Errorf("analysis chain not found: %s", chainID)
	}

	if !chain.Enabled {
		return fmt.Errorf("analysis chain is disabled: %s", chainID)
	}

	pc.logger.Info("Executing analysis chain",
		zap.String("chain_id", chainID),
		zap.String("chain_name", chain.Name))

	var analysisResults []*plugininterface.AnalysisResult

	// 执行分析器
	for _, analyzerID := range chain.Analyzers {
		analyzer := pc.analyzerPlugins[analyzerID]
		if analyzer == nil {
			pc.logger.Warn("Analyzer plugin not found, skipping",
				zap.String("analyzer_id", analyzerID))
			continue
		}

		result, err := pc.runAnalyzer(ctx, analyzer, data)
		if err != nil {
			pc.logger.Error("Analyzer execution failed",
				zap.String("analyzer_id", analyzerID),
				zap.Error(err))
			continue
		}

		if result != nil {
			analysisResults = append(analysisResults, result)
		}
	}

	// 如果有异常或警报条件，触发警报器
	for _, result := range analysisResults {
		if pc.shouldTriggerAlerts(result, chain) {
			for _, alerterID := range chain.Alerters {
				alerter := pc.alerterPlugins[alerterID]
				if alerter == nil {
					continue
				}

				if err := pc.triggerAlerter(ctx, alerter, result); err != nil {
					pc.logger.Error("Alerter execution failed",
						zap.String("alerter_id", alerterID),
						zap.Error(err))
				}
			}
		}
	}

	return nil
}

// ExecuteAlertingChain 执行警报链
func (pc *PluginCoordinator) ExecuteAlertingChain(ctx context.Context, chainID string, alertEvent *plugininterface.AlertEvent) error {
	pc.mu.RLock()
	chain, exists := pc.alertingChains[chainID]
	pc.mu.RUnlock()

	if !exists {
		return fmt.Errorf("alerting chain not found: %s", chainID)
	}

	if !chain.Enabled {
		return fmt.Errorf("alerting chain is disabled: %s", chainID)
	}

	pc.logger.Info("Executing alerting chain",
		zap.String("chain_id", chainID),
		zap.String("chain_name", chain.Name),
		zap.String("alert_id", alertEvent.ID))

	// 评估规则
	for _, rule := range chain.Rules {
		if pc.evaluateCoordinationRule(&rule, alertEvent) {
			for _, action := range rule.Actions {
				if action.Async {
					go pc.executeCoordinationAction(ctx, &action, alertEvent)
				} else {
					pc.executeCoordinationAction(ctx, &action, alertEvent)
				}
			}
		}
	}

	// 执行链中的警报器
	for _, alerterID := range chain.Alerters {
		alerter := pc.alerterPlugins[alerterID]
		if alerter == nil {
			continue
		}

		if err := alerter.SendAlert(ctx, alertEvent); err != nil {
			pc.logger.Error("Failed to send alert in chain",
				zap.String("chain_id", chainID),
				zap.String("alerter_id", alerterID),
				zap.Error(err))
		}
	}

	return nil
}

// AddCoordinationRule 添加协调规则
func (pc *PluginCoordinator) AddCoordinationRule(rule *CoordinationRule) error {
	pc.mu.Lock()
	defer pc.mu.Unlock()

	pc.coordinationRules = append(pc.coordinationRules, rule)

	pc.logger.Info("Added coordination rule",
		zap.String("rule_id", rule.ID),
		zap.String("rule_name", rule.Name))

	return nil
}

// ProcessCoordinationEvent 处理协调事件
func (pc *PluginCoordinator) ProcessCoordinationEvent(ctx context.Context, event interface{}) error {
	pc.mu.RLock()
	rules := make([]*CoordinationRule, len(pc.coordinationRules))
	copy(rules, pc.coordinationRules)
	pc.mu.RUnlock()

	// 评估所有协调规则
	for _, rule := range rules {
		if !rule.Enabled {
			continue
		}

		if pc.evaluateCoordinationRule(rule, event) {
			pc.logger.Info("Coordination rule triggered",
				zap.String("rule_id", rule.ID),
				zap.String("rule_name", rule.Name))

			// 执行规则动作
			for _, action := range rule.Actions {
				if action.Async {
					go pc.executeCoordinationAction(ctx, &action, event)
				} else {
					pc.executeCoordinationAction(ctx, &action, event)
				}
			}
		}
	}

	return nil
}

// runAnalyzer 运行分析器
func (pc *PluginCoordinator) runAnalyzer(ctx context.Context, analyzer plugininterface.Analyzer, data interface{}) (*plugininterface.AnalysisResult, error) {
	// 根据数据类型调用不同的分析方法
	switch d := data.(type) {
	case []*pb.MetricData:
		return analyzer.AnalyzeMetrics(ctx, d)
	case []*pb.LogEntry:
		return analyzer.AnalyzeLogs(ctx, d)
	case []*plugininterface.PluginEvent:
		return analyzer.AnalyzeEvents(ctx, d)
	default:
		return nil, fmt.Errorf("unsupported data type for analysis")
	}
}

// shouldTriggerAlerts 判断是否应该触发警报
func (pc *PluginCoordinator) shouldTriggerAlerts(result *plugininterface.AnalysisResult, chain *AnalysisChain) bool {
	// 检查分析结果是否满足警报条件
	if result.Severity == "critical" || result.Severity == "error" {
		return true
	}

	if result.Confidence < 0.5 {
		return false // 置信度太低
	}

	if len(result.Anomalies) > 0 {
		return true // 发现异常
	}

	return false
}

// triggerAlerter 触发警报器
func (pc *PluginCoordinator) triggerAlerter(ctx context.Context, alerter plugininterface.Alerter, result *plugininterface.AnalysisResult) error {
	// 创建警报事件
	alertEvent := &plugininterface.AlertEvent{
		ID:          fmt.Sprintf("analysis_alert_%s_%d", result.ID, time.Now().Unix()),
		Title:       "Analysis Alert",
		Description: result.Message,
		Level:       result.Severity,
		Source:      "analysis_chain",
		Timestamp:   time.Now(),
	}

	return alerter.SendAlert(ctx, alertEvent)
}

// evaluateCoordinationRule 评估协调规则
func (pc *PluginCoordinator) evaluateCoordinationRule(rule *CoordinationRule, event interface{}) bool {
	// 简化的规则评估逻辑
	// 在实际实现中，这里应该支持更复杂的条件表达式

	switch e := event.(type) {
	case *plugininterface.AlertEvent:
		return pc.evaluateAlertEventRule(rule, e)
	case *plugininterface.AnalysisResult:
		return pc.evaluateAnalysisResultRule(rule, e)
	default:
		return false
	}
}

// evaluateAlertEventRule 评估警报事件规则
func (pc *PluginCoordinator) evaluateAlertEventRule(rule *CoordinationRule, event *plugininterface.AlertEvent) bool {
	// 检查严重性级别
	if requiredLevel, exists := rule.Conditions["min_severity"]; exists {
		if levelStr, ok := requiredLevel.(string); ok {
			if !pc.compareSeverity(event.Level, levelStr) {
				return false
			}
		}
	}

	// 检查设备ID
	if requiredDeviceID, exists := rule.Conditions["device_id"]; exists {
		if deviceID, ok := requiredDeviceID.(string); ok {
			if event.DeviceID != deviceID {
				return false
			}
		}
	}

	// 检查指标键
	if requiredMetricKey, exists := rule.Conditions["metric_key"]; exists {
		if metricKey, ok := requiredMetricKey.(string); ok {
			if event.MetricKey != metricKey {
				return false
			}
		}
	}

	return true
}

// evaluateAnalysisResultRule 评估分析结果规则
func (pc *PluginCoordinator) evaluateAnalysisResultRule(rule *CoordinationRule, result *plugininterface.AnalysisResult) bool {
	// 检查置信度
	if minConfidence, exists := rule.Conditions["min_confidence"]; exists {
		if confidence, ok := minConfidence.(float64); ok {
			if result.Confidence < confidence {
				return false
			}
		}
	}

	// 检查异常数量
	if minAnomalies, exists := rule.Conditions["min_anomalies"]; exists {
		if count, ok := minAnomalies.(int); ok {
			if len(result.Anomalies) < count {
				return false
			}
		}
	}

	return true
}

// executeCoordinationAction 执行协调动作
func (pc *PluginCoordinator) executeCoordinationAction(ctx context.Context, action *CoordinationAction, event interface{}) {
	pc.logger.Info("Executing coordination action",
		zap.String("action_type", action.Type),
		zap.String("target", action.Target))

	switch action.Type {
	case "trigger_analysis":
		pc.executeAnalysisAction(ctx, action, event)
	case "send_alert":
		pc.executeAlertAction(ctx, action, event)
	case "run_processor":
		pc.executeProcessorAction(ctx, action, event)
	default:
		pc.logger.Warn("Unknown coordination action type",
			zap.String("action_type", action.Type))
	}
}

// executeAnalysisAction 执行分析动作
func (pc *PluginCoordinator) executeAnalysisAction(ctx context.Context, action *CoordinationAction, event interface{}) {
	analyzer := pc.analyzerPlugins[action.Target]
	if analyzer == nil {
		pc.logger.Warn("Analyzer plugin not found for coordination action",
			zap.String("analyzer_id", action.Target))
		return
	}

	result, err := pc.runAnalyzer(ctx, analyzer, event)
	if err != nil {
		pc.logger.Error("Failed to execute analysis action",
			zap.String("analyzer_id", action.Target),
			zap.Error(err))
		return
	}

	if result != nil {
		// 将结果发送到分析结果通道
		select {
		case pc.taskManager.analysisResultCh <- &AnalysisResult{
			TaskID:    "coordination_analysis",
			Timestamp: time.Now(),
			Metrics:   make(map[string]float64),
		}:
		default:
			pc.logger.Warn("Analysis result channel is full")
		}
	}
}

// executeAlertAction 执行警报动作
func (pc *PluginCoordinator) executeAlertAction(ctx context.Context, action *CoordinationAction, event interface{}) {
	alerter := pc.alerterPlugins[action.Target]
	if alerter == nil {
		pc.logger.Warn("Alerter plugin not found for coordination action",
			zap.String("alerter_id", action.Target))
		return
	}

	// 创建警报事件
	var alertEvent *plugininterface.AlertEvent

	switch e := event.(type) {
	case *plugininterface.AlertEvent:
		alertEvent = e
	case *plugininterface.AnalysisResult:
		alertEvent = &plugininterface.AlertEvent{
			ID:          fmt.Sprintf("coord_alert_%d", time.Now().Unix()),
			Title:       "Coordination Alert",
			Description: e.Message,
			Level:       e.Severity,
			Source:      "coordination",
			Timestamp:   time.Now(),
		}
	default:
		pc.logger.Warn("Cannot create alert event from unknown event type")
		return
	}

	if err := alerter.SendAlert(ctx, alertEvent); err != nil {
		pc.logger.Error("Failed to execute alert action",
			zap.String("alerter_id", action.Target),
			zap.Error(err))
	}
}

// executeProcessorAction 执行处理器动作
func (pc *PluginCoordinator) executeProcessorAction(ctx context.Context, action *CoordinationAction, event interface{}) {
	processor := pc.processorPlugins[action.Target]
	if processor == nil {
		pc.logger.Warn("Processor plugin not found for coordination action",
			zap.String("processor_id", action.Target))
		return
	}

	// 根据事件类型处理
	switch e := event.(type) {
	case []*pb.MetricData:
		_, err := processor.ProcessMetrics(ctx, e)
		if err != nil {
			pc.logger.Error("Failed to process metrics",
				zap.String("processor_id", action.Target),
				zap.Error(err))
		}
	case []*pb.LogEntry:
		_, err := processor.ProcessLogs(ctx, e)
		if err != nil {
			pc.logger.Error("Failed to process logs",
				zap.String("processor_id", action.Target),
				zap.Error(err))
		}
	default:
		pc.logger.Warn("Unsupported event type for processor action")
	}
}

// compareSeverity 比较严重性级别
func (pc *PluginCoordinator) compareSeverity(current, min string) bool {
	severityLevels := map[string]int{
		"info":     1,
		"warning":  2,
		"error":    3,
		"critical": 4,
	}

	currentLevel, currentExists := severityLevels[current]
	minLevel, minExists := severityLevels[min]

	if !currentExists || !minExists {
		return false
	}

	return currentLevel >= minLevel
}
