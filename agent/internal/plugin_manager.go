package internal

import (
	"context"
	"crypto/sha256"
	"fmt"
	"os"
	"path/filepath"
	"plugin"
	"sync"
	"time"

	plugininterface "aiops/plugins/interface"

	"go.uber.org/zap"
)

// PluginMetadata 插件元数据
type PluginMetadata struct {
	ID           string            `json:"id"`
	Name         string            `json:"name"`
	Version      string            `json:"version"`
	DeviceTypes  []string          `json:"device_types"`
	BinaryPath   string            `json:"binary_path"`
	Checksum     string            `json:"checksum"`
	Dependencies []string          `json:"dependencies"`
	LastUpdated  time.Time         `json:"last_updated"`
	Config       map[string]string `json:"config"`
}

// PluginStatus 插件状态
type PluginStatus struct {
	ID          string    `json:"id"`
	Status      string    `json:"status"` // loading, loaded, running, stopped, error
	LastUpdate  time.Time `json:"last_update"`
	ErrorMsg    string    `json:"error_msg,omitempty"`
	ResourceUse struct {
		CPU    float64 `json:"cpu_percent"`
		Memory int64   `json:"memory_bytes"`
	} `json:"resource_use"`
}

// AgentPluginManager Agent端插件管理器
type AgentPluginManager struct {
	mu             sync.RWMutex
	loadedPlugins  map[string]plugininterface.Plugin
	pluginMetadata map[string]*PluginMetadata
	pluginStatus   map[string]*PluginStatus
	cacheDir       string
	maxCacheSize   int64
	logger         *zap.Logger
	autoUpdate     bool
	loadTimeout    time.Duration
	pluginClient   *PluginClient // 新增：插件客户端
}

// NewAgentPluginManager 创建新的Agent插件管理器
func NewAgentPluginManager(cacheDir string, logger *zap.Logger, pluginClient *PluginClient) *AgentPluginManager {
	return &AgentPluginManager{
		loadedPlugins:  make(map[string]plugininterface.Plugin),
		pluginMetadata: make(map[string]*PluginMetadata),
		pluginStatus:   make(map[string]*PluginStatus),
		cacheDir:       cacheDir,
		maxCacheSize:   1024 * 1024 * 1024, // 1GB
		logger:         logger,
		autoUpdate:     true,
		loadTimeout:    10 * time.Second,
		pluginClient:   pluginClient,
	}
}

// EnsurePluginLoaded 确保插件已加载
func (apm *AgentPluginManager) EnsurePluginLoaded(deviceType string) error {
	apm.mu.RLock()

	// 检查是否已有满足需求的插件
	for _, plugin := range apm.loadedPlugins {
		if collector, ok := plugin.(plugininterface.Collector); ok {
			supportedTypes := collector.GetSupportedDeviceTypes()
			for _, supportedType := range supportedTypes {
				if supportedType == deviceType {
					apm.mu.RUnlock()
					return nil
				}
			}
		}
	}
	apm.mu.RUnlock()

	// 需要加载新插件
	return apm.requestAndLoadPlugin(deviceType)
}

// requestAndLoadPlugin 请求并加载插件
func (apm *AgentPluginManager) requestAndLoadPlugin(deviceType string) error {
	apm.logger.Info("向控制平面请求插件", zap.String("device_type", deviceType))

	// 首先尝试从控制平面请求插件
	if apm.pluginClient != nil {
		pluginName := fmt.Sprintf("%s-collector", deviceType)
		pluginInfo, err := apm.pluginClient.RequestPlugin(
			context.Background(),
			pluginName,
			"", // 空版本表示请求最新版本
			deviceType,
		)
		if err != nil {
			apm.logger.Warn("从控制平面请求插件失败，尝试本地缓存",
				zap.String("plugin_name", pluginName),
				zap.Error(err))
		} else {
			// 成功获取插件路径，直接从路径加载
			return apm.loadPluginFromInfo(deviceType, pluginInfo)
		}
	}

	// 回退到本地缓存
	apm.logger.Info("尝试从本地缓存加载插件", zap.String("device_type", deviceType))
	pluginPath := filepath.Join(apm.cacheDir, fmt.Sprintf("%s-collector.so", deviceType))
	if _, err := os.Stat(pluginPath); os.IsNotExist(err) {
		return fmt.Errorf("插件不存在且无法从控制平面获取: %s", pluginPath)
	}

	return apm.loadPluginFromFile(deviceType, pluginPath)
}

// loadPluginFromInfo 从插件信息加载插件
func (apm *AgentPluginManager) loadPluginFromInfo(deviceType string, pluginInfo *PluginInfo) error {
	if pluginInfo == nil {
		return fmt.Errorf("插件信息为空")
	}

	apm.logger.Info("从插件路径加载插件",
		zap.String("device_type", deviceType),
		zap.String("plugin_name", pluginInfo.Name),
		zap.String("plugin_version", pluginInfo.Version),
		zap.String("plugin_path", pluginInfo.Path),
		zap.String("absolute_path", pluginInfo.AbsolutePath))

	// 使用绝对路径加载插件
	pluginPath := pluginInfo.AbsolutePath
	if pluginPath == "" {
		// 如果没有绝对路径，使用相对路径
		pluginPath = pluginInfo.Path
	}

	return apm.loadPluginFromFile(deviceType, pluginPath)
}

// loadPluginFromFile 从文件加载插件
func (apm *AgentPluginManager) loadPluginFromFile(pluginID, pluginPath string) error {
	apm.mu.Lock()
	defer apm.mu.Unlock()

	// 更新状态为加载中
	apm.updatePluginStatus(pluginID, "loading", "")

	// 验证插件文件
	if err := apm.validatePlugin(pluginPath); err != nil {
		apm.updatePluginStatus(pluginID, "error", err.Error())
		return fmt.Errorf("插件验证失败: %w", err)
	}

	// 加载插件
	p, err := plugin.Open(pluginPath)
	if err != nil {
		apm.updatePluginStatus(pluginID, "error", err.Error())
		return fmt.Errorf("打开插件失败: %w", err)
	}

	// 查找插件信息函数
	infoSymbol, err := p.Lookup("GetPluginInfo")
	if err != nil {
		apm.updatePluginStatus(pluginID, "error", err.Error())
		return fmt.Errorf("查找GetPluginInfo函数失败: %w", err)
	}

	infoFunc, ok := infoSymbol.(func() *plugininterface.PluginInfo)
	if !ok {
		err := fmt.Errorf("GetPluginInfo函数签名无效")
		apm.updatePluginStatus(pluginID, "error", err.Error())
		return err
	}

	// 获取插件信息
	info := infoFunc()
	if info == nil {
		err := fmt.Errorf("插件返回空信息")
		apm.updatePluginStatus(pluginID, "error", err.Error())
		return err
	}

	// 查找插件工厂函数
	factorySymbol, err := p.Lookup("CreatePluginFactory")
	if err != nil {
		apm.updatePluginStatus(pluginID, "error", err.Error())
		return fmt.Errorf("查找CreatePluginFactory函数失败: %w", err)
	}

	factoryFunc, ok := factorySymbol.(func() plugininterface.PluginFactory)
	if !ok {
		err := fmt.Errorf("CreatePluginFactory函数签名无效")
		apm.updatePluginStatus(pluginID, "error", err.Error())
		return err
	}

	// 创建插件工厂
	factory := factoryFunc()
	if factory == nil {
		err := fmt.Errorf("插件返回空工厂")
		apm.updatePluginStatus(pluginID, "error", err.Error())
		return err
	}

	// 准备插件配置
	config := make(map[string]interface{})

	// 创建插件实例
	pluginInstance, err := factory.CreatePlugin(info.Type, config)
	if err != nil {
		apm.updatePluginStatus(pluginID, "error", err.Error())
		return fmt.Errorf("创建插件实例失败: %w", err)
	}

	// 初始化插件
	ctx, cancel := context.WithTimeout(context.Background(), apm.loadTimeout)
	defer cancel()

	if err := pluginInstance.Initialize(ctx, config); err != nil {
		apm.updatePluginStatus(pluginID, "error", err.Error())
		return fmt.Errorf("插件初始化失败: %w", err)
	}

	// 存储插件
	apm.loadedPlugins[pluginID] = pluginInstance
	apm.updatePluginStatus(pluginID, "loaded", "")

	apm.logger.Info("插件加载成功",
		zap.String("plugin_id", pluginID),
		zap.String("plugin_path", pluginPath))

	return nil
}

// validatePlugin 验证插件文件
func (apm *AgentPluginManager) validatePlugin(pluginPath string) error {
	// 检查文件是否存在
	if _, err := os.Stat(pluginPath); err != nil {
		return fmt.Errorf("插件文件不存在: %w", err)
	}

	// 计算文件校验和（简化版本）
	data, err := os.ReadFile(pluginPath)
	if err != nil {
		return fmt.Errorf("读取插件文件失败: %w", err)
	}

	checksum := fmt.Sprintf("%x", sha256.Sum256(data))
	apm.logger.Debug("插件校验和",
		zap.String("path", pluginPath),
		zap.String("checksum", checksum))

	// TODO: 验证数字签名
	// TODO: 验证插件来源

	return nil
}

// GetCollectorPlugin 获取采集器插件
func (apm *AgentPluginManager) GetCollectorPlugin(deviceType string) (plugininterface.Collector, error) {
	apm.mu.RLock()
	defer apm.mu.RUnlock()

	for _, plugin := range apm.loadedPlugins {
		if collector, ok := plugin.(plugininterface.Collector); ok {
			supportedTypes := collector.GetSupportedDeviceTypes()
			for _, supportedType := range supportedTypes {
				if supportedType == deviceType {
					return collector, nil
				}
			}
		}
	}

	return nil, fmt.Errorf("未找到支持设备类型 %s 的采集器插件", deviceType)
}

// UnloadPlugin 卸载插件
func (apm *AgentPluginManager) UnloadPlugin(pluginID string) error {
	apm.mu.Lock()
	defer apm.mu.Unlock()

	plugin, exists := apm.loadedPlugins[pluginID]
	if !exists {
		return fmt.Errorf("插件未加载: %s", pluginID)
	}

	// 更新状态
	apm.updatePluginStatus(pluginID, "unloading", "")

	// 关闭插件
	ctx, cancel := context.WithTimeout(context.Background(), apm.loadTimeout)
	defer cancel()

	if err := plugin.Shutdown(ctx); err != nil {
		apm.logger.Warn("插件关闭失败",
			zap.String("plugin_id", pluginID),
			zap.Error(err))
	}

	// 从内存中移除
	delete(apm.loadedPlugins, pluginID)
	delete(apm.pluginStatus, pluginID)

	apm.logger.Info("插件卸载成功", zap.String("plugin_id", pluginID))
	return nil
}

// updatePluginStatus 更新插件状态
func (apm *AgentPluginManager) updatePluginStatus(pluginID, status, errorMsg string) {
	if apm.pluginStatus[pluginID] == nil {
		apm.pluginStatus[pluginID] = &PluginStatus{
			ID: pluginID,
		}
	}

	apm.pluginStatus[pluginID].Status = status
	apm.pluginStatus[pluginID].LastUpdate = time.Now()
	apm.pluginStatus[pluginID].ErrorMsg = errorMsg
}

// GetPluginStatus 获取插件状态
func (apm *AgentPluginManager) GetPluginStatus(pluginID string) (*PluginStatus, error) {
	apm.mu.RLock()
	defer apm.mu.RUnlock()

	status, exists := apm.pluginStatus[pluginID]
	if !exists {
		return nil, fmt.Errorf("插件状态不存在: %s", pluginID)
	}

	return status, nil
}

// ListLoadedPlugins 列出已加载的插件
func (apm *AgentPluginManager) ListLoadedPlugins() []string {
	apm.mu.RLock()
	defer apm.mu.RUnlock()

	var pluginIDs []string
	for id := range apm.loadedPlugins {
		pluginIDs = append(pluginIDs, id)
	}

	return pluginIDs
}

// GetSupportedDeviceTypes 获取所有支持的设备类型
func (apm *AgentPluginManager) GetSupportedDeviceTypes() []string {
	apm.mu.RLock()
	defer apm.mu.RUnlock()

	var deviceTypes []string
	seen := make(map[string]bool)

	for _, plugin := range apm.loadedPlugins {
		if collector, ok := plugin.(plugininterface.Collector); ok {
			supportedTypes := collector.GetSupportedDeviceTypes()
			for _, deviceType := range supportedTypes {
				if !seen[deviceType] {
					deviceTypes = append(deviceTypes, deviceType)
					seen[deviceType] = true
				}
			}
		}
	}

	return deviceTypes
}

// Cleanup 清理资源
func (apm *AgentPluginManager) Cleanup() error {
	apm.mu.Lock()
	defer apm.mu.Unlock()

	var errors []error

	// 卸载所有插件
	for pluginID := range apm.loadedPlugins {
		if err := apm.UnloadPlugin(pluginID); err != nil {
			errors = append(errors, err)
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("清理插件时发生错误: %v", errors)
	}

	return nil
}
