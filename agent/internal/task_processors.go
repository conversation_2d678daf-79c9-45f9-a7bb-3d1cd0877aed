package internal

import (
	"context"
	"fmt"
	"time"

	pb "aiops/pkg/proto"
	plugininterface "aiops/plugins/interface"

	"go.uber.org/zap"
)

// metricDataProcessor 处理指标数据
func (tm *TaskManager) metricDataProcessor(ctx context.Context) {
	tm.logger.Info("Metric data processor started")

	// 批处理配置
	batchSize := 100
	batchTimeout := 5 * time.Second

	batch := make([]*pb.MetricData, 0, batchSize)
	timer := time.NewTimer(batchTimeout)
	defer timer.Stop()

	for {
		select {
		case <-ctx.Done():
			// 处理剩余的批次
			if len(batch) > 0 {
				tm.processBatch(batch)
			}
			tm.logger.Info("Metric data processor stopped")
			return

		case <-tm.shutdownCh:
			// 处理剩余的批次
			if len(batch) > 0 {
				tm.processBatch(batch)
			}
			tm.logger.Info("Metric data processor stopped due to shutdown")
			return

		case metric := <-tm.metricDataCh:
			batch = append(batch, metric)

			// 如果批次满了，立即处理
			if len(batch) >= batchSize {
				tm.processBatch(batch)
				batch = batch[:0] // 重置批次

				// 重置计时器
				if !timer.Stop() {
					<-timer.C
				}
				timer.Reset(batchTimeout)
			}

		case <-timer.C:
			// 超时处理批次
			if len(batch) > 0 {
				tm.processBatch(batch)
				batch = batch[:0] // 重置批次
			}
			timer.Reset(batchTimeout)
		}
	}
}

// processBatch 处理指标数据批次
func (tm *TaskManager) processBatch(batch []*pb.MetricData) {
	if len(batch) == 0 {
		return
	}

	tm.logger.Debug("Processing metric batch",
		zap.Int("batch_size", len(batch)))

	// 1. 发送到外部系统（如果有的话）
	// 这里可以添加发送到时序数据库、消息队列等的逻辑

	// 2. 触发实时分析
	tm.triggerRealTimeAnalysis(batch)

	// 3. 更新内部缓存
	tm.updateMetricCache(batch)
}

// triggerRealTimeAnalysis 触发实时分析
func (tm *TaskManager) triggerRealTimeAnalysis(metrics []*pb.MetricData) {
	// 获取所有运行中的分析任务
	analysisTask := tm.findAnalysisTasks()

	for _, task := range analysisTask {
		// 检查这些指标是否与分析任务相关
		relevantMetrics := tm.filterRelevantMetrics(metrics, task)

		if len(relevantMetrics) > 0 {
			// 执行快速异常检测
			tm.performQuickAnomalyDetection(relevantMetrics, task)
		}
	}
}

// findAnalysisTasks 查找分析任务
func (tm *TaskManager) findAnalysisTasks() []*TaskExecutionContext {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	var analysisTasks []*TaskExecutionContext
	for _, task := range tm.activeTasks {
		if task.Type == AnalysisTask && task.Status == "running" {
			analysisTasks = append(analysisTasks, task)
		}
	}

	return analysisTasks
}

// filterRelevantMetrics 过滤相关指标
func (tm *TaskManager) filterRelevantMetrics(metrics []*pb.MetricData, task *TaskExecutionContext) []*pb.MetricData {
	// 这里可以根据任务配置过滤相关指标
	// 简化实现：返回所有指标
	return metrics
}

// performQuickAnomalyDetection 执行快速异常检测
func (tm *TaskManager) performQuickAnomalyDetection(metrics []*pb.MetricData, task *TaskExecutionContext) {
	// 简化的异常检测逻辑
	for _, metric := range metrics {
		if tm.isAnomalousMetric(metric) {
			anomaly := &plugininterface.Anomaly{
				ID:             generateAnomalyID(metric),
				DeviceID:       metric.DeviceId,
				MetricKey:      metric.MetricKey,
				AnomalyType:    "spike",
				ActualValue:    tm.getMetricValue(metric),
				ExpectedValue:  tm.getExpectedValue(metric),
				DeviationScore: tm.calculateDeviationScore(metric),
				Confidence:     0.8,
				Severity:       "warning",
				DetectedAt:     time.Now(),
				Status:         "new",
			}

			// 创建警报事件
			alertEvent := &plugininterface.AlertEvent{
				ID:             anomaly.ID + "_alert",
				Title:          "Anomaly detected",
				Description:    anomaly.Description,
				Level:          anomaly.Severity,
				DeviceID:       anomaly.DeviceID,
				DeviceName:     "", // 可以从缓存中获取
				MetricKey:      anomaly.MetricKey,
				CurrentValue:   anomaly.ActualValue,
				ThresholdValue: anomaly.ExpectedValue,
				Source:         "real_time_analysis",
				Timestamp:      anomaly.DetectedAt,
			}

			// 发送到警报通道
			select {
			case tm.alertEventCh <- alertEvent:
			default:
				tm.logger.Warn("Alert event channel is full, dropping real-time alert",
					zap.String("anomaly_id", anomaly.ID))
			}
		}
	}
}

// analysisResultProcessor 处理分析结果
func (tm *TaskManager) analysisResultProcessor(ctx context.Context) {
	tm.logger.Info("Analysis result processor started")

	for {
		select {
		case <-ctx.Done():
			tm.logger.Info("Analysis result processor stopped")
			return

		case <-tm.shutdownCh:
			tm.logger.Info("Analysis result processor stopped due to shutdown")
			return

		case result := <-tm.analysisResultCh:
			tm.handleAnalysisResult(result)
		}
	}
}

// handleAnalysisResult 处理分析结果
func (tm *TaskManager) handleAnalysisResult(result *AnalysisResult) {
	tm.logger.Info("Processing analysis result",
		zap.String("task_id", result.TaskID),
		zap.Int("anomaly_count", len(result.Anomalies)),
		zap.String("severity", result.Severity))

	// 1. 存储分析结果（可以存储到数据库）
	tm.storeAnalysisResult(result)

	// 2. 处理异常情况
	for _, anomaly := range result.Anomalies {
		tm.handleAnomaly(anomaly)
	}

	// 3. 生成报告（如果需要）
	if result.Severity == "critical" || result.Severity == "error" {
		tm.generateEmergencyReport(result)
	}

	// 4. 更新统计信息
	tm.updateAnalysisStatistics(result)
}

// storeAnalysisResult 存储分析结果
func (tm *TaskManager) storeAnalysisResult(result *AnalysisResult) {
	// 这里可以实现存储到数据库的逻辑
	tm.logger.Debug("Storing analysis result",
		zap.String("task_id", result.TaskID))
}

// handleAnomaly 处理异常
func (tm *TaskManager) handleAnomaly(anomaly *plugininterface.Anomaly) {
	tm.logger.Info("Handling anomaly",
		zap.String("anomaly_id", anomaly.ID),
		zap.String("device_id", anomaly.DeviceID),
		zap.String("metric_key", anomaly.MetricKey),
		zap.String("severity", anomaly.Severity))

	// 根据异常严重性采取不同的行动
	switch anomaly.Severity {
	case "critical":
		tm.handleCriticalAnomaly(anomaly)
	case "error":
		tm.handleErrorAnomaly(anomaly)
	case "warning":
		tm.handleWarningAnomaly(anomaly)
	default:
		tm.handleInfoAnomaly(anomaly)
	}
}

// handleCriticalAnomaly 处理严重异常
func (tm *TaskManager) handleCriticalAnomaly(anomaly *plugininterface.Anomaly) {
	// 立即发送警报
	alertEvent := &plugininterface.AlertEvent{
		ID:             anomaly.ID + "_critical_alert",
		Title:          "Critical Anomaly Detected",
		Description:    fmt.Sprintf("Critical anomaly in %s on device %s", anomaly.MetricKey, anomaly.DeviceID),
		Level:          "critical",
		DeviceID:       anomaly.DeviceID,
		MetricKey:      anomaly.MetricKey,
		CurrentValue:   anomaly.ActualValue,
		ThresholdValue: anomaly.ExpectedValue,
		Source:         "anomaly_handler",
		Timestamp:      time.Now(),
	}

	// 发送到所有警报器
	select {
	case tm.alertEventCh <- alertEvent:
	default:
		tm.logger.Error("Failed to send critical alert, channel is full")
	}

	// 触发自动恢复（如果有配置的话）
	tm.triggerAutoRecovery(anomaly)
}

// handleErrorAnomaly 处理错误异常
func (tm *TaskManager) handleErrorAnomaly(anomaly *plugininterface.Anomaly) {
	// 发送警报并记录
	alertEvent := &plugininterface.AlertEvent{
		ID:             anomaly.ID + "_error_alert",
		Title:          "Error Anomaly Detected",
		Description:    fmt.Sprintf("Error anomaly in %s on device %s", anomaly.MetricKey, anomaly.DeviceID),
		Level:          "error",
		DeviceID:       anomaly.DeviceID,
		MetricKey:      anomaly.MetricKey,
		CurrentValue:   anomaly.ActualValue,
		ThresholdValue: anomaly.ExpectedValue,
		Source:         "anomaly_handler",
		Timestamp:      time.Now(),
	}

	select {
	case tm.alertEventCh <- alertEvent:
	default:
		tm.logger.Warn("Failed to send error alert, channel is full")
	}
}

// handleWarningAnomaly 处理警告异常
func (tm *TaskManager) handleWarningAnomaly(anomaly *plugininterface.Anomaly) {
	// 记录并发送警告
	tm.logger.Warn("Warning anomaly detected",
		zap.String("device_id", anomaly.DeviceID),
		zap.String("metric_key", anomaly.MetricKey),
		zap.Float64("actual_value", anomaly.ActualValue),
		zap.Float64("expected_value", anomaly.ExpectedValue))
}

// handleInfoAnomaly 处理信息异常
func (tm *TaskManager) handleInfoAnomaly(anomaly *plugininterface.Anomaly) {
	// 仅记录
	tm.logger.Info("Info anomaly detected",
		zap.String("device_id", anomaly.DeviceID),
		zap.String("metric_key", anomaly.MetricKey))
}

// triggerAutoRecovery 触发自动恢复
func (tm *TaskManager) triggerAutoRecovery(anomaly *plugininterface.Anomaly) {
	tm.logger.Info("Triggering auto recovery",
		zap.String("anomaly_id", anomaly.ID))

	// 这里可以实现自动恢复逻辑
	// 例如：重启服务、清理缓存、调整配置等
}

// generateEmergencyReport 生成紧急报告
func (tm *TaskManager) generateEmergencyReport(result *AnalysisResult) {
	tm.logger.Info("Generating emergency report",
		zap.String("task_id", result.TaskID),
		zap.String("severity", result.Severity))

	// 这里可以实现生成报告的逻辑
	// 例如：发送邮件、生成PDF、推送到管理面板等
}

// updateAnalysisStatistics 更新分析统计信息
func (tm *TaskManager) updateAnalysisStatistics(result *AnalysisResult) {
	// 这里可以实现统计信息更新逻辑
	tm.logger.Debug("Updating analysis statistics",
		zap.String("task_id", result.TaskID))
}

// updateMetricCache 更新指标缓存
func (tm *TaskManager) updateMetricCache(metrics []*pb.MetricData) {
	// 这里可以实现指标缓存更新逻辑
	// 用于存储最近的指标数据，供分析使用
}

// 辅助函数
func (tm *TaskManager) isAnomalousMetric(metric *pb.MetricData) bool {
	// 简化的异常检测逻辑
	value := tm.getMetricValue(metric)
	expected := tm.getExpectedValue(metric)

	// 如果偏差超过50%认为是异常
	deviation := (value - expected) / expected
	return deviation > 0.5 || deviation < -0.5
}

func (tm *TaskManager) getMetricValue(metric *pb.MetricData) float64 {
	switch metric.ValueType.(type) {
	case *pb.MetricData_NumericValue:
		return metric.GetNumericValue()
	case *pb.MetricData_BooleanValue:
		if metric.GetBooleanValue() {
			return 1.0
		}
		return 0.0
	default:
		return 0.0
	}
}

func (tm *TaskManager) getExpectedValue(metric *pb.MetricData) float64 {
	// 这里应该从历史数据或模型中获取期望值
	// 简化实现：返回固定值
	return 100.0
}

func (tm *TaskManager) calculateDeviationScore(metric *pb.MetricData) float64 {
	actual := tm.getMetricValue(metric)
	expected := tm.getExpectedValue(metric)

	if expected == 0 {
		return 0
	}

	return (actual - expected) / expected
}

func generateAnomalyID(metric *pb.MetricData) string {
	return fmt.Sprintf("anomaly_%s_%s_%d", metric.DeviceId, metric.MetricKey, time.Now().Unix())
}
