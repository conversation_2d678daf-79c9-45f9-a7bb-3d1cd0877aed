package pipeline

import (
	"context"
	"fmt"
	"sync"
	"time"

	pb "aiops/pkg/proto"

	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

// ControlPlaneClientImpl Control Plane客户端实现
type ControlPlaneClientImpl struct {
	// 连接配置
	serverAddr string
	agentID    string
	logger     *zap.Logger

	// gRPC连接
	conn   *grpc.ClientConn
	client pb.AgentServiceClient

	// 流管理
	pipelineStream pb.AgentService_StreamPipelinesClient
	streamMutex    sync.RWMutex

	// 状态管理
	connected bool
	ctx       context.Context
	cancel    context.CancelFunc
	mutex     sync.RWMutex
}

// NewControlPlaneClient 创建新的Control Plane客户端
func NewControlPlaneClient(serverAddr, agentID string, logger *zap.Logger) *ControlPlaneClientImpl {
	ctx, cancel := context.WithCancel(context.Background())

	return &ControlPlaneClientImpl{
		serverAddr: serverAddr,
		agentID:    agentID,
		logger:     logger,
		ctx:        ctx,
		cancel:     cancel,
	}
}

// Connect 连接到Control Plane
func (c *ControlPlaneClientImpl) Connect() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.connected {
		return fmt.Errorf("already connected")
	}

	c.logger.Info("Connecting to Control Plane", zap.String("server", c.serverAddr))

	// 建立gRPC连接
	conn, err := grpc.Dial(c.serverAddr, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		return fmt.Errorf("failed to connect to control plane: %w", err)
	}

	c.conn = conn
	c.client = pb.NewAgentServiceClient(conn)

	// 注册Agent
	if err := c.registerAgent(); err != nil {
		c.conn.Close()
		return fmt.Errorf("failed to register agent: %w", err)
	}

	c.connected = true
	c.logger.Info("Connected to Control Plane successfully")

	return nil
}

// Disconnect 断开连接
func (c *ControlPlaneClientImpl) Disconnect() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if !c.connected {
		return fmt.Errorf("not connected")
	}

	c.logger.Info("Disconnecting from Control Plane")

	// 关闭流
	c.streamMutex.Lock()
	if c.pipelineStream != nil {
		c.pipelineStream.CloseSend()
		c.pipelineStream = nil
	}
	c.streamMutex.Unlock()

	// 关闭连接
	if c.conn != nil {
		c.conn.Close()
		c.conn = nil
	}

	c.client = nil
	c.connected = false
	c.cancel()

	c.logger.Info("Disconnected from Control Plane")

	return nil
}

// registerAgent 注册Agent
func (c *ControlPlaneClientImpl) registerAgent() error {
	req := &pb.RegisterAgentRequest{
		AgentId:                  c.agentID,
		AgentIp:                  "127.0.0.1", // 简化实现
		SupportedCollectorTypes:  []string{"system", "mysql", "redis", "http"},
	}

	ctx, cancel := context.WithTimeout(c.ctx, 10*time.Second)
	defer cancel()

	resp, err := c.client.RegisterAgent(ctx, req)
	if err != nil {
		return fmt.Errorf("register agent failed: %w", err)
	}

	if !resp.Success {
		return fmt.Errorf("register agent failed: %s", resp.Message)
	}

	c.logger.Info("Agent registered successfully", zap.String("message", resp.Message))

	return nil
}

// StreamPipelines 创建流水线流
func (c *ControlPlaneClientImpl) StreamPipelines(ctx context.Context) (pb.AgentService_StreamPipelinesClient, error) {
	c.streamMutex.Lock()
	defer c.streamMutex.Unlock()

	if !c.connected {
		return nil, fmt.Errorf("not connected to control plane")
	}

	// 如果已有流，先关闭
	if c.pipelineStream != nil {
		c.pipelineStream.CloseSend()
	}

	// 创建新流
	stream, err := c.client.StreamPipelines(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to create pipeline stream: %w", err)
	}

	c.pipelineStream = stream
	c.logger.Info("Pipeline stream created successfully")

	return stream, nil
}

// SendPipelineStatus 发送流水线状态
func (c *ControlPlaneClientImpl) SendPipelineStatus(status *pb.PipelineStatus) error {
	c.streamMutex.RLock()
	defer c.streamMutex.RUnlock()

	if c.pipelineStream == nil {
		return fmt.Errorf("pipeline stream not available")
	}

	if err := c.pipelineStream.Send(status); err != nil {
		return fmt.Errorf("failed to send pipeline status: %w", err)
	}

	return nil
}

// GetPipelineTemplates 获取流水线模板
func (c *ControlPlaneClientImpl) GetPipelineTemplates(req *pb.PipelineTemplateRequest) (*pb.PipelineTemplateResponse, error) {
	if !c.connected {
		return nil, fmt.Errorf("not connected to control plane")
	}

	ctx, cancel := context.WithTimeout(c.ctx, 30*time.Second)
	defer cancel()

	resp, err := c.client.GetPipelineTemplates(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to get pipeline templates: %w", err)
	}

	return resp, nil
}

// ValidatePipelineConfig 验证流水线配置
func (c *ControlPlaneClientImpl) ValidatePipelineConfig(req *pb.PipelineConfigValidationRequest) (*pb.PipelineConfigValidationResponse, error) {
	if !c.connected {
		return nil, fmt.Errorf("not connected to control plane")
	}

	ctx, cancel := context.WithTimeout(c.ctx, 30*time.Second)
	defer cancel()

	resp, err := c.client.ValidatePipelineConfig(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to validate pipeline config: %w", err)
	}

	return resp, nil
}

// RequestPlugin 请求插件
func (c *ControlPlaneClientImpl) RequestPlugin(req *pb.PluginRequest) (*pb.PluginResponse, error) {
	if !c.connected {
		return nil, fmt.Errorf("not connected to control plane")
	}

	ctx, cancel := context.WithTimeout(c.ctx, 60*time.Second) // 插件下载可能需要更长时间
	defer cancel()

	resp, err := c.client.RequestPlugin(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to request plugin: %w", err)
	}

	return resp, nil
}

// ReportPluginStatus 报告插件状态
func (c *ControlPlaneClientImpl) ReportPluginStatus(req *pb.PluginStatusReport) (*pb.PluginStatusResponse, error) {
	if !c.connected {
		return nil, fmt.Errorf("not connected to control plane")
	}

	ctx, cancel := context.WithTimeout(c.ctx, 10*time.Second)
	defer cancel()

	resp, err := c.client.ReportPluginStatus(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to report plugin status: %w", err)
	}

	return resp, nil
}

// IsConnected 检查是否已连接
func (c *ControlPlaneClientImpl) IsConnected() bool {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.connected
}

// GetServerAddr 获取服务器地址
func (c *ControlPlaneClientImpl) GetServerAddr() string {
	return c.serverAddr
}

// GetAgentID 获取Agent ID
func (c *ControlPlaneClientImpl) GetAgentID() string {
	return c.agentID
}

// Reconnect 重新连接
func (c *ControlPlaneClientImpl) Reconnect() error {
	c.logger.Info("Attempting to reconnect to Control Plane")

	// 先断开现有连接
	if c.IsConnected() {
		if err := c.Disconnect(); err != nil {
			c.logger.Error("Failed to disconnect before reconnect", zap.Error(err))
		}
	}

	// 重新连接
	return c.Connect()
}

// StartHeartbeat 启动心跳
func (c *ControlPlaneClientImpl) StartHeartbeat() {
	go c.heartbeatLoop()
}

// heartbeatLoop 心跳循环
func (c *ControlPlaneClientImpl) heartbeatLoop() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-c.ctx.Done():
			return
		case <-ticker.C:
			if c.IsConnected() {
				// 发送心跳状态
				status := &pb.PipelineStatus{
					AgentId:             c.agentID,
					Status:              "heartbeat",
					LastUpdateTimestamp: time.Now().Unix(),
				}

				if err := c.SendPipelineStatus(status); err != nil {
					c.logger.Error("Failed to send heartbeat", zap.Error(err))
					// 尝试重连
					go func() {
						if err := c.Reconnect(); err != nil {
							c.logger.Error("Failed to reconnect", zap.Error(err))
						}
					}()
				}
			}
		}
	}
}

// GetConnectionStats 获取连接统计信息
func (c *ControlPlaneClientImpl) GetConnectionStats() map[string]interface{} {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	stats := make(map[string]interface{})
	stats["connected"] = c.connected
	stats["server_addr"] = c.serverAddr
	stats["agent_id"] = c.agentID

	if c.conn != nil {
		stats["connection_state"] = c.conn.GetState().String()
	}

	return stats
}
