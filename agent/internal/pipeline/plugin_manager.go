package pipeline

import (
	"fmt"
	"os"
	"path/filepath"
	"plugin"
	"strings"
	"sync"

	"aiops/pkg/pipeline"
	pb "aiops/pkg/proto"

	"go.uber.org/zap"
)

// PluginManager 简化的插件管理器
type PluginManager struct {
	// 基础配置
	pluginsDir string
	logger     *zap.Logger

	// 插件注册表
	loadedPlugins map[string]*LoadedPlugin
	factories     map[string]pipeline.PluginFactory

	// 同步
	mutex sync.RWMutex
}

// LoadedPlugin 已加载的插件
type LoadedPlugin struct {
	Name     string
	Version  string
	Type     pipeline.PluginType
	Path     string
	Plugin   *plugin.Plugin
	Factory  pipeline.PluginFactory
	LoadedAt int64
}

// NewPluginManager 创建新的插件管理器
func NewPluginManager(pluginsDir string, logger *zap.Logger) *PluginManager {
	return &PluginManager{
		pluginsDir:    pluginsDir,
		logger:        logger,
		loadedPlugins: make(map[string]*LoadedPlugin),
		factories:     make(map[string]pipeline.PluginFactory),
	}
}

// Initialize 初始化插件管理器
func (pm *PluginManager) Initialize() error {
	pm.logger.Info("Initializing plugin manager", zap.String("plugins_dir", pm.pluginsDir))

	// 确保插件目录存在
	if err := os.MkdirAll(pm.pluginsDir, 0755); err != nil {
		return fmt.Errorf("failed to create plugins directory: %w", err)
	}

	// 扫描并加载插件
	if err := pm.scanAndLoadPlugins(); err != nil {
		return fmt.Errorf("failed to scan and load plugins: %w", err)
	}

	pm.logger.Info("Plugin manager initialized successfully",
		zap.Int("loaded_plugins", len(pm.loadedPlugins)))

	return nil
}

// scanAndLoadPlugins 扫描并加载插件
func (pm *PluginManager) scanAndLoadPlugins() error {
	return filepath.Walk(pm.pluginsDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 只处理.so文件
		if !info.IsDir() && strings.HasSuffix(path, ".so") {
			if err := pm.loadPlugin(path); err != nil {
				pm.logger.Error("Failed to load plugin",
					zap.String("path", path),
					zap.Error(err))
				// 继续加载其他插件，不因为一个插件失败而停止
			}
		}

		return nil
	})
}

// loadPlugin 加载单个插件
func (pm *PluginManager) loadPlugin(pluginPath string) error {
	pm.logger.Info("Loading plugin", zap.String("path", pluginPath))

	// 打开插件
	p, err := plugin.Open(pluginPath)
	if err != nil {
		return fmt.Errorf("failed to open plugin: %w", err)
	}

	// 查找插件工厂函数
	factorySymbol, err := p.Lookup("NewPluginFactory")
	if err != nil {
		return fmt.Errorf("plugin factory function not found: %w", err)
	}

	// 类型断言
	factoryFunc, ok := factorySymbol.(func() pipeline.PluginFactory)
	if !ok {
		return fmt.Errorf("invalid plugin factory function signature")
	}

	// 创建工厂实例
	factory := factoryFunc()
	if factory == nil {
		return fmt.Errorf("plugin factory returned nil")
	}

	// 获取插件信息
	info := factory.GetPluginInfo()
	if info == nil {
		return fmt.Errorf("plugin info is nil")
	}

	// 生成插件键
	pluginKey := fmt.Sprintf("%s:%s", info.Name, info.Version)

	// 检查是否已加载
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	if _, exists := pm.loadedPlugins[pluginKey]; exists {
		return fmt.Errorf("plugin already loaded: %s", pluginKey)
	}

	// 保存插件信息
	loadedPlugin := &LoadedPlugin{
		Name:     info.Name,
		Version:  info.Version,
		Type:     info.Type,
		Path:     pluginPath,
		Plugin:   p,
		Factory:  factory,
		LoadedAt: info.LoadedAt.Unix(),
	}

	pm.loadedPlugins[pluginKey] = loadedPlugin
	pm.factories[string(info.Type)] = factory

	pm.logger.Info("Plugin loaded successfully",
		zap.String("name", info.Name),
		zap.String("version", info.Version),
		zap.String("type", string(info.Type)))

	return nil
}

// LoadPlugin 加载插件实例
func (pm *PluginManager) LoadPlugin(name string, pluginType pipeline.PluginType, config map[string]interface{}) (pipeline.PipelinePlugin, error) {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	factory, exists := pm.factories[string(pluginType)]
	if !exists {
		return nil, fmt.Errorf("plugin factory not found for type: %s", pluginType)
	}

	// 创建插件实例
	pluginInstance, err := factory.CreatePlugin(pluginType, config)
	if err != nil {
		return nil, fmt.Errorf("failed to create plugin %s: %w", name, err)
	}

	// 初始化插件
	if err := pluginInstance.Initialize(config); err != nil {
		return nil, fmt.Errorf("failed to initialize plugin %s: %w", name, err)
	}

	return pluginInstance, nil
}

// IsPluginAvailable 检查插件是否可用
func (pm *PluginManager) IsPluginAvailable(name string, pluginType pipeline.PluginType) bool {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	_, exists := pm.factories[string(pluginType)]
	return exists
}

// GetAvailablePlugins 获取可用插件列表
func (pm *PluginManager) GetAvailablePlugins() []*LoadedPlugin {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	plugins := make([]*LoadedPlugin, 0, len(pm.loadedPlugins))
	for _, plugin := range pm.loadedPlugins {
		plugins = append(plugins, plugin)
	}

	return plugins
}

// CreateCollectorPlugin 创建采集器插件
func (pm *PluginManager) CreateCollectorPlugin(config *pb.CollectorPluginConfig) (pipeline.CollectorPlugin, error) {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	factory, exists := pm.factories[string(pipeline.CollectorType)]
	if !exists {
		return nil, fmt.Errorf("collector plugin factory not found")
	}

	// 转换配置
	pluginConfig := convertStringMapToInterface(config.Config)

	// 创建插件实例
	pluginInstance, err := factory.CreatePlugin(pipeline.CollectorType, pluginConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create collector plugin: %w", err)
	}

	// 类型断言
	collector, ok := pluginInstance.(pipeline.CollectorPlugin)
	if !ok {
		return nil, fmt.Errorf("plugin is not a collector")
	}

	return collector, nil
}

// CreateProcessorPlugin 创建处理器插件
func (pm *PluginManager) CreateProcessorPlugin(config *pb.ProcessorPluginConfig) (pipeline.PipelinePlugin, error) {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	factory, exists := pm.factories[string(pipeline.ProcessorType)]
	if !exists {
		return nil, fmt.Errorf("processor plugin factory not found")
	}

	// 转换配置
	pluginConfig := convertStringMapToInterface(config.Config)

	// 创建插件实例
	pluginInstance, err := factory.CreatePlugin(pipeline.ProcessorType, pluginConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create processor plugin: %w", err)
	}

	return pluginInstance, nil
}

// GetLoadedPlugins 获取已加载的插件列表
func (pm *PluginManager) GetLoadedPlugins() []*LoadedPlugin {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	plugins := make([]*LoadedPlugin, 0, len(pm.loadedPlugins))
	for _, plugin := range pm.loadedPlugins {
		plugins = append(plugins, plugin)
	}

	return plugins
}

// GetPluginByName 根据名称获取插件
func (pm *PluginManager) GetPluginByName(name, version string) (*LoadedPlugin, error) {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	pluginKey := fmt.Sprintf("%s:%s", name, version)
	plugin, exists := pm.loadedPlugins[pluginKey]
	if !exists {
		return nil, fmt.Errorf("plugin not found: %s", pluginKey)
	}

	return plugin, nil
}

// ReloadPlugin 重新加载插件
func (pm *PluginManager) ReloadPlugin(name, version string) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	pluginKey := fmt.Sprintf("%s:%s", name, version)
	loadedPlugin, exists := pm.loadedPlugins[pluginKey]
	if !exists {
		return fmt.Errorf("plugin not found: %s", pluginKey)
	}

	// 移除旧插件
	delete(pm.loadedPlugins, pluginKey)
	delete(pm.factories, string(loadedPlugin.Type))

	// 重新加载
	pm.mutex.Unlock() // 临时释放锁以调用loadPlugin
	err := pm.loadPlugin(loadedPlugin.Path)
	pm.mutex.Lock() // 重新获取锁

	if err != nil {
		return fmt.Errorf("failed to reload plugin: %w", err)
	}

	pm.logger.Info("Plugin reloaded successfully",
		zap.String("name", name),
		zap.String("version", version))

	return nil
}

// UnloadPlugin 卸载插件
func (pm *PluginManager) UnloadPlugin(name, version string) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	pluginKey := fmt.Sprintf("%s:%s", name, version)
	loadedPlugin, exists := pm.loadedPlugins[pluginKey]
	if !exists {
		return fmt.Errorf("plugin not found: %s", pluginKey)
	}

	// 移除插件
	delete(pm.loadedPlugins, pluginKey)
	delete(pm.factories, string(loadedPlugin.Type))

	pm.logger.Info("Plugin unloaded",
		zap.String("name", name),
		zap.String("version", version))

	return nil
}

// ValidatePlugin 验证插件
func (pm *PluginManager) ValidatePlugin(pluginPath string) error {
	// 尝试打开插件
	p, err := plugin.Open(pluginPath)
	if err != nil {
		return fmt.Errorf("failed to open plugin: %w", err)
	}

	// 查找必需的符号
	_, err = p.Lookup("NewPluginFactory")
	if err != nil {
		return fmt.Errorf("plugin factory function not found: %w", err)
	}

	return nil
}

// GetPluginStats 获取插件统计信息
func (pm *PluginManager) GetPluginStats() map[string]interface{} {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	stats := make(map[string]interface{})
	stats["total_plugins"] = len(pm.loadedPlugins)
	stats["plugins_dir"] = pm.pluginsDir

	// 按类型统计
	typeStats := make(map[string]int)
	for _, plugin := range pm.loadedPlugins {
		typeStats[string(plugin.Type)]++
	}
	stats["by_type"] = typeStats

	// 插件列表
	pluginList := make([]map[string]interface{}, 0, len(pm.loadedPlugins))
	for _, plugin := range pm.loadedPlugins {
		pluginInfo := map[string]interface{}{
			"name":      plugin.Name,
			"version":   plugin.Version,
			"type":      string(plugin.Type),
			"path":      plugin.Path,
			"loaded_at": plugin.LoadedAt,
		}
		pluginList = append(pluginList, pluginInfo)
	}
	stats["plugins"] = pluginList

	return stats
}

// Shutdown 关闭插件管理器
func (pm *PluginManager) Shutdown() error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	pm.logger.Info("Shutting down plugin manager")

	// 清理资源
	pm.loadedPlugins = make(map[string]*LoadedPlugin)
	pm.factories = make(map[string]pipeline.PluginFactory)

	pm.logger.Info("Plugin manager shutdown complete")

	return nil
}
