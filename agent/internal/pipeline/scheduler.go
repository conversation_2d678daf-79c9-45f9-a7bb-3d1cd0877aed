package pipeline

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
)

// PipelineScheduler 流水线调度器
type PipelineScheduler struct {
	logger    *zap.Logger
	pipelines map[string]*ScheduledPipeline
	mutex     sync.RWMutex
	
	// 调度配置
	tickInterval time.Duration
	
	// 状态
	running bool
	ctx     context.Context
	cancel  context.CancelFunc
}

// ScheduledPipeline 调度的流水线
type ScheduledPipeline struct {
	Instance     *PipelineInstance
	Schedule     *PipelineSchedule
	LastRun      time.Time
	NextRun      time.Time
	RunCount     int64
	ErrorCount   int64
	LastError    error
}

// PipelineSchedule 流水线调度配置
type PipelineSchedule struct {
	Type     ScheduleType  `json:"type"`     // 调度类型
	Interval time.Duration `json:"interval"` // 间隔时间
	CronExpr string        `json:"cron"`     // Cron表达式
	Enabled  bool          `json:"enabled"`  // 是否启用
	
	// 高级配置
	MaxRetries    int           `json:"max_retries"`    // 最大重试次数
	RetryInterval time.Duration `json:"retry_interval"` // 重试间隔
	Timeout       time.Duration `json:"timeout"`        // 超时时间
}

// ScheduleType 调度类型
type ScheduleType string

const (
	ScheduleTypeInterval ScheduleType = "interval" // 间隔调度
	ScheduleTypeCron     ScheduleType = "cron"     // Cron调度
	ScheduleTypeManual   ScheduleType = "manual"   // 手动调度
)

// NewPipelineScheduler 创建新的流水线调度器
func NewPipelineScheduler(logger *zap.Logger) *PipelineScheduler {
	ctx, cancel := context.WithCancel(context.Background())
	
	return &PipelineScheduler{
		logger:       logger,
		pipelines:    make(map[string]*ScheduledPipeline),
		tickInterval: 10 * time.Second, // 每10秒检查一次
		ctx:          ctx,
		cancel:       cancel,
	}
}

// Start 启动调度器
func (s *PipelineScheduler) Start() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	
	if s.running {
		return fmt.Errorf("scheduler already running")
	}
	
	s.running = true
	go s.schedulerLoop()
	
	s.logger.Info("Pipeline scheduler started")
	return nil
}

// Stop 停止调度器
func (s *PipelineScheduler) Stop() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	
	if !s.running {
		return nil
	}
	
	s.cancel()
	s.running = false
	
	s.logger.Info("Pipeline scheduler stopped")
	return nil
}

// RegisterPipeline 注册流水线到调度器
func (s *PipelineScheduler) RegisterPipeline(instance *PipelineInstance) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	
	config := instance.GetConfig()
	
	// 创建默认调度配置
	schedule := &PipelineSchedule{
		Type:          ScheduleTypeInterval,
		Interval:      30 * time.Second, // 默认30秒间隔
		Enabled:       true,
		MaxRetries:    3,
		RetryInterval: 5 * time.Second,
		Timeout:       5 * time.Minute,
	}
	
	// 如果配置中有采集器间隔，使用它
	if config.Collector != nil && config.Collector.Interval > 0 {
		schedule.Interval = config.Collector.Interval
	}
	
	scheduledPipeline := &ScheduledPipeline{
		Instance: instance,
		Schedule: schedule,
		NextRun:  time.Now().Add(schedule.Interval),
	}
	
	s.pipelines[config.ID] = scheduledPipeline
	s.logger.Info("Pipeline registered to scheduler", 
		zap.String("id", config.ID),
		zap.Duration("interval", schedule.Interval))
	
	return nil
}

// UnregisterPipeline 从调度器中移除流水线
func (s *PipelineScheduler) UnregisterPipeline(id string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	
	delete(s.pipelines, id)
	s.logger.Info("Pipeline unregistered from scheduler", zap.String("id", id))
}

// UpdateSchedule 更新流水线调度配置
func (s *PipelineScheduler) UpdateSchedule(id string, schedule *PipelineSchedule) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	
	scheduledPipeline, exists := s.pipelines[id]
	if !exists {
		return fmt.Errorf("pipeline %s not found in scheduler", id)
	}
	
	scheduledPipeline.Schedule = schedule
	scheduledPipeline.NextRun = s.calculateNextRun(schedule)
	
	s.logger.Info("Pipeline schedule updated", 
		zap.String("id", id),
		zap.String("type", string(schedule.Type)),
		zap.Duration("interval", schedule.Interval))
	
	return nil
}

// GetSchedule 获取流水线调度配置
func (s *PipelineScheduler) GetSchedule(id string) (*PipelineSchedule, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	
	scheduledPipeline, exists := s.pipelines[id]
	if !exists {
		return nil, fmt.Errorf("pipeline %s not found in scheduler", id)
	}
	
	return scheduledPipeline.Schedule, nil
}

// TriggerPipeline 手动触发流水线执行
func (s *PipelineScheduler) TriggerPipeline(id string) error {
	s.mutex.RLock()
	scheduledPipeline, exists := s.pipelines[id]
	s.mutex.RUnlock()
	
	if !exists {
		return fmt.Errorf("pipeline %s not found in scheduler", id)
	}
	
	return s.executePipeline(scheduledPipeline)
}

// GetSchedulerStats 获取调度器统计信息
func (s *PipelineScheduler) GetSchedulerStats() *SchedulerStats {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	
	stats := &SchedulerStats{
		TotalPipelines:    len(s.pipelines),
		EnabledPipelines:  0,
		DisabledPipelines: 0,
		TotalRuns:         0,
		TotalErrors:       0,
	}
	
	for _, sp := range s.pipelines {
		if sp.Schedule.Enabled {
			stats.EnabledPipelines++
		} else {
			stats.DisabledPipelines++
		}
		stats.TotalRuns += sp.RunCount
		stats.TotalErrors += sp.ErrorCount
	}
	
	return stats
}

// schedulerLoop 调度器主循环
func (s *PipelineScheduler) schedulerLoop() {
	ticker := time.NewTicker(s.tickInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-s.ctx.Done():
			return
		case <-ticker.C:
			s.checkAndExecutePipelines()
		}
	}
}

// checkAndExecutePipelines 检查并执行到期的流水线
func (s *PipelineScheduler) checkAndExecutePipelines() {
	s.mutex.RLock()
	now := time.Now()
	toExecute := make([]*ScheduledPipeline, 0)
	
	for _, sp := range s.pipelines {
		if sp.Schedule.Enabled && now.After(sp.NextRun) {
			toExecute = append(toExecute, sp)
		}
	}
	s.mutex.RUnlock()
	
	// 执行到期的流水线
	for _, sp := range toExecute {
		go func(scheduledPipeline *ScheduledPipeline) {
			if err := s.executePipeline(scheduledPipeline); err != nil {
				s.logger.Error("Failed to execute scheduled pipeline",
					zap.String("id", scheduledPipeline.Instance.GetConfig().ID),
					zap.Error(err))
			}
		}(sp)
	}
}

// executePipeline 执行流水线
func (s *PipelineScheduler) executePipeline(sp *ScheduledPipeline) error {
	config := sp.Instance.GetConfig()
	
	s.logger.Debug("Executing scheduled pipeline", zap.String("id", config.ID))
	
	// 更新运行统计
	s.mutex.Lock()
	sp.LastRun = time.Now()
	sp.RunCount++
	sp.NextRun = s.calculateNextRun(sp.Schedule)
	s.mutex.Unlock()
	
	// 创建执行上下文
	ctx, cancel := context.WithTimeout(s.ctx, sp.Schedule.Timeout)
	defer cancel()
	
	// 执行流水线
	if err := sp.Instance.Execute(ctx); err != nil {
		s.mutex.Lock()
		sp.ErrorCount++
		sp.LastError = err
		s.mutex.Unlock()
		
		s.logger.Error("Pipeline execution failed",
			zap.String("id", config.ID),
			zap.Error(err))
		
		return err
	}
	
	s.logger.Debug("Pipeline executed successfully", zap.String("id", config.ID))
	return nil
}

// calculateNextRun 计算下次运行时间
func (s *PipelineScheduler) calculateNextRun(schedule *PipelineSchedule) time.Time {
	now := time.Now()
	
	switch schedule.Type {
	case ScheduleTypeInterval:
		return now.Add(schedule.Interval)
	case ScheduleTypeCron:
		// 简化实现：这里应该使用cron库解析表达式
		// 暂时使用间隔时间作为回退
		return now.Add(schedule.Interval)
	case ScheduleTypeManual:
		// 手动调度不自动计算下次运行时间
		return time.Time{}
	default:
		return now.Add(schedule.Interval)
	}
}

// SchedulerStats 调度器统计信息
type SchedulerStats struct {
	TotalPipelines    int   `json:"total_pipelines"`
	EnabledPipelines  int   `json:"enabled_pipelines"`
	DisabledPipelines int   `json:"disabled_pipelines"`
	TotalRuns         int64 `json:"total_runs"`
	TotalErrors       int64 `json:"total_errors"`
}
