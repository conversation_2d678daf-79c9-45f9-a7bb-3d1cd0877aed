package pipeline

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
)

// PipelineMonitor 流水线监控器
type PipelineMonitor struct {
	logger    *zap.Logger
	pipelines map[string]*MonitoredPipeline
	mutex     sync.RWMutex
	
	// 监控配置
	checkInterval    time.Duration
	healthTimeout    time.Duration
	alertThresholds  *AlertThresholds
	
	// 状态
	running bool
	ctx     context.Context
	cancel  context.CancelFunc
	
	// 事件通道
	eventChan chan *MonitorEvent
}

// MonitoredPipeline 被监控的流水线
type MonitoredPipeline struct {
	Instance      *PipelineInstance
	HealthStatus  HealthStatus
	Metrics       *PipelineMetrics
	LastCheck     time.Time
	AlertCount    int64
	LastAlert     time.Time
}

// HealthStatus 健康状态
type HealthStatus struct {
	Status      string    `json:"status"`       // healthy, unhealthy, unknown
	LastHealthy time.Time `json:"last_healthy"` // 最后健康时间
	LastError   error     `json:"last_error"`   // 最后错误
	CheckCount  int64     `json:"check_count"`  // 检查次数
	ErrorCount  int64     `json:"error_count"`  // 错误次数
}

// AlertThresholds 告警阈值
type AlertThresholds struct {
	ErrorRate         float64       `json:"error_rate"`          // 错误率阈值
	ResponseTime      time.Duration `json:"response_time"`       // 响应时间阈值
	MemoryUsage       int64         `json:"memory_usage"`        // 内存使用阈值(MB)
	CPUUsage          float64       `json:"cpu_usage"`           // CPU使用率阈值
	UnhealthyDuration time.Duration `json:"unhealthy_duration"`  // 不健康持续时间阈值
}

// MonitorEvent 监控事件
type MonitorEvent struct {
	Type      MonitorEventType `json:"type"`
	PipelineID string          `json:"pipeline_id"`
	Timestamp  time.Time       `json:"timestamp"`
	Message    string          `json:"message"`
	Data       interface{}     `json:"data"`
}

// MonitorEventType 监控事件类型
type MonitorEventType string

const (
	EventTypeHealthCheck MonitorEventType = "health_check"
	EventTypeAlert       MonitorEventType = "alert"
	EventTypeRecovery    MonitorEventType = "recovery"
	EventTypeMetrics     MonitorEventType = "metrics"
)

// NewPipelineMonitor 创建新的流水线监控器
func NewPipelineMonitor(logger *zap.Logger) *PipelineMonitor {
	ctx, cancel := context.WithCancel(context.Background())
	
	return &PipelineMonitor{
		logger:       logger,
		pipelines:    make(map[string]*MonitoredPipeline),
		checkInterval: 30 * time.Second,
		healthTimeout: 10 * time.Second,
		alertThresholds: &AlertThresholds{
			ErrorRate:         0.1,  // 10%错误率
			ResponseTime:      30 * time.Second,
			MemoryUsage:       512,  // 512MB
			CPUUsage:          80.0, // 80%
			UnhealthyDuration: 5 * time.Minute,
		},
		ctx:       ctx,
		cancel:    cancel,
		eventChan: make(chan *MonitorEvent, 100),
	}
}

// Start 启动监控器
func (m *PipelineMonitor) Start() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	if m.running {
		return fmt.Errorf("monitor already running")
	}
	
	m.running = true
	
	// 启动监控循环
	go m.monitorLoop()
	
	// 启动事件处理循环
	go m.eventLoop()
	
	m.logger.Info("Pipeline monitor started")
	return nil
}

// Stop 停止监控器
func (m *PipelineMonitor) Stop() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	if !m.running {
		return nil
	}
	
	m.cancel()
	m.running = false
	close(m.eventChan)
	
	m.logger.Info("Pipeline monitor stopped")
	return nil
}

// RegisterPipeline 注册流水线到监控器
func (m *PipelineMonitor) RegisterPipeline(instance *PipelineInstance) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	config := instance.GetConfig()
	
	monitoredPipeline := &MonitoredPipeline{
		Instance: instance,
		HealthStatus: HealthStatus{
			Status:      "unknown",
			LastHealthy: time.Now(),
		},
		Metrics:   &PipelineMetrics{},
		LastCheck: time.Now(),
	}
	
	m.pipelines[config.ID] = monitoredPipeline
	m.logger.Info("Pipeline registered to monitor", zap.String("id", config.ID))
	
	return nil
}

// UnregisterPipeline 从监控器中移除流水线
func (m *PipelineMonitor) UnregisterPipeline(id string) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	
	delete(m.pipelines, id)
	m.logger.Info("Pipeline unregistered from monitor", zap.String("id", id))
}

// GetPipelineHealth 获取流水线健康状态
func (m *PipelineMonitor) GetPipelineHealth(id string) (*HealthStatus, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	
	monitoredPipeline, exists := m.pipelines[id]
	if !exists {
		return nil, fmt.Errorf("pipeline %s not found in monitor", id)
	}
	
	return &monitoredPipeline.HealthStatus, nil
}

// GetPipelineMetrics 获取流水线指标
func (m *PipelineMonitor) GetPipelineMetrics(id string) (*PipelineMetrics, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	
	monitoredPipeline, exists := m.pipelines[id]
	if !exists {
		return nil, fmt.Errorf("pipeline %s not found in monitor", id)
	}
	
	return monitoredPipeline.Metrics, nil
}

// GetMonitorStats 获取监控器统计信息
func (m *PipelineMonitor) GetMonitorStats() *MonitorStats {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	
	stats := &MonitorStats{
		TotalPipelines:   len(m.pipelines),
		HealthyPipelines: 0,
		UnhealthyPipelines: 0,
		UnknownPipelines: 0,
		TotalAlerts:      0,
	}
	
	for _, mp := range m.pipelines {
		switch mp.HealthStatus.Status {
		case "healthy":
			stats.HealthyPipelines++
		case "unhealthy":
			stats.UnhealthyPipelines++
		default:
			stats.UnknownPipelines++
		}
		stats.TotalAlerts += mp.AlertCount
	}
	
	return stats
}

// GetEventChannel 获取事件通道
func (m *PipelineMonitor) GetEventChannel() <-chan *MonitorEvent {
	return m.eventChan
}

// monitorLoop 监控主循环
func (m *PipelineMonitor) monitorLoop() {
	ticker := time.NewTicker(m.checkInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-m.ctx.Done():
			return
		case <-ticker.C:
			m.checkAllPipelines()
		}
	}
}

// checkAllPipelines 检查所有流水线
func (m *PipelineMonitor) checkAllPipelines() {
	m.mutex.RLock()
	pipelines := make([]*MonitoredPipeline, 0, len(m.pipelines))
	for _, mp := range m.pipelines {
		pipelines = append(pipelines, mp)
	}
	m.mutex.RUnlock()
	
	for _, mp := range pipelines {
		go m.checkPipelineHealth(mp)
	}
}

// checkPipelineHealth 检查流水线健康状态
func (m *PipelineMonitor) checkPipelineHealth(mp *MonitoredPipeline) {
	config := mp.Instance.GetConfig()
	
	// 创建健康检查上下文
	ctx, cancel := context.WithTimeout(m.ctx, m.healthTimeout)
	defer cancel()
	
	// 更新检查统计
	m.mutex.Lock()
	mp.LastCheck = time.Now()
	mp.HealthStatus.CheckCount++
	m.mutex.Unlock()
	
	// 执行健康检查
	err := mp.Instance.HealthCheck(ctx)
	
	// 更新健康状态
	m.mutex.Lock()
	previousStatus := mp.HealthStatus.Status
	
	if err != nil {
		mp.HealthStatus.Status = "unhealthy"
		mp.HealthStatus.LastError = err
		mp.HealthStatus.ErrorCount++
	} else {
		mp.HealthStatus.Status = "healthy"
		mp.HealthStatus.LastHealthy = time.Now()
		mp.HealthStatus.LastError = nil
	}
	m.mutex.Unlock()
	
	// 发送监控事件
	event := &MonitorEvent{
		Type:       EventTypeHealthCheck,
		PipelineID: config.ID,
		Timestamp:  time.Now(),
		Message:    fmt.Sprintf("Health check: %s", mp.HealthStatus.Status),
		Data:       mp.HealthStatus,
	}
	
	select {
	case m.eventChan <- event:
	default:
		m.logger.Warn("Monitor event channel full, dropping event")
	}
	
	// 检查是否需要发送告警
	if previousStatus != mp.HealthStatus.Status {
		m.checkAndSendAlert(mp, previousStatus)
	}
	
	// 更新指标
	m.updatePipelineMetrics(mp)
}

// checkAndSendAlert 检查并发送告警
func (m *PipelineMonitor) checkAndSendAlert(mp *MonitoredPipeline, previousStatus string) {
	config := mp.Instance.GetConfig()
	currentStatus := mp.HealthStatus.Status
	
	var eventType MonitorEventType
	var message string
	
	if previousStatus == "healthy" && currentStatus == "unhealthy" {
		eventType = EventTypeAlert
		message = "Pipeline became unhealthy"
		
		m.mutex.Lock()
		mp.AlertCount++
		mp.LastAlert = time.Now()
		m.mutex.Unlock()
		
	} else if previousStatus == "unhealthy" && currentStatus == "healthy" {
		eventType = EventTypeRecovery
		message = "Pipeline recovered to healthy state"
	} else {
		return // 不需要发送告警
	}
	
	event := &MonitorEvent{
		Type:       eventType,
		PipelineID: config.ID,
		Timestamp:  time.Now(),
		Message:    message,
		Data:       mp.HealthStatus,
	}
	
	select {
	case m.eventChan <- event:
	default:
		m.logger.Warn("Monitor event channel full, dropping alert event")
	}
}

// updatePipelineMetrics 更新流水线指标
func (m *PipelineMonitor) updatePipelineMetrics(mp *MonitoredPipeline) {
	// 获取流水线指标
	metrics := mp.Instance.GetMetrics()
	if metrics != nil {
		m.mutex.Lock()
		mp.Metrics = metrics
		m.mutex.Unlock()
		
		// 发送指标事件
		event := &MonitorEvent{
			Type:       EventTypeMetrics,
			PipelineID: mp.Instance.GetConfig().ID,
			Timestamp:  time.Now(),
			Message:    "Metrics updated",
			Data:       metrics,
		}
		
		select {
		case m.eventChan <- event:
		default:
			// 指标事件不是关键事件，可以丢弃
		}
	}
}

// eventLoop 事件处理循环
func (m *PipelineMonitor) eventLoop() {
	for {
		select {
		case <-m.ctx.Done():
			return
		case event, ok := <-m.eventChan:
			if !ok {
				return
			}
			m.handleEvent(event)
		}
	}
}

// handleEvent 处理监控事件
func (m *PipelineMonitor) handleEvent(event *MonitorEvent) {
	switch event.Type {
	case EventTypeAlert:
		m.logger.Warn("Pipeline alert",
			zap.String("pipeline_id", event.PipelineID),
			zap.String("message", event.Message))
		
	case EventTypeRecovery:
		m.logger.Info("Pipeline recovery",
			zap.String("pipeline_id", event.PipelineID),
			zap.String("message", event.Message))
		
	case EventTypeHealthCheck:
		m.logger.Debug("Pipeline health check",
			zap.String("pipeline_id", event.PipelineID),
			zap.String("message", event.Message))
		
	case EventTypeMetrics:
		m.logger.Debug("Pipeline metrics updated",
			zap.String("pipeline_id", event.PipelineID))
	}
}

// MonitorStats 监控器统计信息
type MonitorStats struct {
	TotalPipelines     int   `json:"total_pipelines"`
	HealthyPipelines   int   `json:"healthy_pipelines"`
	UnhealthyPipelines int   `json:"unhealthy_pipelines"`
	UnknownPipelines   int   `json:"unknown_pipelines"`
	TotalAlerts        int64 `json:"total_alerts"`
}
