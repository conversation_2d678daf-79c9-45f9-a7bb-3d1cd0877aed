package pipeline

import (
	"context"
	"fmt"
	"sync"
	"time"

	"aiops/pkg/pipeline"
	pb "aiops/pkg/proto"

	"go.uber.org/zap"
)

// Manager Agent流水线管理器
type Manager struct {
	// 基础配置
	agentID string
	logger  *zap.Logger

	// 流水线管理
	pipelines       map[string]*pipeline.Pipeline
	pipelineConfigs map[string]*pb.PipelineConfig

	// 插件管理
	pluginManager *PluginManager

	// 执行引擎
	engines map[string]*pipeline.ExecutionEngine

	// 通信管理
	controlPlaneClient ControlPlaneClient

	// 状态管理
	running bool
	ctx     context.Context
	cancel  context.CancelFunc
	mutex   sync.RWMutex

	// 监控和指标
	metricsCollector *MetricsCollector
	statusReporter   *StatusReporter
}

// ControlPlaneClient Control Plane客户端接口
type ControlPlaneClient interface {
	StreamPipelines(ctx context.Context) (pb.AgentService_StreamPipelinesClient, error)
	SendPipelineStatus(status *pb.PipelineStatus) error
	GetPipelineTemplates(req *pb.PipelineTemplateRequest) (*pb.PipelineTemplateResponse, error)
}

// MetricsCollector 指标收集器
type MetricsCollector struct {
	manager  *Manager
	logger   *zap.Logger
	interval time.Duration
	stopCh   chan struct{}
}

// StatusReporter 状态报告器
type StatusReporter struct {
	manager *Manager
	logger  *zap.Logger
	client  ControlPlaneClient
	stopCh  chan struct{}
}

// NewManager 创建新的流水线管理器
func NewManager(agentID string, logger *zap.Logger) *Manager {
	ctx, cancel := context.WithCancel(context.Background())

	return &Manager{
		agentID:         agentID,
		logger:          logger,
		pipelines:       make(map[string]*pipeline.Pipeline),
		pipelineConfigs: make(map[string]*pb.PipelineConfig),
		engines:         make(map[string]*pipeline.ExecutionEngine),
		ctx:             ctx,
		cancel:          cancel,
		metricsCollector: &MetricsCollector{
			interval: time.Minute,
			stopCh:   make(chan struct{}),
		},
		statusReporter: &StatusReporter{
			stopCh: make(chan struct{}),
		},
	}
}

// SetControlPlaneClient 设置Control Plane客户端
func (m *Manager) SetControlPlaneClient(client ControlPlaneClient) {
	m.controlPlaneClient = client
	m.statusReporter.client = client
}

// SetPluginManager 设置插件管理器
func (m *Manager) SetPluginManager(pluginManager *PluginManager) {
	m.pluginManager = pluginManager
}

// Start 启动流水线管理器
func (m *Manager) Start() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.running {
		return fmt.Errorf("pipeline manager already running")
	}

	m.logger.Info("Starting pipeline manager", zap.String("agent_id", m.agentID))

	// 初始化组件
	m.metricsCollector.manager = m
	m.metricsCollector.logger = m.logger.Named("metrics")
	m.statusReporter.manager = m
	m.statusReporter.logger = m.logger.Named("status")

	// 启动Control Plane连接
	if err := m.startControlPlaneConnection(); err != nil {
		return fmt.Errorf("failed to start control plane connection: %w", err)
	}

	// 启动指标收集
	go m.metricsCollector.start()

	// 启动状态报告
	go m.statusReporter.start()

	m.running = true
	m.logger.Info("Pipeline manager started successfully")

	return nil
}

// Stop 停止流水线管理器
func (m *Manager) Stop() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if !m.running {
		return fmt.Errorf("pipeline manager not running")
	}

	m.logger.Info("Stopping pipeline manager")

	// 停止所有流水线
	for pipelineID, engine := range m.engines {
		if err := engine.Stop(); err != nil {
			m.logger.Error("Failed to stop pipeline",
				zap.String("pipeline_id", pipelineID),
				zap.Error(err))
		}
	}

	// 停止组件
	close(m.metricsCollector.stopCh)
	close(m.statusReporter.stopCh)

	// 取消上下文
	m.cancel()

	m.running = false
	m.logger.Info("Pipeline manager stopped")

	return nil
}

// CreatePipeline 创建流水线
func (m *Manager) CreatePipeline(config *pb.PipelineConfig) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	pipelineID := config.PipelineId
	if _, exists := m.pipelines[pipelineID]; exists {
		return fmt.Errorf("pipeline already exists: %s", pipelineID)
	}

	m.logger.Info("Creating pipeline",
		zap.String("pipeline_id", pipelineID),
		zap.String("name", config.Name))

	// 转换配置
	pipelineConfig, err := m.convertPbConfigToPipeline(config)
	if err != nil {
		return fmt.Errorf("failed to convert config: %w", err)
	}

	// 创建流水线实例
	pipelineInstance := &pipeline.Pipeline{
		ID:          pipelineID,
		Name:        config.Name,
		Description: config.Description,
		Version:     config.Version,
		Config:      pipelineConfig,
		State:       pipeline.StateIdle,
		Metrics:     pipeline.NewPipelineMetrics(),
	}

	// 创建采集器和处理器
	if err := m.setupPipelinePlugins(pipelineInstance, config); err != nil {
		return fmt.Errorf("failed to setup pipeline plugins: %w", err)
	}

	// 保存流水线
	m.pipelines[pipelineID] = pipelineInstance
	m.pipelineConfigs[pipelineID] = config

	m.logger.Info("Pipeline created successfully",
		zap.String("pipeline_id", pipelineID))

	return nil
}

// StartPipeline 启动流水线
func (m *Manager) StartPipeline(pipelineID string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	pipelineInstance, exists := m.pipelines[pipelineID]
	if !exists {
		return fmt.Errorf("pipeline not found: %s", pipelineID)
	}

	if pipelineInstance.State == pipeline.StateRunning {
		return fmt.Errorf("pipeline already running: %s", pipelineID)
	}

	m.logger.Info("Starting pipeline", zap.String("pipeline_id", pipelineID))

	// 创建执行引擎
	engine := pipeline.NewExecutionEngine(pipelineInstance, m.logger)

	// 启动执行引擎
	if err := engine.Start(m.ctx); err != nil {
		return fmt.Errorf("failed to start execution engine: %w", err)
	}

	// 保存引擎引用
	m.engines[pipelineID] = engine

	m.logger.Info("Pipeline started successfully",
		zap.String("pipeline_id", pipelineID))

	return nil
}

// StopPipeline 停止流水线
func (m *Manager) StopPipeline(pipelineID string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	engine, exists := m.engines[pipelineID]
	if !exists {
		return fmt.Errorf("pipeline engine not found: %s", pipelineID)
	}

	m.logger.Info("Stopping pipeline", zap.String("pipeline_id", pipelineID))

	// 停止执行引擎
	if err := engine.Stop(); err != nil {
		return fmt.Errorf("failed to stop execution engine: %w", err)
	}

	// 移除引擎引用
	delete(m.engines, pipelineID)

	m.logger.Info("Pipeline stopped successfully",
		zap.String("pipeline_id", pipelineID))

	return nil
}

// DeletePipeline 删除流水线
func (m *Manager) DeletePipeline(pipelineID string) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 如果正在运行，先停止
	if engine, exists := m.engines[pipelineID]; exists {
		if err := engine.Stop(); err != nil {
			m.logger.Error("Failed to stop pipeline before deletion",
				zap.String("pipeline_id", pipelineID),
				zap.Error(err))
		}
		delete(m.engines, pipelineID)
	}

	// 删除流水线
	delete(m.pipelines, pipelineID)
	delete(m.pipelineConfigs, pipelineID)

	m.logger.Info("Pipeline deleted", zap.String("pipeline_id", pipelineID))

	return nil
}

// GetPipeline 获取流水线
func (m *Manager) GetPipeline(pipelineID string) (*pipeline.Pipeline, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	pipelineInstance, exists := m.pipelines[pipelineID]
	if !exists {
		return nil, fmt.Errorf("pipeline not found: %s", pipelineID)
	}

	return pipelineInstance, nil
}

// ListPipelines 列出所有流水线
func (m *Manager) ListPipelines() []*pipeline.Pipeline {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	pipelines := make([]*pipeline.Pipeline, 0, len(m.pipelines))
	for _, p := range m.pipelines {
		pipelines = append(pipelines, p)
	}

	return pipelines
}

// GetPipelineStatus 获取流水线状态
func (m *Manager) GetPipelineStatus(pipelineID string) (*pb.PipelineStatus, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	pipelineInstance, exists := m.pipelines[pipelineID]
	if !exists {
		return nil, fmt.Errorf("pipeline not found: %s", pipelineID)
	}

	// 转换状态
	status := &pb.PipelineStatus{
		PipelineId:          pipelineID,
		AgentId:             m.agentID,
		Status:              string(pipelineInstance.State),
		StartTimestamp:      pipelineInstance.StartTime.Unix(),
		LastUpdateTimestamp: time.Now().Unix(),
	}

	// 添加指标数据
	if pipelineInstance.Metrics != nil {
		metrics := m.convertPipelineMetricsToPb(pipelineInstance.Metrics)
		status.Metrics = metrics
	}

	return status, nil
}

// startControlPlaneConnection 启动Control Plane连接
func (m *Manager) startControlPlaneConnection() error {
	if m.controlPlaneClient == nil {
		return fmt.Errorf("control plane client not set")
	}

	// 启动流水线配置流
	go m.handlePipelineStream()

	return nil
}

// handlePipelineStream 处理流水线配置流
func (m *Manager) handlePipelineStream() {
	for {
		select {
		case <-m.ctx.Done():
			return
		default:
			if err := m.processPipelineStream(); err != nil {
				m.logger.Error("Pipeline stream error", zap.Error(err))
				time.Sleep(5 * time.Second) // 重试延迟
			}
		}
	}
}

// processPipelineStream 处理流水线流
func (m *Manager) processPipelineStream() error {
	stream, err := m.controlPlaneClient.StreamPipelines(m.ctx)
	if err != nil {
		return fmt.Errorf("failed to create pipeline stream: %w", err)
	}

	// 发送初始状态
	initialStatus := &pb.PipelineStatus{
		AgentId:             m.agentID,
		Status:              "connected",
		LastUpdateTimestamp: time.Now().Unix(),
	}

	if err := stream.Send(initialStatus); err != nil {
		return fmt.Errorf("failed to send initial status: %w", err)
	}

	// 接收配置
	for {
		config, err := stream.Recv()
		if err != nil {
			return fmt.Errorf("failed to receive pipeline config: %w", err)
		}

		if err := m.handlePipelineConfig(config); err != nil {
			m.logger.Error("Failed to handle pipeline config",
				zap.String("pipeline_id", config.PipelineId),
				zap.Error(err))
		}
	}
}

// handlePipelineConfig 处理流水线配置
func (m *Manager) handlePipelineConfig(config *pb.PipelineConfig) error {
	pipelineID := config.PipelineId

	// 检查是否已存在
	if _, exists := m.pipelines[pipelineID]; exists {
		// 更新配置
		return m.updatePipelineConfig(config)
	} else {
		// 创建新流水线
		if err := m.CreatePipeline(config); err != nil {
			return err
		}

		// 如果配置为启用，自动启动
		if config.Enabled {
			return m.StartPipeline(pipelineID)
		}
	}

	return nil
}

// updatePipelineConfig 更新流水线配置
func (m *Manager) updatePipelineConfig(config *pb.PipelineConfig) error {
	pipelineID := config.PipelineId

	// 如果流水线正在运行，先停止
	if _, running := m.engines[pipelineID]; running {
		if err := m.StopPipeline(pipelineID); err != nil {
			return fmt.Errorf("failed to stop pipeline for update: %w", err)
		}
	}

	// 删除旧配置
	delete(m.pipelines, pipelineID)
	delete(m.pipelineConfigs, pipelineID)

	// 创建新配置
	if err := m.CreatePipeline(config); err != nil {
		return fmt.Errorf("failed to recreate pipeline: %w", err)
	}

	// 如果配置为启用，重新启动
	if config.Enabled {
		return m.StartPipeline(pipelineID)
	}

	return nil
}

// convertPbConfigToPipeline 转换protobuf配置到流水线配置
func (m *Manager) convertPbConfigToPipeline(config *pb.PipelineConfig) (*pipeline.PipelineConfig, error) {
	pipelineConfig := &pipeline.PipelineConfig{
		Enabled:         config.Enabled,
		BufferSize:      int(config.BufferSize),
		WorkerCount:     int(config.WorkerCount),
		Timeout:         time.Duration(config.TimeoutSeconds) * time.Second,
		RetryAttempts:   int(config.RetryAttempts),
		RetryDelay:      time.Duration(config.RetryDelaySeconds) * time.Second,
		EnableMetrics:   config.EnableMetrics,
		EnableTracing:   config.EnableTracing,
		MetricsInterval: time.Duration(config.MetricsIntervalSeconds) * time.Second,
	}

	// 转换采集器配置
	if config.Collector != nil {
		pipelineConfig.Collector = pipeline.CollectorConfig{
			Name:     config.Collector.Name,
			Type:     config.Collector.Type,
			Interval: time.Duration(config.Collector.IntervalSeconds) * time.Second,
			Config:   convertStringMapToInterface(config.Collector.Config),
			Enabled:  config.Collector.Enabled,
		}
	}

	// 转换处理器配置
	pipelineConfig.Processors = make([]pipeline.ProcessorConfig, 0, len(config.Processors))
	for _, processor := range config.Processors {
		pipelineConfig.Processors = append(pipelineConfig.Processors, pipeline.ProcessorConfig{
			Name:        processor.Name,
			Type:        processor.Type,
			Enabled:     processor.Enabled,
			Config:      convertStringMapToInterface(processor.Config),
			Concurrency: int(processor.Concurrency),
			Timeout:     time.Duration(processor.TimeoutSeconds) * time.Second,
			Order:       int(processor.Order),
		})
	}

	// 设置默认值
	if pipelineConfig.BufferSize <= 0 {
		pipelineConfig.BufferSize = 1000
	}
	if pipelineConfig.WorkerCount <= 0 {
		pipelineConfig.WorkerCount = 2
	}
	if pipelineConfig.Timeout <= 0 {
		pipelineConfig.Timeout = 30 * time.Second
	}

	return pipelineConfig, nil
}

// setupPipelinePlugins 设置流水线插件
func (m *Manager) setupPipelinePlugins(pipelineInstance *pipeline.Pipeline, config *pb.PipelineConfig) error {
	if m.pluginManager == nil {
		return fmt.Errorf("plugin manager not set")
	}

	// 创建采集器插件
	if config.Collector != nil {
		collector, err := m.pluginManager.CreateCollectorPlugin(config.Collector)
		if err != nil {
			return fmt.Errorf("failed to create collector plugin: %w", err)
		}
		pipelineInstance.Collector = collector
	}

	// 创建处理器插件
	processors := make([]pipeline.PipelinePlugin, 0, len(config.Processors))
	for _, processorConfig := range config.Processors {
		if processorConfig.Enabled {
			processor, err := m.pluginManager.CreateProcessorPlugin(processorConfig)
			if err != nil {
				return fmt.Errorf("failed to create processor plugin %s: %w", processorConfig.Name, err)
			}
			processors = append(processors, processor)
		}
	}
	pipelineInstance.Processors = processors

	return nil
}

// convertPipelineMetricsToPb 转换流水线指标到protobuf
func (m *Manager) convertPipelineMetricsToPb(metrics *pipeline.PipelineMetrics) *pb.PipelineMetrics {
	snapshot := metrics.GetSnapshot()

	pbMetrics := &pb.PipelineMetrics{
		CollectedCount:      snapshot.CollectedCount,
		ProcessedCount:      snapshot.ProcessedCount,
		ErrorCount:          snapshot.ErrorCount,
		DroppedCount:        snapshot.DroppedCount,
		AvgLatencyMs:        snapshot.AvgLatency.Milliseconds(),
		P95LatencyMs:        snapshot.P95Latency.Milliseconds(),
		P99LatencyMs:        snapshot.P99Latency.Milliseconds(),
		Throughput:          snapshot.Throughput,
		MemoryUsageMb:       snapshot.MemoryUsage / 1024 / 1024,
		CpuUsagePercent:     snapshot.CPUUsage,
		GoroutineCount:      int32(snapshot.GoroutineCount),
		ChannelSize:         int32(snapshot.ChannelSize),
		LastUpdateTimestamp: snapshot.LastUpdate.Unix(),
		PluginMetrics:       make(map[string]*pb.PluginMetrics),
	}

	// 转换插件指标
	for name, pluginMetrics := range snapshot.PluginMetrics {
		pbMetrics.PluginMetrics[name] = &pb.PluginMetrics{
			Name:                 pluginMetrics.Name,
			ProcessedCount:       pluginMetrics.ProcessedCount,
			ErrorCount:           pluginMetrics.ErrorCount,
			AvgLatencyMs:         pluginMetrics.AvgLatency.Milliseconds(),
			LastProcessTimestamp: pluginMetrics.LastProcessTime.Unix(),
			Status:               pluginMetrics.Status,
			CustomMetrics:        convertInterfaceMapToString(pluginMetrics.CustomMetrics),
		}
	}

	return pbMetrics
}

// convertStringMapToInterface 转换string map到interface map
func convertStringMapToInterface(stringMap map[string]string) map[string]interface{} {
	interfaceMap := make(map[string]interface{})
	for k, v := range stringMap {
		interfaceMap[k] = v
	}
	return interfaceMap
}

// convertInterfaceMapToString 转换interface map到string map
func convertInterfaceMapToString(interfaceMap map[string]interface{}) map[string]string {
	stringMap := make(map[string]string)
	for k, v := range interfaceMap {
		stringMap[k] = fmt.Sprintf("%v", v)
	}
	return stringMap
}

// start 启动指标收集器
func (mc *MetricsCollector) start() {
	ticker := time.NewTicker(mc.interval)
	defer ticker.Stop()

	for {
		select {
		case <-mc.stopCh:
			return
		case <-ticker.C:
			mc.collect()
		}
	}
}

// collect 收集指标
func (mc *MetricsCollector) collect() {
	mc.manager.mutex.RLock()
	defer mc.manager.mutex.RUnlock()

	for pipelineID, pipelineInstance := range mc.manager.pipelines {
		if pipelineInstance.Metrics != nil {
			mc.logger.Debug("Collecting metrics",
				zap.String("pipeline_id", pipelineID),
				zap.Int64("processed", pipelineInstance.Metrics.ProcessedCount),
				zap.Int64("errors", pipelineInstance.Metrics.ErrorCount))
		}
	}
}

// start 启动状态报告器
func (sr *StatusReporter) start() {
	ticker := time.NewTicker(30 * time.Second) // 每30秒报告一次状态
	defer ticker.Stop()

	for {
		select {
		case <-sr.stopCh:
			return
		case <-ticker.C:
			sr.reportStatus()
		}
	}
}

// reportStatus 报告状态
func (sr *StatusReporter) reportStatus() {
	if sr.client == nil {
		return
	}

	sr.manager.mutex.RLock()
	defer sr.manager.mutex.RUnlock()

	for pipelineID := range sr.manager.pipelines {
		status, err := sr.manager.GetPipelineStatus(pipelineID)
		if err != nil {
			sr.logger.Error("Failed to get pipeline status",
				zap.String("pipeline_id", pipelineID),
				zap.Error(err))
			continue
		}

		if err := sr.client.SendPipelineStatus(status); err != nil {
			sr.logger.Error("Failed to send pipeline status",
				zap.String("pipeline_id", pipelineID),
				zap.Error(err))
		}
	}
}
