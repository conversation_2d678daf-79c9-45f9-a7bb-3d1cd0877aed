package pipeline

import (
	"testing"

	cf "aiops/pkg/config"
	"aiops/pkg/log"
)

// TestPluginManagerBasic 测试插件管理器基本功能
func TestPluginManagerBasic(t *testing.T) {
	// 创建日志
	logConfig := cf.NewConfig("../../../config/agent_config.yaml")
	logger := log.NewLog(logConfig)

	// 创建插件管理器
	pluginManager := NewPluginManager("../../../plugins/build", logger.Logger)
	if err := pluginManager.Initialize(); err != nil {
		t.Fatalf("初始化插件管理器失败: %v", err)
	}

	t.Log("插件管理器初始化成功")
}
