package internal

import (
	"context"
	"fmt"
	"sync"
	"time"

	pb "aiops/pkg/proto"
	plugininterface "aiops/plugins/interface"

	"go.uber.org/zap"
)

// TaskType 任务类型
type TaskType string

const (
	CollectionTask TaskType = "collection"
	AnalysisTask   TaskType = "analysis"
	AlertingTask   TaskType = "alerting"
	ProcessingTask TaskType = "processing"
)

// TaskConfig 统一的任务配置
type TaskConfig struct {
	ID       string                 `json:"id"`
	Type     TaskType               `json:"type"`
	Priority int                    `json:"priority"`
	Config   map[string]interface{} `json:"config"`

	// 分析任务特定配置
	AnalysisConfig *AnalysisTaskConfig `json:"analysis_config,omitempty"`

	// 警报任务特定配置
	AlertingConfig *AlertingTaskConfig `json:"alerting_config,omitempty"`
}

// AnalysisTaskConfig 分析任务配置
type AnalysisTaskConfig struct {
	AnalyzerPluginID string                 `json:"analyzer_plugin_id"`
	DataSources      []string               `json:"data_sources"`
	Thresholds       map[string]float64     `json:"thresholds"`
	AlerterConfigs   []AlerterConfig        `json:"alerter_configs"`
	Config           map[string]interface{} `json:"config"`
}

// AlertingTaskConfig 警报任务配置
type AlertingTaskConfig struct {
	AlerterPluginID string                 `json:"alerter_plugin_id"`
	Rules           []AlertRule            `json:"rules"`
	Config          map[string]interface{} `json:"config"`
}

// AlerterConfig 警报器配置
type AlerterConfig struct {
	PluginID   string                 `json:"plugin_id"`
	Channels   []string               `json:"channels"`
	Conditions map[string]interface{} `json:"conditions"`
	Config     map[string]interface{} `json:"config"`
}

// AlertRule 警报规则
type AlertRule struct {
	ID        string                 `json:"id"`
	Name      string                 `json:"name"`
	Condition string                 `json:"condition"`
	Threshold float64                `json:"threshold"`
	Severity  string                 `json:"severity"`
	Config    map[string]interface{} `json:"config"`
}

// TaskExecutionContext 任务执行上下文
type TaskExecutionContext struct {
	TaskID    string
	Type      TaskType
	StartTime time.Time
	Context   context.Context
	Cancel    context.CancelFunc
	Status    string
	Error     error
	Results   interface{}
	Metadata  map[string]interface{}
}

// StatusCallback 状态回调函数
type StatusCallback func(*pb.TaskStatus)

// AnalysisResult 分析结果（简化版本）
type AnalysisResult struct {
	TaskID          string                     `json:"task_id"`
	Anomalies       []*plugininterface.Anomaly `json:"anomalies"`
	Metrics         map[string]float64         `json:"metrics"`
	Timestamp       time.Time                  `json:"timestamp"`
	Confidence      float64                    `json:"confidence"`
	Severity        string                     `json:"severity"`
	AlertsTriggered []string                   `json:"alerts_triggered"`
}

// TaskManager 任务管理器
type TaskManager struct {
	mu             sync.RWMutex
	pluginManager  *AgentPluginManager
	logger         *zap.Logger
	statusCallback StatusCallback

	// 任务管理
	activeTasks map[string]*TaskExecutionContext
	taskQueue   chan *TaskConfig
	workerCount int

	// 插件协调
	collectorPlugins map[string]plugininterface.Collector
	analyzerPlugins  map[string]plugininterface.Analyzer
	alerterPlugins   map[string]plugininterface.Alerter
	processorPlugins map[string]plugininterface.Processor

	// 数据流
	metricDataCh     chan *pb.MetricData
	analysisResultCh chan *AnalysisResult
	alertEventCh     chan *plugininterface.AlertEvent

	// 运行状态
	running    bool
	shutdownCh chan struct{}
}

// NewTaskManager 创建新的任务管理器
func NewTaskManager(pluginManager *AgentPluginManager, logger *zap.Logger) *TaskManager {
	return &TaskManager{
		pluginManager:    pluginManager,
		logger:           logger,
		activeTasks:      make(map[string]*TaskExecutionContext),
		taskQueue:        make(chan *TaskConfig, 100),
		workerCount:      5,
		collectorPlugins: make(map[string]plugininterface.Collector),
		analyzerPlugins:  make(map[string]plugininterface.Analyzer),
		alerterPlugins:   make(map[string]plugininterface.Alerter),
		processorPlugins: make(map[string]plugininterface.Processor),
		metricDataCh:     make(chan *pb.MetricData, 1000),
		analysisResultCh: make(chan *AnalysisResult, 100),
		alertEventCh:     make(chan *plugininterface.AlertEvent, 100),
		shutdownCh:       make(chan struct{}),
	}
}

// SetStatusCallback 设置状态回调
func (tm *TaskManager) SetStatusCallback(callback StatusCallback) {
	tm.statusCallback = callback
}

// Start 启动任务管理器
func (tm *TaskManager) Start(ctx context.Context) error {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	if tm.running {
		return fmt.Errorf("task manager is already running")
	}

	tm.running = true

	// 启动工作器
	for i := 0; i < tm.workerCount; i++ {
		go tm.taskWorker(ctx, i)
	}

	// 启动分析结果处理器
	go tm.analysisResultProcessor(ctx)

	// 启动指标数据处理器
	go tm.metricDataProcessor(ctx)

	tm.logger.Info("Task manager started",
		zap.Int("worker_count", tm.workerCount))

	return nil
}

// Stop 停止任务管理器
func (tm *TaskManager) Stop() error {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	if !tm.running {
		return nil
	}

	tm.running = false
	close(tm.shutdownCh)

	// 取消所有活动任务
	for _, task := range tm.activeTasks {
		if task.Cancel != nil {
			task.Cancel()
		}
	}

	tm.logger.Info("Task manager stopped")
	return nil
}

// StartCollector 启动采集器任务（兼容原有接口）
func (tm *TaskManager) StartCollector(taskConfig *pb.CollectorTaskConfig, statusCallback StatusCallback) error {
	// 转换为新的任务配置格式
	config := &TaskConfig{
		ID:       taskConfig.TaskId,
		Type:     CollectionTask,
		Priority: 5,
		Config: map[string]interface{}{
			"device_id":         taskConfig.DeviceId,
			"device_name":       taskConfig.DeviceName,
			"device_type":       taskConfig.DeviceType,
			"host":              taskConfig.Host,
			"port":              taskConfig.Port,
			"username":          taskConfig.Username,
			"password":          taskConfig.Password,
			"connect_params":    taskConfig.ConnectParams,
			"frequency_seconds": taskConfig.FrequencySeconds,
			"collect_items":     taskConfig.CollectItems,
			"is_enabled":        taskConfig.IsEnabled,
		},
	}

	return tm.SubmitTask(config)
}

// StopCollector 停止采集器任务
func (tm *TaskManager) StopCollector(taskID string) error {
	return tm.CancelTask(taskID)
}

// SubmitTask 提交任务
func (tm *TaskManager) SubmitTask(taskConfig *TaskConfig) error {
	if !tm.running {
		return fmt.Errorf("task manager is not running")
	}

	select {
	case tm.taskQueue <- taskConfig:
		tm.logger.Info("Task submitted",
			zap.String("task_id", taskConfig.ID),
			zap.String("type", string(taskConfig.Type)))
		return nil
	default:
		return fmt.Errorf("task queue is full")
	}
}

// CancelTask 取消任务
func (tm *TaskManager) CancelTask(taskID string) error {
	tm.mu.Lock()
	defer tm.mu.Unlock()

	task, exists := tm.activeTasks[taskID]
	if !exists {
		return fmt.Errorf("task not found: %s", taskID)
	}

	if task.Cancel != nil {
		task.Cancel()
	}

	tm.updateTaskStatus(taskID, "cancelled", "")

	tm.logger.Info("Task cancelled",
		zap.String("task_id", taskID))

	return nil
}

// GetTaskStatus 获取任务状态
func (tm *TaskManager) GetTaskStatus(taskID string) (*TaskExecutionContext, bool) {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	task, exists := tm.activeTasks[taskID]
	return task, exists
}

// ListActiveTasks 列出活动任务
func (tm *TaskManager) ListActiveTasks() map[string]*TaskExecutionContext {
	tm.mu.RLock()
	defer tm.mu.RUnlock()

	result := make(map[string]*TaskExecutionContext)
	for id, task := range tm.activeTasks {
		result[id] = task
	}

	return result
}

// GetMetricDataChannel 获取指标数据通道
func (tm *TaskManager) GetMetricDataChannel() <-chan *pb.MetricData {
	return tm.metricDataCh
}

// GetAnalysisResultChannel 获取分析结果通道
func (tm *TaskManager) GetAnalysisResultChannel() <-chan *AnalysisResult {
	return tm.analysisResultCh
}

// GetAlertEventChannel 获取警报事件通道
func (tm *TaskManager) GetAlertEventChannel() <-chan *plugininterface.AlertEvent {
	return tm.alertEventCh
}

// taskWorker 任务工作器
func (tm *TaskManager) taskWorker(ctx context.Context, workerID int) {
	tm.logger.Info("Task worker started",
		zap.Int("worker_id", workerID))

	for {
		select {
		case <-ctx.Done():
			tm.logger.Info("Task worker stopped due to context cancellation",
				zap.Int("worker_id", workerID))
			return
		case <-tm.shutdownCh:
			tm.logger.Info("Task worker stopped due to shutdown",
				zap.Int("worker_id", workerID))
			return
		case taskConfig := <-tm.taskQueue:
			tm.executeTask(ctx, taskConfig, workerID)
		}
	}
}

// executeTask 执行任务
func (tm *TaskManager) executeTask(parentCtx context.Context, taskConfig *TaskConfig, workerID int) {
	taskCtx, cancel := context.WithCancel(parentCtx)
	defer cancel()

	// 创建任务执行上下文
	execCtx := &TaskExecutionContext{
		TaskID:    taskConfig.ID,
		Type:      taskConfig.Type,
		StartTime: time.Now(),
		Context:   taskCtx,
		Cancel:    cancel,
		Status:    "running",
		Metadata:  make(map[string]interface{}),
	}

	// 注册任务
	tm.mu.Lock()
	tm.activeTasks[taskConfig.ID] = execCtx
	tm.mu.Unlock()

	// 延迟清理
	defer func() {
		tm.mu.Lock()
		delete(tm.activeTasks, taskConfig.ID)
		tm.mu.Unlock()
	}()

	// 更新状态为运行中
	tm.updateTaskStatus(taskConfig.ID, "running", "")

	tm.logger.Info("Executing task",
		zap.String("task_id", taskConfig.ID),
		zap.String("type", string(taskConfig.Type)),
		zap.Int("worker_id", workerID))

	// 根据任务类型执行
	var err error
	switch taskConfig.Type {
	case CollectionTask:
		err = tm.executeCollectionTask(taskCtx, taskConfig, execCtx)
	case AnalysisTask:
		err = tm.executeAnalysisTask(taskCtx, taskConfig, execCtx)
	case AlertingTask:
		err = tm.executeAlertingTask(taskCtx, taskConfig, execCtx)
	case ProcessingTask:
		err = tm.executeProcessingTask(taskCtx, taskConfig, execCtx)
	default:
		err = fmt.Errorf("unsupported task type: %s", taskConfig.Type)
	}

	// 更新最终状态
	if err != nil {
		execCtx.Error = err
		tm.updateTaskStatus(taskConfig.ID, "failed", err.Error())
		tm.logger.Error("Task execution failed",
			zap.String("task_id", taskConfig.ID),
			zap.Error(err))
	} else {
		tm.updateTaskStatus(taskConfig.ID, "completed", "")
		tm.logger.Info("Task execution completed",
			zap.String("task_id", taskConfig.ID))
	}
}

// updateTaskStatus 更新任务状态
func (tm *TaskManager) updateTaskStatus(taskID, status, errorMsg string) {
	if tm.statusCallback != nil {
		statusUpdate := &pb.TaskStatus{
			TaskId:               taskID,
			Status:               status,
			ErrorMessage:         errorMsg,
			LastCollectTimestamp: time.Now().Unix(),
		}
		tm.statusCallback(statusUpdate)
	}
}
