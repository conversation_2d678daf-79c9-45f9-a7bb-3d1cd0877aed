# DevInsight 示例流水线配置
# 展示完整的系统监控 -> 阈值检测 -> 邮件告警流水线

pipeline_id: "system-monitoring-demo"
name: "系统监控演示流水线"
description: "完整的系统监控、阈值检测和邮件告警演示"
version: "1.0"
enabled: true

# 基础配置
buffer_size: 1000
worker_count: 2
timeout_seconds: 30
retry_attempts: 3
retry_delay_seconds: 5

# 监控配置
enable_metrics: true
enable_tracing: false
metrics_interval_seconds: 60

# 采集器配置
collector:
  name: "system_collector"
  type: "system"
  interval_seconds: 30
  enabled: true
  config:
    enable_cpu: "true"
    enable_memory: "true"
    enable_disk: "true"
    enable_network: "true"
    interval: "30s"

# 处理器链配置
processors:
  # 阈值检测处理器
  - name: "threshold_processor"
    type: "processor"
    enabled: true
    concurrency: 1
    timeout_seconds: 10
    order: 1
    config:
      thresholds: |
        {
          "system.memory.alloc": 1073741824,
          "system.memory.sys": 2147483648,
          "system.goroutines": 1000
        }
  
  # 邮件告警器
  - name: "email_alerter"
    type: "alerter"
    enabled: true
    concurrency: 1
    timeout_seconds: 30
    order: 2
    config:
      smtp_server: "smtp.example.com"
      smtp_port: "587"
      username: "<EMAIL>"
      password: "your_password"
      recipients: "<EMAIL>,<EMAIL>"

# 错误处理配置
error_handling:
  strategy: "retry"
  max_retries: 3
  retry_delay_seconds: 5
  circuit_breaker:
    enabled: true
    failure_threshold: 5
    recovery_timeout_seconds: 60
    half_open_requests: 3
  dead_letter:
    enabled: true
    queue_size: 1000
    persistent: true
    path: "/var/log/devinsight/dead_letter"

# 资源限制配置
resource_limits:
  max_memory_mb: 256
  max_cpu_percent: 50
  max_goroutines: 100
  process_timeout_seconds: 30
  collect_timeout_seconds: 10

# 创建和更新时间（由系统自动填充）
created_at: 0
updated_at: 0
