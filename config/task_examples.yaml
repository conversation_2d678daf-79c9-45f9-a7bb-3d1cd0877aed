# 任务管理器配置示例
# 支持多种任务类型：collection、analysis、alerting、processing

# 采集任务配置示例
collection_tasks:
  - id: "system_metrics_task"
    type: "collection"
    priority: 5
    config:
      device_id: "server-001"
      device_name: "主要服务器"
      device_type: "linux"
      host: "*************"
      port: 22
      username: "admin"
      password: "password"
      connect_params:
        ssh_key_path: "/etc/ssh/id_rsa"
      frequency_seconds: 30
      collect_items:
        - "cpu_usage"
        - "memory_usage"
        - "disk_usage"
        - "network_io"
      is_enabled: true

# 分析任务配置示例
analysis_tasks:
  - id: "anomaly_detection_task"
    type: "analysis"
    priority: 3
    analysis_config:
      analyzer_plugin_id: "enhanced-analyzer"
      data_sources:
        - "system_metrics_task"
      thresholds:
        cpu_usage: 85.0
        memory_usage: 90.0
        disk_usage: 95.0
      alerter_configs:
        - alerter_plugin_id: "email-alerter"
          threshold: 80.0
          conditions:
            - metric: "cpu_usage"
              operator: ">"
              value: 80.0
        - alerter_plugin_id: "email-alerter"
          threshold: 90.0
          conditions:
            - metric: "memory_usage"
              operator: ">"
              value: 90.0
      config:
        window_size: 10
        sensitivity: 0.7
        model_type: "statistical"

# 警报任务配置示例
alerting_tasks:
  - id: "critical_alerts_task"
    type: "alerting"
    priority: 1
    alerting_config:
      alerter_plugin_id: "email-alerter"
      rules:
        - id: "cpu_critical"
          metric: "cpu_usage"
          operator: ">"
          threshold: 95.0
          severity: "critical"
          actions:
            - type: "email"
              config:
                to: ["<EMAIL>"]
                subject: "Critical CPU Usage Alert"
        - id: "memory_critical"
          metric: "memory_usage"
          operator: ">"
          threshold: 95.0
          severity: "critical"
          actions:
            - type: "email"
              config:
                to: ["<EMAIL>"]
                subject: "Critical Memory Usage Alert"
      config:
        cooldown_seconds: 300
        max_alerts_per_minute: 5

# 处理任务配置示例
processing_tasks:
  - id: "data_enrichment_task"
    type: "processing"
    priority: 4
    config:
      processor_type: "data_enricher"
      input_sources:
        - "system_metrics_task"
      output_targets:
        - "enhanced_metrics_storage"
      processing_rules:
        - type: "add_labels"
          config:
            environment: "production"
            datacenter: "dc1"
        - type: "calculate_derived_metrics"
          config:
            cpu_efficiency: "cpu_usage / cpu_cores"
            memory_efficiency: "memory_used / memory_total"

# 协调规则配置示例
coordination_rules:
  - id: "analysis_to_alert_chain"
    name: "分析结果触发警报"
    trigger_type: "analysis_result"
    conditions:
      - field: "anomaly_score"
        operator: ">"
        value: 0.8
    actions:
      - type: "trigger_alerter"
        target_plugin: "email-alerter"
        config:
          severity: "warning"
          message: "检测到异常，异常分数: {{.anomaly_score}}"
  
  - id: "critical_threshold_chain"
    name: "严重阈值响应链"
    trigger_type: "metric_threshold"
    conditions:
      - field: "cpu_usage"
        operator: ">"
        value: 95.0
    actions:
      - type: "trigger_alerter"
        target_plugin: "email-alerter"
        config:
          severity: "critical"
          immediate: true
      - type: "trigger_processor"
        target_plugin: "auto_scaler"
        config:
          action: "scale_up"
          factor: 1.5

# 插件链配置示例
analysis_chains:
  - id: "comprehensive_analysis"
    name: "综合分析链"
    analyzers:
      - "enhanced-analyzer"
      - "simple-analyzer"
    alerters:
      - "email-alerter"
    processors:
      - "data_enricher"
    config:
      parallel_execution: true
      timeout_seconds: 60
    enabled: true

alerting_chains:
  - id: "escalation_chain"
    name: "警报升级链"
    alerters:
      - "email-alerter"
      - "slack-alerter"
      - "pager-duty-alerter"
    rules:
      - id: "level1_alert"
        severity: "warning"
        delay_seconds: 0
        targets: ["email-alerter"]
      - id: "level2_alert"
        severity: "error"
        delay_seconds: 300
        targets: ["email-alerter", "slack-alerter"]
      - id: "level3_alert"
        severity: "critical"
        delay_seconds: 900
        targets: ["email-alerter", "slack-alerter", "pager-duty-alerter"]
