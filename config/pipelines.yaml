# DevInsight 流水线配置文件
# 定义了各种监控场景的流水线配置

pipelines:
  # MySQL监控流水线
  mysql-production:
    enabled: true
    name: "MySQL生产环境监控"
    description: "MySQL数据库性能监控和智能告警"
    version: "1.0"
    
    # 基础配置
    buffer_size: 1000
    worker_count: 4
    timeout: "30s"
    retry_attempts: 3
    retry_delay: "5s"
    
    # 监控配置
    enable_metrics: true
    enable_tracing: true
    metrics_interval: "1m"
    
    # 采集器配置 (必须)
    collector:
      name: "mysql_collector"
      type: "mysql"
      interval: "30s"
      enabled: true
      config:
        host: "mysql-prod.example.com"
        port: "3306"
        username: "monitor"
        password: "${MYSQL_PASSWORD}"
        databases: "app_db,user_db"
        timeout: "10s"
        max_connections: "5"
    
    # 处理器链路配置
    processors:
      # 异常检测器
      - name: "anomaly_detector"
        type: "ml_anomaly"
        enabled: true
        concurrency: 2
        timeout: "10s"
        order: 1
        config:
          algorithm: "isolation_forest"
          sensitivity: "0.05"
          training_window: "7d"
          update_interval: "1h"
          features: "cpu_usage,memory_usage,connection_count,query_time"
      
      # 阈值分析器
      - name: "threshold_analyzer"
        type: "dynamic_threshold"
        enabled: true
        order: 2
        config:
          cpu_usage_warning: "70"
          cpu_usage_critical: "85"
          memory_usage_warning: "80"
          memory_usage_critical: "90"
          connection_count_warning: "800"
          connection_count_critical: "950"
          adaptive: "true"
          baseline_window: "24h"
          adaptation_rate: "0.1"
      
      # 关联分析器
      - name: "correlation_analyzer"
        type: "multi_metric_correlation"
        enabled: true
        order: 3
        config:
          correlation_window: "10m"
          min_correlation_score: "0.7"
          correlation_pairs: "cpu_usage:query_time,memory_usage:connection_count,disk_io:slow_queries"
      
      # 降噪器
      - name: "noise_reducer"
        type: "intelligent_filter"
        enabled: true
        order: 4
        config:
          duplicate_window: "5m"
          max_alerts_per_minute: "5"
          similarity_threshold: "0.8"
          whitelist_patterns: "planned_maintenance.*,backup_operation.*"
          blacklist_patterns: "test_.*"
      
      # 智能告警器
      - name: "smart_alerter"
        type: "multi_channel_alerter"
        enabled: true
        order: 5
        config:
          escalation_levels: "3"
          level1_delay: "0s"
          level1_channels: "slack"
          level1_severity: "warning"
          level2_delay: "10m"
          level2_channels: "email,slack"
          level2_severity: "critical"
          level3_delay: "30m"
          level3_channels: "pagerduty,phone"
          level3_severity: "critical"
          level3_duration: "20m"
          slack_webhook_url: "${SLACK_WEBHOOK_URL}"
          slack_channel: "#mysql-alerts"
          slack_username: "DevInsight"
          smtp_server: "smtp.company.com"
          smtp_port: "587"
          smtp_username: "${SMTP_USERNAME}"
          smtp_password: "${SMTP_PASSWORD}"
          email_recipients: "<EMAIL>,<EMAIL>"
          pagerduty_key: "${PAGERDUTY_KEY}"
          pagerduty_service: "MySQL Production"

    # 错误处理配置
    error_handling:
      strategy: "circuit_breaker"
      max_retries: 3
      retry_delay: "5s"
      circuit_breaker:
        enabled: true
        failure_threshold: 5
        recovery_timeout: "60s"
        half_open_requests: 3
      dead_letter:
        enabled: true
        queue_size: 1000
        persistent: true
        path: "/var/log/devinsight/dead_letter"

    # 资源限制
    resource_limits:
      max_memory_mb: 512
      max_cpu_percent: 50
      max_goroutines: 100
      process_timeout: "30s"
      collect_timeout: "10s"

  # 系统监控流水线  
  system-monitoring:
    enabled: true
    name: "系统资源监控"
    description: "服务器系统资源监控和容量预测"
    version: "1.0"
    
    buffer_size: 500
    worker_count: 2
    timeout: "20s"
    enable_metrics: true
    
    collector:
      name: "system_collector"
      type: "system"
      interval: "1m"
      enabled: true
      config:
        metrics: "cpu,memory,disk,network,load"
        detailed_metrics: "true"
        include_processes: "true"
        top_processes: "10"
    
    processors:
      # 机器学习分析器
      - name: "ml_analyzer"
        type: "ensemble_analyzer"
        enabled: true
        order: 1
        config:
          models: "autoencoder,lstm,prophet"
          ensemble_method: "voting"
          confidence_threshold: "0.8"
      
      # 预测分析器
      - name: "prediction_analyzer"
        type: "capacity_predictor"
        enabled: true
        order: 2
        config:
          forecast_horizon: "7d"
          prediction_interval: "1h"
          disk_usage_threshold: "85"
          memory_usage_threshold: "90"
          trend_detection: "true"
      
      # 建议引擎
      - name: "recommendation_engine"
        type: "optimization_recommender"
        enabled: true
        order: 3
        config:
          recommendation_types: "scaling,optimization,maintenance"
          confidence_threshold: "0.7"
          cost_optimization: "true"
      
      # 邮件告警器
      - name: "email_alerter"
        type: "email"
        enabled: true
        order: 4
        config:
          smtp_server: "smtp.company.com"
          smtp_port: "587"
          recipients: "<EMAIL>"
          template: "system_alert_template"

    error_handling:
      strategy: "retry"
      max_retries: 2
      retry_delay: "3s"

    resource_limits:
      max_memory_mb: 256
      max_cpu_percent: 30
      max_goroutines: 50

  # Redis缓存监控流水线 (简单示例)
  redis-cache:
    enabled: true
    name: "Redis缓存监控"
    description: "Redis缓存性能监控"
    version: "1.0"
    
    buffer_size: 200
    worker_count: 1
    timeout: "15s"
    
    collector:
      name: "redis_collector"
      type: "redis"
      interval: "30s"
      enabled: true
      config:
        host: "redis.example.com"
        port: "6379"
        password: "${REDIS_PASSWORD}"
        db: "0"
    
    processors:
      - name: "simple_threshold"
        type: "static_threshold"
        enabled: true
        order: 1
        config:
          memory_usage_threshold: "80"
          connection_count_threshold: "1000"
          hit_rate_threshold: "0.9"
      
      - name: "webhook_alerter"
        type: "webhook"
        enabled: true
        order: 2
        config:
          url: "https://hooks.company.com/redis-alerts"
          method: "POST"
          authorization: "Bearer ${WEBHOOK_TOKEN}"
          content_type: "application/json"

    error_handling:
      strategy: "ignore"

    resource_limits:
      max_memory_mb: 128
      max_cpu_percent: 20
      max_goroutines: 20

  # 日志分析流水线
  log-analysis:
    enabled: false
    name: "日志分析流水线"
    description: "应用日志分析和异常检测"
    version: "1.0"
    
    buffer_size: 2000
    worker_count: 6
    timeout: "45s"
    
    collector:
      name: "log_collector"
      type: "log_file"
      interval: "10s"
      enabled: true
      config:
        log_paths: "/var/log/app/*.log,/var/log/nginx/*.log"
        follow: "true"
        multiline_pattern: "^\\d{4}-\\d{2}-\\d{2}"
        encoding: "utf-8"
    
    processors:
      - name: "log_parser"
        type: "structured_parser"
        enabled: true
        order: 1
        config:
          patterns: "nginx,apache,json,custom"
          timestamp_field: "timestamp"
          level_field: "level"
          message_field: "message"
      
      - name: "anomaly_detector"
        type: "log_anomaly"
        enabled: true
        order: 2
        config:
          algorithm: "clustering"
          window_size: "5m"
          threshold: "0.1"
      
      - name: "alert_generator"
        type: "log_alerter"
        enabled: true
        order: 3
        config:
          error_patterns: "ERROR,FATAL,Exception"
          rate_threshold: "10"
          time_window: "1m"

    error_handling:
      strategy: "circuit_breaker"
      max_retries: 5
      retry_delay: "2s"
      circuit_breaker:
        enabled: true
        failure_threshold: 10
        recovery_timeout: "120s"

    resource_limits:
      max_memory_mb: 1024
      max_cpu_percent: 70
      max_goroutines: 200
