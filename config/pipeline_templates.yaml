# DevInsight 流水线模板配置
# 定义了常用的流水线模板，可以快速创建新的流水线

templates:
  # MySQL监控模板
  mysql-monitoring:
    id: "mysql-monitoring"
    name: "MySQL数据库监控模板"
    description: "用于监控MySQL数据库性能和健康状态的标准模板"
    category: "database"
    tags: ["mysql", "database", "monitoring", "performance"]
    
    parameters:
      - name: "mysql_host"
        type: "string"
        description: "MySQL服务器地址"
        required: true
        default_value: "localhost"
      
      - name: "mysql_port"
        type: "int"
        description: "MySQL端口号"
        required: false
        default_value: 3306
        validation:
          min: 1
          max: 65535
      
      - name: "mysql_username"
        type: "string"
        description: "MySQL用户名"
        required: true
      
      - name: "mysql_password"
        type: "string"
        description: "MySQL密码"
        required: true
      
      - name: "databases"
        type: "string"
        description: "要监控的数据库列表，逗号分隔"
        required: false
        default_value: ""
      
      - name: "collect_interval"
        type: "string"
        description: "采集间隔"
        required: false
        default_value: "30s"
        allowed_values: ["10s", "30s", "1m", "5m"]
      
      - name: "enable_anomaly_detection"
        type: "bool"
        description: "是否启用异常检测"
        required: false
        default_value: true
      
      - name: "alert_channels"
        type: "string"
        description: "告警通道，逗号分隔"
        required: false
        default_value: "email"
        allowed_values: ["email", "slack", "webhook", "email,slack"]
    
    defaults:
      buffer_size: 1000
      worker_count: 4
      timeout: "30s"
      enable_metrics: true
    
    template:
      enabled: true
      name: "MySQL监控流水线"
      description: "基于模板创建的MySQL监控流水线"
      version: "1.0"
      
      buffer_size: 1000
      worker_count: 4
      timeout: "30s"
      retry_attempts: 3
      retry_delay: "5s"
      
      enable_metrics: true
      enable_tracing: false
      metrics_interval: "1m"
      
      collector:
        name: "mysql_collector"
        type: "mysql"
        interval: "{{.collect_interval}}"
        enabled: true
        config:
          host: "{{.mysql_host}}"
          port: "{{.mysql_port}}"
          username: "{{.mysql_username}}"
          password: "{{.mysql_password}}"
          databases: "{{.databases}}"
          timeout: "10s"
          max_connections: "5"
      
      processors:
        - name: "threshold_analyzer"
          type: "dynamic_threshold"
          enabled: true
          order: 1
          config:
            cpu_usage_warning: "70"
            cpu_usage_critical: "85"
            memory_usage_warning: "80"
            memory_usage_critical: "90"
            connection_count_warning: "800"
            connection_count_critical: "950"
        
        - name: "anomaly_detector"
          type: "ml_anomaly"
          enabled: "{{.enable_anomaly_detection}}"
          order: 2
          config:
            algorithm: "isolation_forest"
            sensitivity: "0.05"
            training_window: "7d"
        
        - name: "alerter"
          type: "multi_channel_alerter"
          enabled: true
          order: 3
          config:
            channels: "{{.alert_channels}}"
            email_recipients: "<EMAIL>"
            slack_channel: "#alerts"

  # 系统监控模板
  system-monitoring:
    id: "system-monitoring"
    name: "系统资源监控模板"
    description: "用于监控服务器系统资源的标准模板"
    category: "system"
    tags: ["system", "cpu", "memory", "disk", "network"]
    
    parameters:
      - name: "collect_interval"
        type: "string"
        description: "采集间隔"
        required: false
        default_value: "1m"
        allowed_values: ["30s", "1m", "5m", "10m"]
      
      - name: "include_processes"
        type: "bool"
        description: "是否包含进程信息"
        required: false
        default_value: true
      
      - name: "top_processes_count"
        type: "int"
        description: "显示的top进程数量"
        required: false
        default_value: 10
        validation:
          min: 1
          max: 50
      
      - name: "cpu_warning_threshold"
        type: "int"
        description: "CPU使用率警告阈值(%)"
        required: false
        default_value: 80
        validation:
          min: 1
          max: 100
      
      - name: "memory_warning_threshold"
        type: "int"
        description: "内存使用率警告阈值(%)"
        required: false
        default_value: 85
        validation:
          min: 1
          max: 100
      
      - name: "disk_warning_threshold"
        type: "int"
        description: "磁盘使用率警告阈值(%)"
        required: false
        default_value: 90
        validation:
          min: 1
          max: 100
    
    defaults:
      buffer_size: 500
      worker_count: 2
      timeout: "20s"
      enable_metrics: true
    
    template:
      enabled: true
      name: "系统监控流水线"
      description: "基于模板创建的系统监控流水线"
      version: "1.0"
      
      buffer_size: 500
      worker_count: 2
      timeout: "20s"
      enable_metrics: true
      
      collector:
        name: "system_collector"
        type: "system"
        interval: "{{.collect_interval}}"
        enabled: true
        config:
          metrics: "cpu,memory,disk,network,load"
          detailed_metrics: "true"
          include_processes: "{{.include_processes}}"
          top_processes: "{{.top_processes_count}}"
      
      processors:
        - name: "threshold_analyzer"
          type: "static_threshold"
          enabled: true
          order: 1
          config:
            cpu_usage_warning: "{{.cpu_warning_threshold}}"
            memory_usage_warning: "{{.memory_warning_threshold}}"
            disk_usage_warning: "{{.disk_warning_threshold}}"
        
        - name: "alerter"
          type: "email"
          enabled: true
          order: 2
          config:
            smtp_server: "smtp.company.com"
            recipients: "<EMAIL>"

  # Redis监控模板
  redis-monitoring:
    id: "redis-monitoring"
    name: "Redis缓存监控模板"
    description: "用于监控Redis缓存性能的标准模板"
    category: "cache"
    tags: ["redis", "cache", "monitoring", "performance"]
    
    parameters:
      - name: "redis_host"
        type: "string"
        description: "Redis服务器地址"
        required: true
        default_value: "localhost"
      
      - name: "redis_port"
        type: "int"
        description: "Redis端口号"
        required: false
        default_value: 6379
        validation:
          min: 1
          max: 65535
      
      - name: "redis_password"
        type: "string"
        description: "Redis密码"
        required: false
        default_value: ""
      
      - name: "redis_db"
        type: "int"
        description: "Redis数据库编号"
        required: false
        default_value: 0
        validation:
          min: 0
          max: 15
      
      - name: "memory_threshold"
        type: "int"
        description: "内存使用率阈值(%)"
        required: false
        default_value: 80
        validation:
          min: 1
          max: 100
      
      - name: "connection_threshold"
        type: "int"
        description: "连接数阈值"
        required: false
        default_value: 1000
        validation:
          min: 1
          max: 10000
    
    defaults:
      buffer_size: 200
      worker_count: 1
      timeout: "15s"
    
    template:
      enabled: true
      name: "Redis监控流水线"
      description: "基于模板创建的Redis监控流水线"
      version: "1.0"
      
      buffer_size: 200
      worker_count: 1
      timeout: "15s"
      
      collector:
        name: "redis_collector"
        type: "redis"
        interval: "30s"
        enabled: true
        config:
          host: "{{.redis_host}}"
          port: "{{.redis_port}}"
          password: "{{.redis_password}}"
          db: "{{.redis_db}}"
      
      processors:
        - name: "threshold_analyzer"
          type: "static_threshold"
          enabled: true
          order: 1
          config:
            memory_usage_threshold: "{{.memory_threshold}}"
            connection_count_threshold: "{{.connection_threshold}}"
            hit_rate_threshold: "90"
        
        - name: "webhook_alerter"
          type: "webhook"
          enabled: true
          order: 2
          config:
            url: "https://hooks.company.com/redis-alerts"
            method: "POST"
            content_type: "application/json"

  # 简单监控模板
  simple-monitoring:
    id: "simple-monitoring"
    name: "简单监控模板"
    description: "最基础的监控模板，适合快速开始"
    category: "basic"
    tags: ["simple", "basic", "starter"]
    
    parameters:
      - name: "collector_type"
        type: "string"
        description: "采集器类型"
        required: true
        allowed_values: ["system", "mysql", "redis", "http"]
      
      - name: "target_host"
        type: "string"
        description: "目标主机"
        required: true
        default_value: "localhost"
      
      - name: "alert_email"
        type: "string"
        description: "告警邮箱"
        required: true
    
    defaults:
      buffer_size: 100
      worker_count: 1
      timeout: "10s"
    
    template:
      enabled: true
      name: "简单监控流水线"
      description: "基于模板创建的简单监控流水线"
      version: "1.0"
      
      buffer_size: 100
      worker_count: 1
      timeout: "10s"
      
      collector:
        name: "simple_collector"
        type: "{{.collector_type}}"
        interval: "1m"
        enabled: true
        config:
          host: "{{.target_host}}"
      
      processors:
        - name: "simple_alerter"
          type: "email"
          enabled: true
          order: 1
          config:
            recipients: "{{.alert_email}}"
            smtp_server: "smtp.company.com"
