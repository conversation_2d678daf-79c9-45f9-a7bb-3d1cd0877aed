// 优化后的插件接口设计
package plugininterface

import (
	pb "aiops/pkg/proto"
	"context"
	"time"
)

// ==================== 基础类型定义 ====================

// PluginType 插件类型
type PluginType string

const (
	CollectorPlugin PluginType = "collector"
	ProcessorPlugin PluginType = "processor"
	AlerterPlugin   PluginType = "alerter"
	AnalyzerPlugin  PluginType = "analyzer"
	EnhancerPlugin  PluginType = "enhancer" // 新增：数据增强插件
	RoutingPlugin   PluginType = "routing"  // 新增：路由插件
)

// PluginPriority 插件优先级
type PluginPriority int

const (
	PriorityLow      PluginPriority = 1
	PriorityNormal   PluginPriority = 5
	PriorityHigh     PluginPriority = 8
	PriorityCritical PluginPriority = 10
)

// PluginStatus 插件状态
type PluginStatus string

const (
	StatusUnknown    PluginStatus = "unknown"
	StatusRegistered PluginStatus = "registered"
	StatusLoading    PluginStatus = "loading"
	StatusLoaded     PluginStatus = "loaded"
	StatusStarting   PluginStatus = "starting"
	StatusRunning    PluginStatus = "running"
	StatusStopping   PluginStatus = "stopping"
	StatusStopped    PluginStatus = "stopped"
	StatusError      PluginStatus = "error"
	StatusUpdating   PluginStatus = "updating"
	StatusUnloading  PluginStatus = "unloading"
)

// ==================== 依赖管理 ====================

// PluginDependency 插件依赖
type PluginDependency struct {
	Name          string   `json:"name"`
	MinVersion    string   `json:"min_version"`
	MaxVersion    string   `json:"max_version"`
	Required      bool     `json:"required"`
	ConflictsWith []string `json:"conflicts_with"`
	LoadOrder     int      `json:"load_order"` // 加载顺序
}

// PluginCapability 插件能力声明
type PluginCapability struct {
	Name        string         `json:"name"`
	Version     string         `json:"version"`
	Description string         `json:"description"`
	Parameters  map[string]any `json:"parameters"`
}

// ==================== 安全和权限 ====================

// PluginPermission 插件权限
type PluginPermission struct {
	CanAccessNetwork     bool                  `json:"can_access_network"`
	CanAccessFileSystem  bool                  `json:"can_access_filesystem"`
	CanExecuteCommands   bool                  `json:"can_execute_commands"`
	CanAccessDatabase    bool                  `json:"can_access_database"`
	AllowedPaths         []string              `json:"allowed_paths"`
	AllowedNetworkHosts  []string              `json:"allowed_network_hosts"`
	AllowedCommands      []string              `json:"allowed_commands"`
	ResourceLimits       *PluginResourceLimits `json:"resource_limits"`
	RequiredCapabilities []string              `json:"required_capabilities"`
}

// PluginResourceLimits 资源限制
type PluginResourceLimits struct {
	MaxCPUPercent    float64       `json:"max_cpu_percent"`
	MaxMemoryMB      int64         `json:"max_memory_mb"`
	MaxDiskMB        int64         `json:"max_disk_mb"`
	MaxGoroutines    int           `json:"max_goroutines"`
	MaxOpenFiles     int           `json:"max_open_files"`
	MaxNetworkConns  int           `json:"max_network_conns"`
	ExecutionTimeout time.Duration `json:"execution_timeout"`
	IdleTimeout      time.Duration `json:"idle_timeout"`
}

// ==================== 增强的插件信息 ====================

// EnhancedPluginInfo 增强的插件信息
type EnhancedPluginInfo struct {
	// 基础信息
	Name        string         `json:"name"`
	Version     string         `json:"version"`
	Type        PluginType     `json:"type"`
	Priority    PluginPriority `json:"priority"`
	Description string         `json:"description"`
	Author      string         `json:"author"`
	Homepage    string         `json:"homepage"`
	License     string         `json:"license"`
	Tags        []string       `json:"tags"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`

	// 兼容性信息
	APIVersion       string   `json:"api_version"`
	MinSystemVersion string   `json:"min_system_version"`
	SupportedOS      []string `json:"supported_os"`
	SupportedArch    []string `json:"supported_arch"`

	// 依赖和能力
	Dependencies []PluginDependency `json:"dependencies"`
	Provides     []PluginCapability `json:"provides"`
	Requires     []PluginCapability `json:"requires"`

	// 安全配置
	Permissions PluginPermission `json:"permissions"`
	Signature   string           `json:"signature"`
	Checksum    string           `json:"checksum"`

	// 扩展信息
	Category      string   `json:"category"`
	Keywords      []string `json:"keywords"`
	Screenshots   []string `json:"screenshots"`
	Documentation string   `json:"documentation"`
	ChangeLog     string   `json:"changelog"`

	// 运行时信息
	Config         map[string]any `json:"config"`
	ConfigSchema   *ConfigSchema  `json:"config_schema"`
	HealthCheckURL string         `json:"health_check_url"`
	MetricsURL     string         `json:"metrics_url"`

	// 市场信息（可选）
	Downloads   int64   `json:"downloads,omitempty"`
	Rating      float64 `json:"rating,omitempty"`
	ReviewCount int64   `json:"review_count,omitempty"`

	// 能力声明（与provides相同，为了向后兼容）
	Capabilities []PluginCapability `json:"capabilities,omitempty"`
}

// PluginInfo 插件信息类型（别名，向后兼容）
type PluginInfo = EnhancedPluginInfo

// ConfigSchema 配置模式
type ConfigSchema struct {
	Schema   map[string]any   `json:"schema"`    // JSON Schema
	UISchema map[string]any   `json:"ui_schema"` // UI Schema for forms
	Examples []map[string]any `json:"examples"`
	Defaults map[string]any   `json:"defaults"`
	Required []string         `json:"required"`
}

// ==================== 核心插件接口 ====================

// Plugin 统一的插件接口（原 EnhancedPlugin）
type Plugin interface {
	// 基础信息
	GetInfo() *EnhancedPluginInfo
	GetStatus() PluginStatus
	GetMetrics(ctx context.Context) (map[string]any, error)

	// 生命周期管理
	Initialize(ctx context.Context, config map[string]any) error
	Start(ctx context.Context) error
	Stop(ctx context.Context) error
	Reload(ctx context.Context, config map[string]any) error
	Shutdown(ctx context.Context) error

	// 健康检查和监控
	Health(ctx context.Context) error
	Ping(ctx context.Context) error

	// 配置管理
	ValidateConfig(config map[string]any) error
	GetConfigSchema() *ConfigSchema
	UpdateConfig(ctx context.Context, config map[string]any) error

	// 事件处理
	HandleEvent(ctx context.Context, event *PluginEvent) error
}

// PluginEventHandler 插件事件处理器
type PluginEventHandler interface {
	OnPluginLoaded(ctx context.Context, plugin Plugin) error
	OnPluginStarted(ctx context.Context, plugin Plugin) error
	OnPluginStopped(ctx context.Context, plugin Plugin) error
	OnPluginError(ctx context.Context, plugin Plugin, err error) error
	OnConfigUpdated(ctx context.Context, plugin Plugin, config map[string]any) error
}

// PluginEvent 插件事件
type PluginEvent struct {
	Type      string         `json:"type"`
	Source    string         `json:"source"`
	Target    string         `json:"target"`
	Data      map[string]any `json:"data"`
	Timestamp time.Time      `json:"timestamp"`
	Priority  PluginPriority `json:"priority"`
}

// ==================== 专用插件接口 ====================

// Collector 采集器接口
type Collector interface {
	Plugin

	// 设备发现和管理
	DiscoverDevices(ctx context.Context, criteria *DiscoveryCriteria) ([]*DeviceInfo, error)
	GetSupportedDeviceTypes() []string
	GetSupportedMetrics(deviceType string) ([]*pb.SupportedMetric, error)

	// 采集任务管理
	CreateCollectionTask(ctx context.Context, config *pb.CollectorTaskConfig) (*CollectionTask, error)
	UpdateCollectionTask(ctx context.Context, taskID string, config *pb.CollectorTaskConfig) error
	DeleteCollectionTask(ctx context.Context, taskID string) error
	ListCollectionTasks(ctx context.Context) ([]*CollectionTask, error)

	// 数据采集
	CollectMetrics(ctx context.Context, taskConfig *pb.CollectorTaskConfig) ([]*pb.MetricData, error)
	StartContinuousCollection(ctx context.Context, taskID string) error
	StopContinuousCollection(ctx context.Context, taskID string) error

	// 高级功能
	BatchCollect(ctx context.Context, tasks []*pb.CollectorTaskConfig) (map[string][]*pb.MetricData, error)
	PreviewCollection(ctx context.Context, config *pb.CollectorTaskConfig) (*CollectionPreview, error)
	GetCollectionHistory(ctx context.Context, taskID string, timeRange *TimeRange) ([]*pb.MetricData, error)

	// 缓存和优化
	EnableCaching(enabled bool, ttl time.Duration) error
	InvalidateCache(ctx context.Context, keys []string) error
}

// Processor 处理器接口
type Processor interface {
	Plugin

	// 数据处理
	ProcessMetrics(ctx context.Context, metrics []*pb.MetricData) ([]*pb.MetricData, error)
	ProcessLogs(ctx context.Context, logs []*pb.LogEntry) ([]*pb.LogEntry, error)
	ProcessEvents(ctx context.Context, events []*PluginEvent) ([]*PluginEvent, error)

	// 批处理
	ProcessBatch(ctx context.Context, batch *ProcessingBatch) (*ProcessingResult, error)

	// 流处理
	CreateStream(ctx context.Context, config *StreamConfig) (*ProcessingStream, error)
	ProcessStream(ctx context.Context, stream *ProcessingStream, data any) error

	// 处理器链
	SetNextProcessor(processor Processor) error
	GetProcessingPipeline() []string

	// 配置和规则
	GetProcessingConfig() map[string]any
	AddProcessingRule(rule *ProcessingRule) error
	RemoveProcessingRule(ruleID string) error
	ListProcessingRules() ([]*ProcessingRule, error)
}

// Alerter 告警器接口
type Alerter interface {
	Plugin

	// 告警发送
	SendAlert(ctx context.Context, alert *AlertEvent) error
	SendBulkAlerts(ctx context.Context, alerts []*AlertEvent) error

	// 告警通道管理
	GetAlertChannels() []string
	CreateAlertChannel(channel *AlertChannel) error
	UpdateAlertChannel(channelID string, channel *AlertChannel) error
	DeleteAlertChannel(channelID string) error
	TestAlertChannel(channelID string) error

	// 告警规则
	CreateAlertRule(rule *AlertRule) error
	UpdateAlertRule(ruleID string, rule *AlertRule) error
	DeleteAlertRule(ruleID string) error
	ListAlertRules() ([]*AlertRule, error)
	EvaluateRules(ctx context.Context, data any) ([]*AlertEvent, error)

	// 告警历史和统计
	GetAlertHistory(ctx context.Context, filters *AlertFilters) ([]*AlertEvent, error)
	GetAlertStatistics(ctx context.Context, timeRange *TimeRange) (*AlertStatistics, error)

	// 高级功能
	ValidateAlertConfig(config map[string]any) error
	GetDeliveryStatus(alertID string) (*DeliveryStatus, error)
	SetAlertThrottling(rules *ThrottlingRules) error
	AcknowledgeAlert(ctx context.Context, alertID, userID string) error
}

// Analyzer 分析器接口
type Analyzer interface {
	Plugin

	// 数据分析
	AnalyzeMetrics(ctx context.Context, metrics []*pb.MetricData) (*AnalysisResult, error)
	AnalyzeLogs(ctx context.Context, logs []*pb.LogEntry) (*AnalysisResult, error)
	AnalyzeEvents(ctx context.Context, events []*PluginEvent) (*AnalysisResult, error)

	// 异常检测
	DetectAnomalies(ctx context.Context, data any) ([]*Anomaly, error)
	TrainAnomalyModel(ctx context.Context, trainingData any) error
	GetAnomalyModel() (*AnomalyModel, error)

	// 趋势分析
	PredictTrend(ctx context.Context, metricKey string, deviceID string, timeHorizon time.Duration) (*TrendPrediction, error)
	AnalyzeTrends(ctx context.Context, data any, timeWindow time.Duration) ([]*TrendAnalysis, error)

	// 根因分析
	AnalyzeRootCause(ctx context.Context, incident *Incident) (*RootCauseAnalysis, error)
	GetCorrelations(ctx context.Context, data any) ([]*Correlation, error)

	// 预测和建议
	PredictCapacity(ctx context.Context, resource string, timeHorizon time.Duration) (*CapacityPrediction, error)
	GenerateRecommendations(ctx context.Context, analysis *AnalysisResult) ([]*Recommendation, error)

	// 模型管理
	ListModels() ([]*AnalysisModel, error)
	LoadModel(modelID string) error
	SaveModel(model *AnalysisModel) error
	DeleteModel(modelID string) error
}

// ==================== 辅助数据结构 ====================

// DeviceInfo 设备信息
type DeviceInfo struct {
	ID           string            `json:"id"`
	Name         string            `json:"name"`
	Type         string            `json:"type"`
	Address      string            `json:"address"`
	Status       string            `json:"status"`
	Capabilities []string          `json:"capabilities"`
	Metadata     map[string]string `json:"metadata"`
	LastSeen     time.Time         `json:"last_seen"`
}

// DiscoveryCriteria 发现条件
type DiscoveryCriteria struct {
	DeviceTypes []string          `json:"device_types"`
	Networks    []string          `json:"networks"`
	Filters     map[string]string `json:"filters"`
	Timeout     time.Duration     `json:"timeout"`
}

// CollectionTask 采集任务
type CollectionTask struct {
	ID         string                  `json:"id"`
	Name       string                  `json:"name"`
	Config     *pb.CollectorTaskConfig `json:"config"`
	Status     string                  `json:"status"`
	CreatedAt  time.Time               `json:"created_at"`
	UpdatedAt  time.Time               `json:"updated_at"`
	LastRun    *time.Time              `json:"last_run,omitempty"`
	NextRun    *time.Time              `json:"next_run,omitempty"`
	Statistics *CollectionStatistics   `json:"statistics"`
}

// CollectionStatistics 采集统计
type CollectionStatistics struct {
	TotalRuns        int64         `json:"total_runs"`
	SuccessfulRuns   int64         `json:"successful_runs"`
	FailedRuns       int64         `json:"failed_runs"`
	AverageRunTime   time.Duration `json:"average_run_time"`
	LastRunTime      time.Duration `json:"last_run_time"`
	MetricsCollected int64         `json:"metrics_collected"`
	LastError        string        `json:"last_error,omitempty"`
}

// TimeRange 时间范围
type TimeRange struct {
	Start time.Time `json:"start"`
	End   time.Time `json:"end"`
}

// CollectionPreview 采集预览
type CollectionPreview struct {
	EstimatedMetrics int              `json:"estimated_metrics"`
	SampleData       []*pb.MetricData `json:"sample_data"`
	Warnings         []string         `json:"warnings"`
	Recommendations  []string         `json:"recommendations"`
}

// AnalysisResult 分析结果（增强版）
type AnalysisResult struct {
	ID         string  `json:"id"`
	Status     string  `json:"status"`
	Message    string  `json:"message"`
	Confidence float64 `json:"confidence"`
	Severity   string  `json:"severity"`

	// 分析结果
	Anomalies       []*Anomaly        `json:"anomalies,omitempty"`
	Trends          []*TrendAnalysis  `json:"trends,omitempty"`
	Correlations    []*Correlation    `json:"correlations,omitempty"`
	Recommendations []*Recommendation `json:"recommendations,omitempty"`

	// 元数据
	AnalysisType   string         `json:"analysis_type"`
	DataSources    []string       `json:"data_sources"`
	Timestamp      time.Time      `json:"timestamp"`
	ProcessingTime time.Duration  `json:"processing_time"`
	Metadata       map[string]any `json:"metadata"`
}

// Anomaly 异常（增强版）
type Anomaly struct {
	ID          string `json:"id"`
	DeviceID    string `json:"device_id"`
	DeviceName  string `json:"device_name"`
	MetricKey   string `json:"metric_key"`
	AnomalyType string `json:"anomaly_type"` // "spike", "drop", "trend", "pattern"

	// 值信息
	ActualValue    float64 `json:"actual_value"`
	ExpectedValue  float64 `json:"expected_value"`
	DeviationScore float64 `json:"deviation_score"`
	Confidence     float64 `json:"confidence"`

	// 分类和影响
	Severity string         `json:"severity"`
	Category string         `json:"category"`
	Impact   string         `json:"impact"`
	Priority PluginPriority `json:"priority"`

	// 上下文
	Description   string         `json:"description"`
	Context       map[string]any `json:"context"`
	RelatedEvents []string       `json:"related_events"`

	// 时间信息
	DetectedAt time.Time      `json:"detected_at"`
	StartTime  *time.Time     `json:"start_time,omitempty"`
	EndTime    *time.Time     `json:"end_time,omitempty"`
	Duration   *time.Duration `json:"duration,omitempty"`

	// 处理状态
	Status     string     `json:"status"` // "new", "acknowledged", "investigating", "resolved"
	AssignedTo string     `json:"assigned_to,omitempty"`
	Resolution string     `json:"resolution,omitempty"`
	ResolvedAt *time.Time `json:"resolved_at,omitempty"`
}

// ==================== 工厂和管理接口 ====================

// PluginFactory 插件工厂接口
type PluginFactory interface {
	// 基础功能
	CreatePlugin(pluginType PluginType, config map[string]any) (Plugin, error)
	GetSupportedTypes() []PluginType

	// 插件信息
	GetPluginInfo() *EnhancedPluginInfo
	ValidateConfig(pluginType PluginType, config map[string]any) error
	GetConfigSchema(pluginType PluginType) *ConfigSchema

	// 依赖管理
	GetDependencies() []PluginDependency
	CheckCompatibility(systemVersion string) error

	// 生命周期
	Initialize(ctx context.Context) error
	Shutdown(ctx context.Context) error
}

// PluginManager 插件管理器接口
type PluginManager interface {
	// 插件注册和发现
	RegisterPlugin(info *EnhancedPluginInfo, factory PluginFactory) error
	UnregisterPlugin(name, version string) error
	DiscoverPlugins(searchPaths []string) error

	// 插件生命周期
	LoadPlugin(name, version string, config map[string]any) error
	StartPlugin(name, version string) error
	StopPlugin(name, version string) error
	ReloadPlugin(name, version string, config map[string]any) error
	UnloadPlugin(name, version string) error

	// 插件查询
	GetPlugin(name, version string) (Plugin, error)
	ListPlugins() []*EnhancedPluginInfo
	FindPlugins(criteria *SearchCriteria) []*EnhancedPluginInfo
	GetPluginsByType(pluginType PluginType) []Plugin

	// 依赖管理
	ResolveDependencies(pluginName, version string) error
	GetDependencyGraph() (*DependencyGraph, error)

	// 配置管理
	UpdatePluginConfig(name, version string, config map[string]any) error
	GetPluginConfig(name, version string) (map[string]any, error)
	ValidatePluginConfig(name, version string, config map[string]any) error

	// 监控和健康检查
	CheckHealth(name, version string) error
	GetPluginMetrics(name, version string) (map[string]any, error)
	GetPluginStatus(name, version string) PluginStatus

	// 事件处理
	AddEventHandler(handler PluginEventHandler) error
	RemoveEventHandler(handler PluginEventHandler) error
	PublishEvent(event *PluginEvent) error

	// 热更新
	UpdatePlugin(name, currentVersion, targetVersion string) error
	RollbackPlugin(name, version string) error

	// 安全和权限
	SetPluginPermissions(name, version string, permissions *PluginPermission) error
	ValidatePluginPermissions(name, version string) error

	// 资源管理
	SetResourceLimits(name, version string, limits *PluginResourceLimits) error
	GetResourceUsage(name, version string) (*ResourceUsage, error)
}

// ==================== 缺失的数据类型定义 ====================

// ProcessingBatch 处理批次
type ProcessingBatch struct {
	ID        string         `json:"id"`
	Size      int            `json:"size"`
	Data      []any          `json:"data"`
	Metadata  map[string]any `json:"metadata"`
	CreatedAt time.Time      `json:"created_at"`
}

// ProcessingResult 处理结果
type ProcessingResult struct {
	BatchID       string         `json:"batch_id"`
	Success       bool           `json:"success"`
	ProcessedData []any          `json:"processed_data"`
	Errors        []string       `json:"errors"`
	Metrics       map[string]any `json:"metrics"`
	ProcessedAt   time.Time      `json:"processed_at"`
}

// StreamConfig 流配置
type StreamConfig struct {
	Name          string         `json:"name"`
	BufferSize    int            `json:"buffer_size"`
	MaxWorkers    int            `json:"max_workers"`
	FlushInterval time.Duration  `json:"flush_interval"`
	Config        map[string]any `json:"config"`
}

// ProcessingStream 处理流
type ProcessingStream struct {
	ID         string         `json:"id"`
	Config     *StreamConfig  `json:"config"`
	Status     string         `json:"status"`
	InputChan  chan any       `json:"-"`
	OutputChan chan any       `json:"-"`
	ErrorChan  chan error     `json:"-"`
	Metrics    map[string]any `json:"metrics"`
	CreatedAt  time.Time      `json:"created_at"`
}

// ProcessingRule 处理规则
type ProcessingRule struct {
	ID          string         `json:"id"`
	Name        string         `json:"name"`
	Description string         `json:"description"`
	Condition   string         `json:"condition"`
	Action      string         `json:"action"`
	Config      map[string]any `json:"config"`
	Enabled     bool           `json:"enabled"`
	Priority    int            `json:"priority"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
}

// AlertEvent 告警事件
type AlertEvent struct {
	ID             string            `json:"id"`
	Title          string            `json:"title"`
	Description    string            `json:"description"`
	Level          string            `json:"level"` // "info", "warning", "error", "critical"
	DeviceID       string            `json:"device_id"`
	DeviceName     string            `json:"device_name"`
	MetricKey      string            `json:"metric_key"`
	CurrentValue   float64           `json:"current_value"`
	ThresholdValue float64           `json:"threshold_value"`
	Labels         map[string]string `json:"labels"`
	Annotations    map[string]string `json:"annotations"`
	Source         string            `json:"source"`
	GroupKey       string            `json:"group_key"`
	Status         string            `json:"status"` // "firing", "resolved"
	StartsAt       time.Time         `json:"starts_at"`
	EndsAt         *time.Time        `json:"ends_at,omitempty"`
	GeneratorURL   string            `json:"generator_url,omitempty"`
	Fingerprint    string            `json:"fingerprint"`
	Context        map[string]any    `json:"context"`
	Timestamp      time.Time         `json:"timestamp"`
}

// AlertChannel 告警通道
type AlertChannel struct {
	ID          string         `json:"id"`
	Name        string         `json:"name"`
	Type        string         `json:"type"` // "email", "webhook", "slack", "dingtalk"
	Config      map[string]any `json:"config"`
	Enabled     bool           `json:"enabled"`
	Description string         `json:"description"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
}

// AlertRule 告警规则
type AlertRule struct {
	ID          string            `json:"id"`
	Name        string            `json:"name"`
	Description string            `json:"description"`
	Query       string            `json:"query"`
	Condition   string            `json:"condition"`
	Threshold   float64           `json:"threshold"`
	Duration    time.Duration     `json:"duration"`
	Severity    string            `json:"severity"`
	Labels      map[string]string `json:"labels"`
	Annotations map[string]string `json:"annotations"`
	Channels    []string          `json:"channels"`
	Enabled     bool              `json:"enabled"`
	Config      map[string]any    `json:"config"`
	CreatedAt   time.Time         `json:"created_at"`
	UpdatedAt   time.Time         `json:"updated_at"`
}

// AlertFilters 告警过滤器
type AlertFilters struct {
	StartTime  *time.Time        `json:"start_time,omitempty"`
	EndTime    *time.Time        `json:"end_time,omitempty"`
	Levels     []string          `json:"levels,omitempty"`
	DeviceIDs  []string          `json:"device_ids,omitempty"`
	MetricKeys []string          `json:"metric_keys,omitempty"`
	Status     []string          `json:"status,omitempty"`
	Labels     map[string]string `json:"labels,omitempty"`
	Limit      int               `json:"limit,omitempty"`
	Offset     int               `json:"offset,omitempty"`
}

// AlertStatistics 告警统计
type AlertStatistics struct {
	Total       int64             `json:"total"`
	ByLevel     map[string]int64  `json:"by_level"`
	ByDevice    map[string]int64  `json:"by_device"`
	ByMetric    map[string]int64  `json:"by_metric"`
	ByStatus    map[string]int64  `json:"by_status"`
	TrendData   []TimeSeriesPoint `json:"trend_data"`
	TimeRange   *TimeRange        `json:"time_range"`
	GeneratedAt time.Time         `json:"generated_at"`
}

// TimeSeriesPoint 时间序列数据点
type TimeSeriesPoint struct {
	Timestamp time.Time `json:"timestamp"`
	Value     float64   `json:"value"`
}

// DeliveryStatus 投递状态
type DeliveryStatus struct {
	AlertID      string     `json:"alert_id"`
	Channel      string     `json:"channel"`
	Status       string     `json:"status"` // "pending", "sent", "failed", "delivered"
	Message      string     `json:"message"`
	AttemptCount int        `json:"attempt_count"`
	LastAttempt  time.Time  `json:"last_attempt"`
	NextRetry    *time.Time `json:"next_retry,omitempty"`
}

// ThrottlingRules 节流规则
type ThrottlingRules struct {
	MaxAlertsPerMinute int           `json:"max_alerts_per_minute"`
	MaxAlertsPerHour   int           `json:"max_alerts_per_hour"`
	WindowSize         time.Duration `json:"window_size"`
	GroupBy            []string      `json:"group_by"`
	Enabled            bool          `json:"enabled"`
}

// TrendAnalysis 趋势分析
type TrendAnalysis struct {
	MetricKey       string    `json:"metric_key"`
	DeviceID        string    `json:"device_id"`
	Direction       string    `json:"direction"` // "increasing", "decreasing", "stable", "volatile"
	Slope           float64   `json:"slope"`
	RSquared        float64   `json:"r_squared"`
	Confidence      float64   `json:"confidence"`
	StartValue      float64   `json:"start_value"`
	EndValue        float64   `json:"end_value"`
	ChangePercent   float64   `json:"change_percent"`
	SeasonalPattern bool      `json:"seasonal_pattern"`
	Timestamp       time.Time `json:"timestamp"`
}

// Correlation 相关性分析
type Correlation struct {
	Metric1      string  `json:"metric1"`
	Metric2      string  `json:"metric2"`
	Device1      string  `json:"device1"`
	Device2      string  `json:"device2"`
	Coefficient  float64 `json:"coefficient"`
	Significance float64 `json:"significance"`
	Strength     string  `json:"strength"` // "weak", "moderate", "strong"
	Type         string  `json:"type"`     // "positive", "negative"
	Confidence   float64 `json:"confidence"`
	Description  string  `json:"description"`
}

// Recommendation 建议
type Recommendation struct {
	ID          string         `json:"id"`
	Type        string         `json:"type"`
	Priority    PluginPriority `json:"priority"`
	Title       string         `json:"title"`
	Description string         `json:"description"`
	Action      string         `json:"action"`
	Impact      string         `json:"impact"`
	Effort      string         `json:"effort"` // "low", "medium", "high"
	Category    string         `json:"category"`
	Context     map[string]any `json:"context"`
	Tags        []string       `json:"tags"`
	CreatedAt   time.Time      `json:"created_at"`
	ExpiresAt   *time.Time     `json:"expires_at,omitempty"`
}

// AnomalyModel 异常检测模型
type AnomalyModel struct {
	ID           string         `json:"id"`
	Name         string         `json:"name"`
	Type         string         `json:"type"` // "statistical", "ml", "dl"
	Version      string         `json:"version"`
	Description  string         `json:"description"`
	Parameters   map[string]any `json:"parameters"`
	TrainingData any            `json:"training_data"`
	Accuracy     float64        `json:"accuracy"`
	Precision    float64        `json:"precision"`
	Recall       float64        `json:"recall"`
	F1Score      float64        `json:"f1_score"`
	TrainedAt    time.Time      `json:"trained_at"`
	LastUsed     *time.Time     `json:"last_used,omitempty"`
	UsageCount   int64          `json:"usage_count"`
	Status       string         `json:"status"` // "training", "ready", "deprecated"
}

// TrendPrediction 趋势预测
type TrendPrediction struct {
	MetricKey       string        `json:"metric_key"`
	DeviceID        string        `json:"device_id"`
	Direction       string        `json:"direction"` // "increasing", "decreasing", "stable", "unknown"
	Confidence      float64       `json:"confidence"`
	PredictedValue  float64       `json:"predicted_value"`
	PredictedValues []float64     `json:"predicted_values"` // 多个时间点的预测值
	TimeHorizon     time.Duration `json:"time_horizon"`
	Model           string        `json:"model"`
	Accuracy        float64       `json:"accuracy"`
	Timestamp       time.Time     `json:"timestamp"`
}

// Incident 事件
type Incident struct {
	ID              string          `json:"id"`
	Title           string          `json:"title"`
	Description     string          `json:"description"`
	Severity        string          `json:"severity"`
	Status          string          `json:"status"` // "open", "investigating", "resolved"
	AffectedSystems []string        `json:"affected_systems"`
	Symptoms        []string        `json:"symptoms"`
	Timeline        []IncidentEvent `json:"timeline"`
	Context         map[string]any  `json:"context"`
	CreatedAt       time.Time       `json:"created_at"`
	UpdatedAt       time.Time       `json:"updated_at"`
	ResolvedAt      *time.Time      `json:"resolved_at,omitempty"`
}

// IncidentEvent 事件中的事件
type IncidentEvent struct {
	Timestamp   time.Time `json:"timestamp"`
	Type        string    `json:"type"`
	Description string    `json:"description"`
	Actor       string    `json:"actor"`
}

// RootCauseAnalysis 根因分析
type RootCauseAnalysis struct {
	IncidentID      string           `json:"incident_id"`
	ProbableCauses  []ProbableCause  `json:"probable_causes"`
	AnalysisMethod  string           `json:"analysis_method"`
	Confidence      float64          `json:"confidence"`
	Evidence        []Evidence       `json:"evidence"`
	Timeline        []AnalysisStep   `json:"timeline"`
	Recommendations []Recommendation `json:"recommendations"`
	Context         map[string]any   `json:"context"`
	AnalyzedAt      time.Time        `json:"analyzed_at"`
}

// ProbableCause 可能原因
type ProbableCause struct {
	Description string   `json:"description"`
	Probability float64  `json:"probability"`
	Category    string   `json:"category"`
	Evidence    []string `json:"evidence"`
}

// Evidence 证据
type Evidence struct {
	Type        string         `json:"type"`
	Description string         `json:"description"`
	Source      string         `json:"source"`
	Relevance   float64        `json:"relevance"`
	Data        map[string]any `json:"data"`
	Timestamp   time.Time      `json:"timestamp"`
}

// AnalysisStep 分析步骤
type AnalysisStep struct {
	Step        string    `json:"step"`
	Description string    `json:"description"`
	Result      string    `json:"result"`
	Timestamp   time.Time `json:"timestamp"`
}

// CapacityPrediction 容量预测
type CapacityPrediction struct {
	Resource        string         `json:"resource"`
	CurrentUsage    float64        `json:"current_usage"`
	PredictedUsage  float64        `json:"predicted_usage"`
	Capacity        float64        `json:"capacity"`
	UtilizationRate float64        `json:"utilization_rate"`
	TimeToFull      *time.Duration `json:"time_to_full,omitempty"`
	Confidence      float64        `json:"confidence"`
	Recommendation  string         `json:"recommendation"`
	Timestamp       time.Time      `json:"timestamp"`
}

// AnalysisModel 分析模型
type AnalysisModel struct {
	ID          string         `json:"id"`
	Name        string         `json:"name"`
	Type        string         `json:"type"`
	Version     string         `json:"version"`
	Description string         `json:"description"`
	Config      map[string]any `json:"config"`
	Status      string         `json:"status"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
}

// SearchCriteria 搜索条件
type SearchCriteria struct {
	Types      []PluginType `json:"types,omitempty"`
	Keywords   []string     `json:"keywords,omitempty"`
	Categories []string     `json:"categories,omitempty"`
	Authors    []string     `json:"authors,omitempty"`
	Tags       []string     `json:"tags,omitempty"`
	MinVersion string       `json:"min_version,omitempty"`
	MaxVersion string       `json:"max_version,omitempty"`
	Enabled    *bool        `json:"enabled,omitempty"`
	Limit      int          `json:"limit,omitempty"`
	Offset     int          `json:"offset,omitempty"`
}

// DependencyGraph 依赖图
type DependencyGraph struct {
	Nodes []DependencyNode `json:"nodes"`
	Edges []DependencyEdge `json:"edges"`
}

// DependencyNode 依赖节点
type DependencyNode struct {
	ID      string     `json:"id"`
	Name    string     `json:"name"`
	Version string     `json:"version"`
	Type    PluginType `json:"type"`
}

// DependencyEdge 依赖边
type DependencyEdge struct {
	From     string `json:"from"`
	To       string `json:"to"`
	Type     string `json:"type"` // "depends", "conflicts"
	Required bool   `json:"required"`
}

// ResourceUsage 资源使用情况
type ResourceUsage struct {
	CPUPercent     float64   `json:"cpu_percent"`
	MemoryMB       int64     `json:"memory_mb"`
	DiskMB         int64     `json:"disk_mb"`
	GoroutineCount int       `json:"goroutine_count"`
	OpenFiles      int       `json:"open_files"`
	NetworkConns   int       `json:"network_conns"`
	LastUpdated    time.Time `json:"last_updated"`
}
