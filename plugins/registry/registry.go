package registry

import (
	"context"
	"fmt"
	"sync"

	plugininterface "aiops/plugins/interface"
)

// PluginRegistry 插件注册中心
type PluginRegistry struct {
	mu      sync.RWMutex
	plugins map[string]*RegisteredPlugin
}

// RegisteredPlugin 注册的插件
type RegisteredPlugin struct {
	Info     *plugininterface.PluginInfo
	Factory  plugininterface.PluginFactory
	Instance plugininterface.Plugin
	Status   PluginStatus
}

// PluginStatus 插件状态
type PluginStatus string

const (
	StatusRegistered PluginStatus = "registered"
	StatusLoaded     PluginStatus = "loaded"
	StatusStarted    PluginStatus = "started"
	StatusStopped    PluginStatus = "stopped"
	StatusError      PluginStatus = "error"
)

// NewPluginRegistry 创建新的插件注册中心
func NewPluginRegistry() *PluginRegistry {
	return &PluginRegistry{
		plugins: make(map[string]*RegisteredPlugin),
	}
}

// Register 注册插件
func (r *PluginRegistry) Register(info *plugininterface.PluginInfo, factory plugininterface.PluginFactory) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	pluginKey := fmt.Sprintf("%s:%s", info.Name, info.Version)

	if _, exists := r.plugins[pluginKey]; exists {
		return fmt.Errorf("plugin %s already registered", pluginKey)
	}

	r.plugins[pluginKey] = &RegisteredPlugin{
		Info:    info,
		Factory: factory,
		Status:  StatusRegistered,
	}

	return nil
}

// Unregister 注销插件
func (r *PluginRegistry) Unregister(name, version string) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	pluginKey := fmt.Sprintf("%s:%s", name, version)

	plugin, exists := r.plugins[pluginKey]
	if !exists {
		return fmt.Errorf("plugin %s not found", pluginKey)
	}

	// 如果插件正在运行，先停止它
	if plugin.Instance != nil && plugin.Status == StatusStarted {
		if err := plugin.Instance.Stop(context.Background()); err != nil {
			return fmt.Errorf("failed to stop plugin %s: %w", pluginKey, err)
		}
	}

	delete(r.plugins, pluginKey)
	return nil
}

// Load 加载插件
func (r *PluginRegistry) Load(name, version string, config map[string]interface{}) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	pluginKey := fmt.Sprintf("%s:%s", name, version)

	registered, exists := r.plugins[pluginKey]
	if !exists {
		return fmt.Errorf("plugin %s not registered", pluginKey)
	}

	// 创建插件实例
	instance, err := registered.Factory.CreatePlugin(registered.Info.Type, config)
	if err != nil {
		registered.Status = StatusError
		return fmt.Errorf("failed to create plugin instance: %w", err)
	}

	// 初始化插件
	if err := instance.Initialize(context.Background(), config); err != nil {
		registered.Status = StatusError
		return fmt.Errorf("failed to initialize plugin: %w", err)
	}

	registered.Instance = instance
	registered.Status = StatusLoaded
	return nil
}

// Start 启动插件
func (r *PluginRegistry) Start(name, version string) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	pluginKey := fmt.Sprintf("%s:%s", name, version)

	registered, exists := r.plugins[pluginKey]
	if !exists {
		return fmt.Errorf("plugin %s not registered", pluginKey)
	}

	if registered.Instance == nil {
		return fmt.Errorf("plugin %s not loaded", pluginKey)
	}

	if registered.Status == StatusStarted {
		return fmt.Errorf("plugin %s already started", pluginKey)
	}

	if err := registered.Instance.Start(context.Background()); err != nil {
		registered.Status = StatusError
		return fmt.Errorf("failed to start plugin: %w", err)
	}

	registered.Status = StatusStarted
	return nil
}

// Stop 停止插件
func (r *PluginRegistry) Stop(name, version string) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	pluginKey := fmt.Sprintf("%s:%s", name, version)

	registered, exists := r.plugins[pluginKey]
	if !exists {
		return fmt.Errorf("plugin %s not registered", pluginKey)
	}

	if registered.Instance == nil {
		return fmt.Errorf("plugin %s not loaded", pluginKey)
	}

	if registered.Status != StatusStarted {
		return fmt.Errorf("plugin %s not started", pluginKey)
	}

	if err := registered.Instance.Stop(context.Background()); err != nil {
		registered.Status = StatusError
		return fmt.Errorf("failed to stop plugin: %w", err)
	}

	registered.Status = StatusStopped
	return nil
}

// Get 获取插件实例
func (r *PluginRegistry) Get(name, version string) (plugininterface.Plugin, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	pluginKey := fmt.Sprintf("%s:%s", name, version)

	registered, exists := r.plugins[pluginKey]
	if !exists {
		return nil, fmt.Errorf("plugin %s not registered", pluginKey)
	}

	if registered.Instance == nil {
		return nil, fmt.Errorf("plugin %s not loaded", pluginKey)
	}

	return registered.Instance, nil
}

// GetByType 根据类型获取插件
func (r *PluginRegistry) GetByType(pluginType plugininterface.PluginType) []plugininterface.Plugin {
	r.mu.RLock()
	defer r.mu.RUnlock()

	var plugins []plugininterface.Plugin
	for _, registered := range r.plugins {
		if registered.Info.Type == pluginType && registered.Instance != nil && registered.Status == StatusStarted {
			plugins = append(plugins, registered.Instance)
		}
	}

	return plugins
}

// List 列出所有插件
func (r *PluginRegistry) List() []*RegisteredPlugin {
	r.mu.RLock()
	defer r.mu.RUnlock()

	plugins := make([]*RegisteredPlugin, 0, len(r.plugins))
	for _, plugin := range r.plugins {
		plugins = append(plugins, plugin)
	}

	return plugins
}

// ListByStatus 根据状态列出插件
func (r *PluginRegistry) ListByStatus(status PluginStatus) []*RegisteredPlugin {
	r.mu.RLock()
	defer r.mu.RUnlock()

	var plugins []*RegisteredPlugin
	for _, plugin := range r.plugins {
		if plugin.Status == status {
			plugins = append(plugins, plugin)
		}
	}

	return plugins
}

// Health 检查插件健康状态
func (r *PluginRegistry) Health(name, version string) error {
	r.mu.RLock()
	defer r.mu.RUnlock()

	pluginKey := fmt.Sprintf("%s:%s", name, version)

	registered, exists := r.plugins[pluginKey]
	if !exists {
		return fmt.Errorf("plugin %s not registered", pluginKey)
	}

	if registered.Instance == nil {
		return fmt.Errorf("plugin %s not loaded", pluginKey)
	}

	if registered.Status != StatusStarted {
		return fmt.Errorf("plugin %s not started", pluginKey)
	}

	return registered.Instance.Health(context.Background())
}

// GetMetrics 获取插件指标
func (r *PluginRegistry) GetMetrics(name, version string) (map[string]interface{}, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	pluginKey := fmt.Sprintf("%s:%s", name, version)

	registered, exists := r.plugins[pluginKey]
	if !exists {
		return nil, fmt.Errorf("plugin %s not registered", pluginKey)
	}

	if registered.Instance == nil {
		return nil, fmt.Errorf("plugin %s not loaded", pluginKey)
	}

	// 所有插件现在都实现了统一的 Plugin 接口
	return registered.Instance.GetMetrics(context.Background())
}
