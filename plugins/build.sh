#!/bin/bash

# 插件构建脚本
# 用于将 Go 插件编译为动态库 (.so 文件)
# 自动扫描 examples 目录并根据版本添加版本后缀

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BUILD_DIR="$SCRIPT_DIR/build"
EXAMPLES_DIR="$SCRIPT_DIR/examples"

# 默认版本，可以通过环境变量覆盖
PLUGIN_VERSION="${PLUGIN_VERSION:-1.0.0}"

# 创建构建目录
mkdir -p "$BUILD_DIR"

echo "开始构建插件..."
echo "插件版本: $PLUGIN_VERSION"
echo ""

# 获取当前时间戳用于增量构建检查
current_time=$(date +%s)

# 自动扫描 examples 目录下的所有插件项目
build_plugin() {
    local plugin_dir="$1"
    local plugin_name=$(basename "$plugin_dir")
    local main_file="$plugin_dir/main.go"
    local output_file="$BUILD_DIR/${plugin_name}-${PLUGIN_VERSION}.so"

    # 检查是否存在 main.go 文件
    if [ ! -f "$main_file" ]; then
        echo "⚠️  跳过 $plugin_name: 未找到 main.go 文件"
        return
    fi

    # 检查是否需要重新构建（增量构建）
    if [ -f "$output_file" ]; then
        # 检查源文件是否比输出文件新
        if [ "$main_file" -ot "$output_file" ]; then
            echo "⏭️  跳过 $plugin_name: 无需重新构建"
            return
        fi
    fi

    echo "🔨 构建 $plugin_name 插件..."
    cd "$plugin_dir"

    # 构建插件，添加版本信息到 ldflags
    if go build -buildmode=plugin \
        -ldflags="-s -w -X main.Version=$PLUGIN_VERSION" \
        -o "$output_file" main.go; then
        echo "✅ ${plugin_name}-${PLUGIN_VERSION}.so 构建完成"

        # 创建不带版本的符号链接，方便开发和测试
        ln -sf "${plugin_name}-${PLUGIN_VERSION}.so" "$BUILD_DIR/${plugin_name}.so"
    else
        echo "❌ $plugin_name 构建失败"
        return 1
    fi
}

# 扫描并构建所有插件
plugin_count=0
success_count=0

if [ -d "$EXAMPLES_DIR" ]; then
    for plugin_dir in "$EXAMPLES_DIR"/*; do
        if [ -d "$plugin_dir" ]; then
            plugin_count=$((plugin_count + 1))
            if build_plugin "$plugin_dir"; then
                success_count=$((success_count + 1))
            fi
        fi
    done
else
    echo "❌ Examples 目录不存在: $EXAMPLES_DIR"
    exit 1
fi

echo ""
echo "📊 构建统计:"
echo "   发现插件: $plugin_count"
echo "   构建成功: $success_count"
echo "   构建失败: $((plugin_count - success_count))"
echo ""
echo "📁 插件文件位置: $BUILD_DIR"
echo ""

# 显示构建结果
if [ $success_count -gt 0 ]; then
    echo "🎉 构建完成的插件:"
    ls -la "$BUILD_DIR"/*.so 2>/dev/null || echo "   无插件文件"
else
    echo "⚠️  没有成功构建任何插件"
fi
