package main

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	pb "aiops/pkg/proto"
)

// MetricsCollector 指标采集器
type MetricsCollector struct {
	connManager *ConnectionManager
	config      *MySQLConfig
}

// NewMetricsCollector 创建指标采集器
func NewMetricsCollector(connManager *ConnectionManager, config *MySQLConfig) *MetricsCollector {
	return &MetricsCollector{
		connManager: connManager,
		config:      config,
	}
}

// CollectAllMetrics 采集所有指标
func (mc *MetricsCollector) CollectAllMetrics(ctx context.Context, deviceID string) ([]*pb.MetricData, error) {
	var allMetrics []*pb.MetricData
	timestamp := time.Now().Unix()

	connections := mc.connManager.GetAllConnections()
	for connName, pool := range connections {
		// 系统指标
		if mc.config.Collection.Metrics.System.Enabled {
			systemMetrics, err := mc.collectSystemMetrics(ctx, pool, deviceID, connName, timestamp)
			if err != nil {
				// 记录错误但继续采集其他指标
				continue
			}
			allMetrics = append(allMetrics, systemMetrics...)
		}

		// 性能指标
		if mc.config.Collection.Metrics.Performance.Enabled {
			perfMetrics, err := mc.collectPerformanceMetrics(ctx, pool, deviceID, connName, timestamp)
			if err != nil {
				continue
			}
			allMetrics = append(allMetrics, perfMetrics...)
		}

		// InnoDB指标
		if mc.config.Collection.Metrics.InnoDB.Enabled {
			innodbMetrics, err := mc.collectInnoDBMetrics(ctx, pool, deviceID, connName, timestamp)
			if err != nil {
				continue
			}
			allMetrics = append(allMetrics, innodbMetrics...)
		}

		// 复制指标
		if mc.config.Collection.Metrics.Replication.Enabled {
			replMetrics, err := mc.collectReplicationMetrics(ctx, pool, deviceID, connName, timestamp)
			if err != nil {
				continue
			}
			allMetrics = append(allMetrics, replMetrics...)
		}
	}

	return allMetrics, nil
}

// collectSystemMetrics 采集系统指标
func (mc *MetricsCollector) collectSystemMetrics(ctx context.Context, pool *ConnectionPool, deviceID, connName string, timestamp int64) ([]*pb.MetricData, error) {
	var metrics []*pb.MetricData

	labels := map[string]string{
		"connection": connName,
		"host":       pool.Config.Host,
		"port":       strconv.Itoa(pool.Config.Port),
	}

	// 连接数
	if mc.config.Collection.Metrics.System.Connections {
		connMetrics, err := mc.getConnectionMetrics(ctx, pool, deviceID, labels, timestamp)
		if err == nil {
			metrics = append(metrics, connMetrics...)
		}
	}

	// 运行时间
	if mc.config.Collection.Metrics.System.Uptime {
		uptimeMetric, err := mc.getUptimeMetric(ctx, pool, deviceID, labels, timestamp)
		if err == nil {
			metrics = append(metrics, uptimeMetric)
		}
	}

	// 版本信息
	if mc.config.Collection.Metrics.System.Version {
		versionMetric, err := mc.getVersionMetric(ctx, pool, deviceID, labels, timestamp)
		if err == nil {
			metrics = append(metrics, versionMetric)
		}
	}

	// 自定义系统变量
	for _, variable := range mc.config.Collection.Metrics.System.Variables {
		variableMetric, err := mc.getVariableMetric(ctx, pool, deviceID, labels, timestamp, variable)
		if err == nil {
			metrics = append(metrics, variableMetric)
		}
	}

	return metrics, nil
}

// collectPerformanceMetrics 采集性能指标
func (mc *MetricsCollector) collectPerformanceMetrics(ctx context.Context, pool *ConnectionPool, deviceID, connName string, timestamp int64) ([]*pb.MetricData, error) {
	var metrics []*pb.MetricData

	labels := map[string]string{
		"connection": connName,
		"host":       pool.Config.Host,
		"port":       strconv.Itoa(pool.Config.Port),
	}

	// QPS (每秒查询数)
	if mc.config.Collection.Metrics.Performance.QPS {
		qpsMetric, err := mc.getQPSMetric(ctx, pool, deviceID, labels, timestamp)
		if err == nil {
			metrics = append(metrics, qpsMetric)
		}
	}

	// TPS (每秒事务数)
	if mc.config.Collection.Metrics.Performance.TPS {
		tpsMetric, err := mc.getTPSMetric(ctx, pool, deviceID, labels, timestamp)
		if err == nil {
			metrics = append(metrics, tpsMetric)
		}
	}

	// 慢查询
	if mc.config.Collection.Metrics.Performance.SlowQueries {
		slowQueryMetrics, err := mc.getSlowQueryMetrics(ctx, pool, deviceID, labels, timestamp)
		if err == nil {
			metrics = append(metrics, slowQueryMetrics...)
		}
	}

	// 表锁
	if mc.config.Collection.Metrics.Performance.TableLocks {
		lockMetrics, err := mc.getTableLockMetrics(ctx, pool, deviceID, labels, timestamp)
		if err == nil {
			metrics = append(metrics, lockMetrics...)
		}
	}

	// 线程缓存
	if mc.config.Collection.Metrics.Performance.ThreadCache {
		threadMetrics, err := mc.getThreadCacheMetrics(ctx, pool, deviceID, labels, timestamp)
		if err == nil {
			metrics = append(metrics, threadMetrics...)
		}
	}

	return metrics, nil
}

// collectInnoDBMetrics 采集InnoDB指标
func (mc *MetricsCollector) collectInnoDBMetrics(ctx context.Context, pool *ConnectionPool, deviceID, connName string, timestamp int64) ([]*pb.MetricData, error) {
	var metrics []*pb.MetricData

	labels := map[string]string{
		"connection": connName,
		"host":       pool.Config.Host,
		"port":       strconv.Itoa(pool.Config.Port),
	}

	// 缓冲池指标
	if mc.config.Collection.Metrics.InnoDB.BufferPool {
		bufferPoolMetrics, err := mc.getBufferPoolMetrics(ctx, pool, deviceID, labels, timestamp)
		if err == nil {
			metrics = append(metrics, bufferPoolMetrics...)
		}
	}

	// 日志文件指标
	if mc.config.Collection.Metrics.InnoDB.LogFiles {
		logMetrics, err := mc.getLogFileMetrics(ctx, pool, deviceID, labels, timestamp)
		if err == nil {
			metrics = append(metrics, logMetrics...)
		}
	}

	// 锁指标
	if mc.config.Collection.Metrics.InnoDB.Locks {
		lockMetrics, err := mc.getInnoDBLockMetrics(ctx, pool, deviceID, labels, timestamp)
		if err == nil {
			metrics = append(metrics, lockMetrics...)
		}
	}

	// 事务指标
	if mc.config.Collection.Metrics.InnoDB.Transactions {
		txMetrics, err := mc.getTransactionMetrics(ctx, pool, deviceID, labels, timestamp)
		if err == nil {
			metrics = append(metrics, txMetrics...)
		}
	}

	// 行操作指标
	if mc.config.Collection.Metrics.InnoDB.RowOperations {
		rowMetrics, err := mc.getRowOperationMetrics(ctx, pool, deviceID, labels, timestamp)
		if err == nil {
			metrics = append(metrics, rowMetrics...)
		}
	}

	return metrics, nil
}

// collectReplicationMetrics 采集复制指标
func (mc *MetricsCollector) collectReplicationMetrics(ctx context.Context, pool *ConnectionPool, deviceID, connName string, timestamp int64) ([]*pb.MetricData, error) {
	var metrics []*pb.MetricData

	labels := map[string]string{
		"connection": connName,
		"host":       pool.Config.Host,
		"port":       strconv.Itoa(pool.Config.Port),
	}

	// 从库状态
	if mc.config.Collection.Metrics.Replication.SlaveStatus {
		slaveMetrics, err := mc.getSlaveStatusMetrics(ctx, pool, deviceID, labels, timestamp)
		if err == nil {
			metrics = append(metrics, slaveMetrics...)
		}
	}

	// 主库信息
	if mc.config.Collection.Metrics.Replication.MasterInfo {
		masterMetrics, err := mc.getMasterInfoMetrics(ctx, pool, deviceID, labels, timestamp)
		if err == nil {
			metrics = append(metrics, masterMetrics...)
		}
	}

	return metrics, nil
}

// 具体指标采集方法

// getConnectionMetrics 获取连接指标
func (mc *MetricsCollector) getConnectionMetrics(ctx context.Context, pool *ConnectionPool, deviceID string, labels map[string]string, timestamp int64) ([]*pb.MetricData, error) {
	var metrics []*pb.MetricData

	// 当前连接数
	var threadsConnected float64
	if err := pool.QueryRow(ctx, "SHOW STATUS LIKE 'Threads_connected'").Scan(nil, &threadsConnected); err != nil {
		return nil, err
	}

	metrics = append(metrics, &pb.MetricData{
		DeviceId:  deviceID,
		MetricKey: "mysql.connections.current",
		Timestamp: timestamp,
		ValueType: &pb.MetricData_NumericValue{NumericValue: threadsConnected},
		Labels:    labels,
	})

	// 最大连接数
	var maxConnections float64
	if err := pool.QueryRow(ctx, "SHOW VARIABLES LIKE 'max_connections'").Scan(nil, &maxConnections); err != nil {
		return nil, err
	}

	metrics = append(metrics, &pb.MetricData{
		DeviceId:  deviceID,
		MetricKey: "mysql.connections.max",
		Timestamp: timestamp,
		ValueType: &pb.MetricData_NumericValue{NumericValue: maxConnections},
		Labels:    labels,
	})

	// 连接使用率
	usage := (threadsConnected / maxConnections) * 100
	metrics = append(metrics, &pb.MetricData{
		DeviceId:  deviceID,
		MetricKey: "mysql.connections.usage_percent",
		Timestamp: timestamp,
		ValueType: &pb.MetricData_NumericValue{NumericValue: usage},
		Labels:    labels,
	})

	return metrics, nil
}

// getUptimeMetric 获取运行时间指标
func (mc *MetricsCollector) getUptimeMetric(ctx context.Context, pool *ConnectionPool, deviceID string, labels map[string]string, timestamp int64) (*pb.MetricData, error) {
	var uptime float64
	if err := pool.QueryRow(ctx, "SHOW STATUS LIKE 'Uptime'").Scan(nil, &uptime); err != nil {
		return nil, err
	}

	return &pb.MetricData{
		DeviceId:  deviceID,
		MetricKey: "mysql.uptime.seconds",
		Timestamp: timestamp,
		ValueType: &pb.MetricData_NumericValue{NumericValue: uptime},
		Labels:    labels,
	}, nil
}

// getVersionMetric 获取版本指标
func (mc *MetricsCollector) getVersionMetric(ctx context.Context, pool *ConnectionPool, deviceID string, labels map[string]string, timestamp int64) (*pb.MetricData, error) {
	var version string
	if err := pool.QueryRow(ctx, "SELECT VERSION()").Scan(&version); err != nil {
		return nil, err
	}

	return &pb.MetricData{
		DeviceId:  deviceID,
		MetricKey: "mysql.version.info",
		Timestamp: timestamp,
		ValueType: &pb.MetricData_StringValue{StringValue: version},
		Labels:    labels,
	}, nil
}

// getVariableMetric 获取自定义变量指标
func (mc *MetricsCollector) getVariableMetric(ctx context.Context, pool *ConnectionPool, deviceID string, labels map[string]string, timestamp int64, variable string) (*pb.MetricData, error) {
	query := fmt.Sprintf("SHOW VARIABLES LIKE '%s'", variable)

	var name, value string
	if err := pool.QueryRow(ctx, query).Scan(&name, &value); err != nil {
		return nil, err
	}

	// 尝试解析为数值
	if numValue, err := strconv.ParseFloat(value, 64); err == nil {
		return &pb.MetricData{
			DeviceId:  deviceID,
			MetricKey: fmt.Sprintf("mysql.variable.%s", strings.ToLower(variable)),
			Timestamp: timestamp,
			ValueType: &pb.MetricData_NumericValue{NumericValue: numValue},
			Labels:    labels,
		}, nil
	}

	// 作为字符串
	return &pb.MetricData{
		DeviceId:  deviceID,
		MetricKey: fmt.Sprintf("mysql.variable.%s", strings.ToLower(variable)),
		Timestamp: timestamp,
		ValueType: &pb.MetricData_StringValue{StringValue: value},
		Labels:    labels,
	}, nil
}

// getQPSMetric 获取QPS指标
func (mc *MetricsCollector) getQPSMetric(ctx context.Context, pool *ConnectionPool, deviceID string, labels map[string]string, timestamp int64) (*pb.MetricData, error) {
	var questions, uptime float64

	if err := pool.QueryRow(ctx, "SHOW STATUS LIKE 'Questions'").Scan(nil, &questions); err != nil {
		return nil, err
	}

	if err := pool.QueryRow(ctx, "SHOW STATUS LIKE 'Uptime'").Scan(nil, &uptime); err != nil {
		return nil, err
	}

	qps := questions / uptime

	return &pb.MetricData{
		DeviceId:  deviceID,
		MetricKey: "mysql.performance.qps",
		Timestamp: timestamp,
		ValueType: &pb.MetricData_NumericValue{NumericValue: qps},
		Labels:    labels,
	}, nil
}

// getTPSMetric 获取TPS指标
func (mc *MetricsCollector) getTPSMetric(ctx context.Context, pool *ConnectionPool, deviceID string, labels map[string]string, timestamp int64) (*pb.MetricData, error) {
	var comCommit, comRollback, uptime float64

	if err := pool.QueryRow(ctx, "SHOW STATUS LIKE 'Com_commit'").Scan(nil, &comCommit); err != nil {
		return nil, err
	}

	if err := pool.QueryRow(ctx, "SHOW STATUS LIKE 'Com_rollback'").Scan(nil, &comRollback); err != nil {
		return nil, err
	}

	if err := pool.QueryRow(ctx, "SHOW STATUS LIKE 'Uptime'").Scan(nil, &uptime); err != nil {
		return nil, err
	}

	tps := (comCommit + comRollback) / uptime

	return &pb.MetricData{
		DeviceId:  deviceID,
		MetricKey: "mysql.performance.tps",
		Timestamp: timestamp,
		ValueType: &pb.MetricData_NumericValue{NumericValue: tps},
		Labels:    labels,
	}, nil
}

// getSlowQueryMetrics 获取慢查询指标
func (mc *MetricsCollector) getSlowQueryMetrics(ctx context.Context, pool *ConnectionPool, deviceID string, labels map[string]string, timestamp int64) ([]*pb.MetricData, error) {
	var metrics []*pb.MetricData

	var slowQueries float64
	if err := pool.QueryRow(ctx, "SHOW STATUS LIKE 'Slow_queries'").Scan(nil, &slowQueries); err != nil {
		return nil, err
	}

	metrics = append(metrics, &pb.MetricData{
		DeviceId:  deviceID,
		MetricKey: "mysql.performance.slow_queries.total",
		Timestamp: timestamp,
		ValueType: &pb.MetricData_NumericValue{NumericValue: slowQueries},
		Labels:    labels,
	})

	return metrics, nil
}

// getTableLockMetrics 获取表锁指标
func (mc *MetricsCollector) getTableLockMetrics(ctx context.Context, pool *ConnectionPool, deviceID string, labels map[string]string, timestamp int64) ([]*pb.MetricData, error) {
	var metrics []*pb.MetricData

	query := `
		SELECT 
			SUM(CASE WHEN LOCK_TYPE = 'TABLE' AND LOCK_MODE = 'READ' THEN 1 ELSE 0 END) as table_locks_immediate,
			SUM(CASE WHEN LOCK_TYPE = 'TABLE' AND LOCK_MODE = 'WRITE' THEN 1 ELSE 0 END) as table_locks_waited
		FROM performance_schema.metadata_locks 
		WHERE OBJECT_TYPE = 'TABLE'
	`

	var immediate, waited float64
	if err := pool.QueryRow(ctx, query).Scan(&immediate, &waited); err != nil {
		// 如果performance_schema不可用，使用全局状态
		if err := pool.QueryRow(ctx, "SHOW STATUS LIKE 'Table_locks_immediate'").Scan(nil, &immediate); err != nil {
			return nil, err
		}
		if err := pool.QueryRow(ctx, "SHOW STATUS LIKE 'Table_locks_waited'").Scan(nil, &waited); err != nil {
			return nil, err
		}
	}

	metrics = append(metrics, &pb.MetricData{
		DeviceId:  deviceID,
		MetricKey: "mysql.performance.table_locks.immediate",
		Timestamp: timestamp,
		ValueType: &pb.MetricData_NumericValue{NumericValue: immediate},
		Labels:    labels,
	})

	metrics = append(metrics, &pb.MetricData{
		DeviceId:  deviceID,
		MetricKey: "mysql.performance.table_locks.waited",
		Timestamp: timestamp,
		ValueType: &pb.MetricData_NumericValue{NumericValue: waited},
		Labels:    labels,
	})

	return metrics, nil
}

// getThreadCacheMetrics 获取线程缓存指标
func (mc *MetricsCollector) getThreadCacheMetrics(ctx context.Context, pool *ConnectionPool, deviceID string, labels map[string]string, timestamp int64) ([]*pb.MetricData, error) {
	var metrics []*pb.MetricData

	var threadsCreated, threadsConnected, threadsCached float64

	if err := pool.QueryRow(ctx, "SHOW STATUS LIKE 'Threads_created'").Scan(nil, &threadsCreated); err != nil {
		return nil, err
	}

	if err := pool.QueryRow(ctx, "SHOW STATUS LIKE 'Threads_connected'").Scan(nil, &threadsConnected); err != nil {
		return nil, err
	}

	if err := pool.QueryRow(ctx, "SHOW STATUS LIKE 'Threads_cached'").Scan(nil, &threadsCached); err != nil {
		return nil, err
	}

	metrics = append(metrics, &pb.MetricData{
		DeviceId:  deviceID,
		MetricKey: "mysql.performance.threads.created",
		Timestamp: timestamp,
		ValueType: &pb.MetricData_NumericValue{NumericValue: threadsCreated},
		Labels:    labels,
	})

	metrics = append(metrics, &pb.MetricData{
		DeviceId:  deviceID,
		MetricKey: "mysql.performance.threads.connected",
		Timestamp: timestamp,
		ValueType: &pb.MetricData_NumericValue{NumericValue: threadsConnected},
		Labels:    labels,
	})

	metrics = append(metrics, &pb.MetricData{
		DeviceId:  deviceID,
		MetricKey: "mysql.performance.threads.cached",
		Timestamp: timestamp,
		ValueType: &pb.MetricData_NumericValue{NumericValue: threadsCached},
		Labels:    labels,
	})

	return metrics, nil
}

// getBufferPoolMetrics 获取缓冲池指标
func (mc *MetricsCollector) getBufferPoolMetrics(ctx context.Context, pool *ConnectionPool, deviceID string, labels map[string]string, timestamp int64) ([]*pb.MetricData, error) {
	var metrics []*pb.MetricData

	// 缓冲池大小
	var bufferPoolSize float64
	if err := pool.QueryRow(ctx, "SHOW STATUS LIKE 'Innodb_buffer_pool_pages_total'").Scan(nil, &bufferPoolSize); err != nil {
		return nil, err
	}

	// 缓冲池使用量
	var bufferPoolUsed float64
	if err := pool.QueryRow(ctx, "SHOW STATUS LIKE 'Innodb_buffer_pool_pages_data'").Scan(nil, &bufferPoolUsed); err != nil {
		return nil, err
	}

	// 缓冲池命中率
	var bufferPoolReads, bufferPoolReadRequests float64
	if err := pool.QueryRow(ctx, "SHOW STATUS LIKE 'Innodb_buffer_pool_reads'").Scan(nil, &bufferPoolReads); err != nil {
		return nil, err
	}

	if err := pool.QueryRow(ctx, "SHOW STATUS LIKE 'Innodb_buffer_pool_read_requests'").Scan(nil, &bufferPoolReadRequests); err != nil {
		return nil, err
	}

	hitRatio := ((bufferPoolReadRequests - bufferPoolReads) / bufferPoolReadRequests) * 100

	metrics = append(metrics, &pb.MetricData{
		DeviceId:  deviceID,
		MetricKey: "mysql.innodb.buffer_pool.size",
		Timestamp: timestamp,
		ValueType: &pb.MetricData_NumericValue{NumericValue: bufferPoolSize},
		Labels:    labels,
	})

	metrics = append(metrics, &pb.MetricData{
		DeviceId:  deviceID,
		MetricKey: "mysql.innodb.buffer_pool.used",
		Timestamp: timestamp,
		ValueType: &pb.MetricData_NumericValue{NumericValue: bufferPoolUsed},
		Labels:    labels,
	})

	metrics = append(metrics, &pb.MetricData{
		DeviceId:  deviceID,
		MetricKey: "mysql.innodb.buffer_pool.hit_ratio",
		Timestamp: timestamp,
		ValueType: &pb.MetricData_NumericValue{NumericValue: hitRatio},
		Labels:    labels,
	})

	return metrics, nil
}

// 其他指标采集方法的简化实现...

// getLogFileMetrics 获取日志文件指标
func (mc *MetricsCollector) getLogFileMetrics(ctx context.Context, pool *ConnectionPool, deviceID string, labels map[string]string, timestamp int64) ([]*pb.MetricData, error) {
	// 简化实现
	return []*pb.MetricData{}, nil
}

// getInnoDBLockMetrics 获取InnoDB锁指标
func (mc *MetricsCollector) getInnoDBLockMetrics(ctx context.Context, pool *ConnectionPool, deviceID string, labels map[string]string, timestamp int64) ([]*pb.MetricData, error) {
	// 简化实现
	return []*pb.MetricData{}, nil
}

// getTransactionMetrics 获取事务指标
func (mc *MetricsCollector) getTransactionMetrics(ctx context.Context, pool *ConnectionPool, deviceID string, labels map[string]string, timestamp int64) ([]*pb.MetricData, error) {
	// 简化实现
	return []*pb.MetricData{}, nil
}

// getRowOperationMetrics 获取行操作指标
func (mc *MetricsCollector) getRowOperationMetrics(ctx context.Context, pool *ConnectionPool, deviceID string, labels map[string]string, timestamp int64) ([]*pb.MetricData, error) {
	// 简化实现
	return []*pb.MetricData{}, nil
}

// getSlaveStatusMetrics 获取从库状态指标
func (mc *MetricsCollector) getSlaveStatusMetrics(ctx context.Context, pool *ConnectionPool, deviceID string, labels map[string]string, timestamp int64) ([]*pb.MetricData, error) {
	// 简化实现
	return []*pb.MetricData{}, nil
}

// getMasterInfoMetrics 获取主库信息指标
func (mc *MetricsCollector) getMasterInfoMetrics(ctx context.Context, pool *ConnectionPool, deviceID string, labels map[string]string, timestamp int64) ([]*pb.MetricData, error) {
	// 简化实现
	return []*pb.MetricData{}, nil
}
