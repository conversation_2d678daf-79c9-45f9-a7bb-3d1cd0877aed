package main

import (
	"encoding/json"
	"fmt"
	"time"
)

// MySQLConfig MySQL采集器配置
type MySQLConfig struct {
	// 基础连接配置
	Connections []ConnectionConfig `json:"connections" validate:"required,min=1"`

	// 采集配置
	Collection CollectionConfig `json:"collection"`

	// 性能配置
	Performance PerformanceConfig `json:"performance"`

	// 安全配置
	Security SecurityConfig `json:"security"`
}

// ConnectionConfig 数据库连接配置
type ConnectionConfig struct {
	Name     string `json:"name" validate:"required"`                 // 连接名称，用于标识
	Host     string `json:"host" validate:"required"`                 // 主机地址
	Port     int    `json:"port" validate:"required,min=1,max=65535"` // 端口
	Username string `json:"username" validate:"required"`             // 用户名
	Password string `json:"password"`                                 // 密码
	Database string `json:"database"`                                 // 默认数据库

	// 连接池配置
	MaxOpenConns    int           `json:"max_open_conns" default:"10"`
	MaxIdleConns    int           `json:"max_idle_conns" default:"5"`
	ConnMaxLifetime time.Duration `json:"conn_max_lifetime" default:"3600s"`
	ConnMaxIdleTime time.Duration `json:"conn_max_idle_time" default:"300s"`

	// SSL配置
	SSL *SSLConfig `json:"ssl,omitempty"`

	// 连接参数
	Params map[string]string `json:"params,omitempty"`
}

// SSLConfig SSL连接配置
type SSLConfig struct {
	Enabled    bool   `json:"enabled"`
	CACert     string `json:"ca_cert,omitempty"`
	ClientCert string `json:"client_cert,omitempty"`
	ClientKey  string `json:"client_key,omitempty"`
	SkipVerify bool   `json:"skip_verify"`
}

// CollectionConfig 采集配置
type CollectionConfig struct {
	// 基础指标采集
	Metrics MetricsConfig `json:"metrics"`

	// 数据采集配置
	Data DataConfig `json:"data"`

	// 采集间隔
	Interval time.Duration `json:"interval" default:"30s"`

	// 采集超时
	Timeout time.Duration `json:"timeout" default:"10s"`
}

// MetricsConfig 指标采集配置
type MetricsConfig struct {
	Enabled bool `json:"enabled" default:"true"`

	// 系统指标
	System SystemMetricsConfig `json:"system"`

	// 性能指标
	Performance PerformanceMetricsConfig `json:"performance"`

	// 复制指标
	Replication ReplicationMetricsConfig `json:"replication"`

	// InnoDB指标
	InnoDB InnoDBMetricsConfig `json:"innodb"`
}

// SystemMetricsConfig 系统指标配置
type SystemMetricsConfig struct {
	Enabled     bool     `json:"enabled" default:"true"`
	Connections bool     `json:"connections" default:"true"`
	Uptime      bool     `json:"uptime" default:"true"`
	Version     bool     `json:"version" default:"true"`
	Variables   []string `json:"variables,omitempty"` // 自定义系统变量
}

// PerformanceMetricsConfig 性能指标配置
type PerformanceMetricsConfig struct {
	Enabled     bool `json:"enabled" default:"true"`
	QPS         bool `json:"qps" default:"true"`          // 每秒查询数
	TPS         bool `json:"tps" default:"true"`          // 每秒事务数
	SlowQueries bool `json:"slow_queries" default:"true"` // 慢查询
	TableLocks  bool `json:"table_locks" default:"true"`  // 表锁
	ThreadCache bool `json:"thread_cache" default:"true"` // 线程缓存
	QueryCache  bool `json:"query_cache" default:"true"`  // 查询缓存
	BinaryLog   bool `json:"binary_log" default:"true"`   // 二进制日志
}

// ReplicationMetricsConfig 复制指标配置
type ReplicationMetricsConfig struct {
	Enabled     bool `json:"enabled" default:"false"`
	SlaveStatus bool `json:"slave_status" default:"true"`
	MasterInfo  bool `json:"master_info" default:"true"`
	RelayLog    bool `json:"relay_log" default:"true"`
}

// InnoDBMetricsConfig InnoDB指标配置
type InnoDBMetricsConfig struct {
	Enabled       bool `json:"enabled" default:"true"`
	BufferPool    bool `json:"buffer_pool" default:"true"`
	LogFiles      bool `json:"log_files" default:"true"`
	Locks         bool `json:"locks" default:"true"`
	Transactions  bool `json:"transactions" default:"true"`
	RowOperations bool `json:"row_operations" default:"true"`
}

// DataConfig 数据采集配置
type DataConfig struct {
	Enabled bool `json:"enabled" default:"false"`

	// 自定义查询配置
	Queries []QueryConfig `json:"queries,omitempty"`

	// 跨库查询配置
	CrossDatabase CrossDatabaseConfig `json:"cross_database"`

	// 增量采集配置
	Incremental IncrementalConfig `json:"incremental"`
}

// QueryConfig 查询配置
type QueryConfig struct {
	Name       string            `json:"name" validate:"required"`       // 查询名称
	Connection string            `json:"connection" validate:"required"` // 使用的连接名称
	SQL        string            `json:"sql" validate:"required"`        // SQL查询语句
	Database   string            `json:"database,omitempty"`             // 指定数据库（可选）
	Timeout    time.Duration     `json:"timeout" default:"30s"`          // 查询超时
	Labels     map[string]string `json:"labels,omitempty"`               // 额外标签

	// 数据处理配置
	Processing ProcessingConfig `json:"processing"`

	// 调度配置
	Schedule ScheduleConfig `json:"schedule"`
}

// CrossDatabaseConfig 跨库查询配置
type CrossDatabaseConfig struct {
	Enabled bool `json:"enabled" default:"false"`

	// 跨库查询定义
	Queries []CrossDBQueryConfig `json:"queries,omitempty"`
}

// CrossDBQueryConfig 跨库查询配置
type CrossDBQueryConfig struct {
	Name        string `json:"name" validate:"required"`
	Description string `json:"description,omitempty"`

	// 涉及的数据库连接
	Connections []string `json:"connections" validate:"required,min=2"`

	// 联合查询SQL
	SQL string `json:"sql" validate:"required"`

	// 结果处理
	Processing ProcessingConfig `json:"processing"`

	// 调度配置
	Schedule ScheduleConfig `json:"schedule"`
}

// IncrementalConfig 增量采集配置
type IncrementalConfig struct {
	Enabled bool `json:"enabled" default:"false"`

	// 增量采集查询
	Queries []IncrementalQueryConfig `json:"queries,omitempty"`
}

// IncrementalQueryConfig 增量查询配置
type IncrementalQueryConfig struct {
	Name       string `json:"name" validate:"required"`
	Connection string `json:"connection" validate:"required"`
	Table      string `json:"table" validate:"required"`

	// 增量字段配置
	IncrementalColumn string                `json:"incremental_column" validate:"required"` // 增量字段
	ColumnType        IncrementalColumnType `json:"column_type" validate:"required"`        // 字段类型

	// 查询配置
	Query      string            `json:"query,omitempty"`      // 自定义查询（可选）
	Conditions string            `json:"conditions,omitempty"` // 额外条件
	Labels     map[string]string `json:"labels,omitempty"`

	// 状态存储
	StateStore StateStoreConfig `json:"state_store"`

	// 调度配置
	Schedule ScheduleConfig `json:"schedule"`
}

// IncrementalColumnType 增量字段类型
type IncrementalColumnType string

const (
	IncrementalTimestamp     IncrementalColumnType = "timestamp"      // 时间戳字段
	IncrementalAutoIncrement IncrementalColumnType = "auto_increment" // 自增字段
	IncrementalVersion       IncrementalColumnType = "version"        // 版本字段
)

// StateStoreConfig 状态存储配置
type StateStoreConfig struct {
	Type   StateStoreType         `json:"type" default:"memory"`
	Config map[string]interface{} `json:"config,omitempty"`
}

// StateStoreType 状态存储类型
type StateStoreType string

const (
	StateStoreMemory StateStoreType = "memory" // 内存存储
	StateStoreFile   StateStoreType = "file"   // 文件存储
	StateStoreRedis  StateStoreType = "redis"  // Redis存储
)

// ProcessingConfig 数据处理配置
type ProcessingConfig struct {
	// 结果格式化
	Format DataFormat `json:"format" default:"metrics"`

	// 字段映射
	FieldMapping map[string]string `json:"field_mapping,omitempty"`

	// 数据转换
	Transformations []TransformationConfig `json:"transformations,omitempty"`

	// 聚合配置
	Aggregation *AggregationConfig `json:"aggregation,omitempty"`
}

// DataFormat 数据格式
type DataFormat string

const (
	FormatMetrics DataFormat = "metrics" // 指标格式
	FormatRaw     DataFormat = "raw"     // 原始格式
	FormatJSON    DataFormat = "json"    // JSON格式
)

// TransformationConfig 数据转换配置
type TransformationConfig struct {
	Field     string                 `json:"field" validate:"required"`
	Operation TransformOperation     `json:"operation" validate:"required"`
	Params    map[string]interface{} `json:"params,omitempty"`
}

// TransformOperation 转换操作
type TransformOperation string

const (
	TransformCast      TransformOperation = "cast"      // 类型转换
	TransformCalculate TransformOperation = "calculate" // 计算
	TransformFormat    TransformOperation = "format"    // 格式化
	TransformFilter    TransformOperation = "filter"    // 过滤
)

// AggregationConfig 聚合配置
type AggregationConfig struct {
	GroupBy   []string            `json:"group_by,omitempty"`
	Functions []AggregateFunction `json:"functions" validate:"required"`
	Window    time.Duration       `json:"window" default:"60s"`
}

// AggregateFunction 聚合函数
type AggregateFunction struct {
	Field    string        `json:"field" validate:"required"`
	Function AggregateType `json:"function" validate:"required"`
	Alias    string        `json:"alias,omitempty"`
}

// AggregateType 聚合类型
type AggregateType string

const (
	AggregateSum   AggregateType = "sum"
	AggregateAvg   AggregateType = "avg"
	AggregateMin   AggregateType = "min"
	AggregateMax   AggregateType = "max"
	AggregateCount AggregateType = "count"
)

// ScheduleConfig 调度配置
type ScheduleConfig struct {
	Interval  time.Duration `json:"interval" default:"60s"`
	Cron      string        `json:"cron,omitempty"` // Cron表达式（可选）
	Enabled   bool          `json:"enabled" default:"true"`
	MaxRuns   int           `json:"max_runs,omitempty"`   // 最大运行次数（0为无限制）
	StartTime *time.Time    `json:"start_time,omitempty"` // 开始时间
	EndTime   *time.Time    `json:"end_time,omitempty"`   // 结束时间
}

// PerformanceConfig 性能配置
type PerformanceConfig struct {
	// 并发控制
	MaxConcurrent int `json:"max_concurrent" default:"10"`

	// 缓存配置
	Cache CacheConfig `json:"cache"`

	// 批处理配置
	Batch BatchConfig `json:"batch"`

	// 重试配置
	Retry RetryConfig `json:"retry"`
}

// CacheConfig 缓存配置
type CacheConfig struct {
	Enabled bool          `json:"enabled" default:"true"`
	TTL     time.Duration `json:"ttl" default:"300s"`
	MaxSize int           `json:"max_size" default:"1000"`
}

// BatchConfig 批处理配置
type BatchConfig struct {
	Enabled bool          `json:"enabled" default:"false"`
	Size    int           `json:"size" default:"100"`
	Timeout time.Duration `json:"timeout" default:"30s"`
	MaxWait time.Duration `json:"max_wait" default:"10s"`
}

// RetryConfig 重试配置
type RetryConfig struct {
	Enabled      bool          `json:"enabled" default:"true"`
	MaxAttempts  int           `json:"max_attempts" default:"3"`
	InitialDelay time.Duration `json:"initial_delay" default:"1s"`
	MaxDelay     time.Duration `json:"max_delay" default:"30s"`
	Backoff      BackoffType   `json:"backoff" default:"exponential"`
}

// BackoffType 退避类型
type BackoffType string

const (
	BackoffFixed       BackoffType = "fixed"
	BackoffLinear      BackoffType = "linear"
	BackoffExponential BackoffType = "exponential"
)

// SecurityConfig 安全配置
type SecurityConfig struct {
	// 加密配置
	Encryption EncryptionConfig `json:"encryption"`

	// 访问控制
	AccessControl AccessControlConfig `json:"access_control"`

	// 审计配置
	Audit AuditConfig `json:"audit"`
}

// EncryptionConfig 加密配置
type EncryptionConfig struct {
	Enabled    bool   `json:"enabled" default:"false"`
	Algorithm  string `json:"algorithm" default:"AES-256"`
	KeyFile    string `json:"key_file,omitempty"`
	PassPhrase string `json:"pass_phrase,omitempty"`
}

// AccessControlConfig 访问控制配置
type AccessControlConfig struct {
	Enabled       bool     `json:"enabled" default:"false"`
	AllowedIPs    []string `json:"allowed_ips,omitempty"`
	AllowedUsers  []string `json:"allowed_users,omitempty"`
	RequiredRoles []string `json:"required_roles,omitempty"`
}

// AuditConfig 审计配置
type AuditConfig struct {
	Enabled    bool   `json:"enabled" default:"false"`
	LogFile    string `json:"log_file,omitempty"`
	LogQueries bool   `json:"log_queries" default:"false"`
	LogData    bool   `json:"log_data" default:"false"`
}

// ValidateConfig 验证配置
func (c *MySQLConfig) ValidateConfig() error {
	if len(c.Connections) == 0 {
		return fmt.Errorf("至少需要配置一个数据库连接")
	}

	// 验证连接名称唯一性
	connNames := make(map[string]bool)
	for _, conn := range c.Connections {
		if connNames[conn.Name] {
			return fmt.Errorf("连接名称重复: %s", conn.Name)
		}
		connNames[conn.Name] = true

		if conn.Host == "" {
			return fmt.Errorf("连接 %s 的主机地址不能为空", conn.Name)
		}
		if conn.Port <= 0 || conn.Port > 65535 {
			return fmt.Errorf("连接 %s 的端口无效: %d", conn.Name, conn.Port)
		}
		if conn.Username == "" {
			return fmt.Errorf("连接 %s 的用户名不能为空", conn.Name)
		}
	}

	// 验证查询配置中引用的连接名是否存在
	for _, query := range c.Collection.Data.Queries {
		if !connNames[query.Connection] {
			return fmt.Errorf("查询 %s 引用了不存在的连接: %s", query.Name, query.Connection)
		}
	}

	// 验证跨库查询配置
	for _, query := range c.Collection.Data.CrossDatabase.Queries {
		for _, connName := range query.Connections {
			if !connNames[connName] {
				return fmt.Errorf("跨库查询 %s 引用了不存在的连接: %s", query.Name, connName)
			}
		}
	}

	// 验证增量查询配置
	for _, query := range c.Collection.Data.Incremental.Queries {
		if !connNames[query.Connection] {
			return fmt.Errorf("增量查询 %s 引用了不存在的连接: %s", query.Name, query.Connection)
		}
	}

	return nil
}

// ToJSON 转换为JSON字符串
func (c *MySQLConfig) ToJSON() (string, error) {
	data, err := json.MarshalIndent(c, "", "  ")
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// FromJSON 从JSON字符串解析
func (c *MySQLConfig) FromJSON(jsonStr string) error {
	return json.Unmarshal([]byte(jsonStr), c)
}
