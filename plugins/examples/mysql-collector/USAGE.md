# MySQL 采集器插件使用指南

## 快速开始

### 1. 基本配置

创建一个最小的配置文件 `simple-config.json`：

```json
{
  "connections": {
    "default": {
      "host": "localhost",
      "port": 3306,
      "user": "monitor_user",
      "password": "monitor_password",
      "database": "information_schema"
    }
  },
  "collection": {
    "metrics": {
      "system": {
        "enabled": true,
        "interval": 30
      }
    }
  }
}
```

### 2. 创建监控用户

```sql
-- 创建监控用户
CREATE USER 'monitor_user'@'%' IDENTIFIED BY 'monitor_password';

-- 授予必要权限
GRANT SELECT ON *.* TO 'monitor_user'@'%';
GRANT PROCESS ON *.* TO 'monitor_user'@'%';
GRANT REPLICATION CLIENT ON *.* TO 'monitor_user'@'%';

-- 刷新权限
FLUSH PRIVILEGES;
```

### 3. 测试连接

```bash
# 编译插件
cd /path/to/mysql-collector
go build

# 测试配置（需要实际的MySQL实例）
go test -v
```

## 配置示例

### 多数据库监控

```json
{
  "connections": {
    "main_db": {
      "host": "db1.example.com",
      "port": 3306,
      "user": "monitor",
      "password": "secret",
      "database": "application_db",
      "ssl": {
        "enabled": true,
        "skipVerify": false,
        "caFile": "/path/to/ca.pem"
      }
    },
    "replica_db": {
      "host": "db2.example.com",
      "port": 3306,
      "user": "monitor",
      "password": "secret",
      "database": "application_db"
    }
  },
  "collection": {
    "metrics": {
      "system": {
        "enabled": true,
        "interval": 30
      },
      "performance": {
        "enabled": true,
        "interval": 60
      },
      "replication": {
        "enabled": true,
        "interval": 30
      }
    }
  }
}
```

### 自定义查询配置

```json
{
  "connections": {
    "default": {
      "host": "localhost",
      "port": 3306,
      "user": "monitor",
      "password": "secret",
      "database": "mysql"
    }
  },
  "collection": {
    "data": {
      "queries": [
        {
          "name": "table_sizes",
          "sql": "SELECT table_schema, table_name, data_length, index_length FROM information_schema.tables WHERE table_schema NOT IN ('information_schema', 'mysql', 'performance_schema', 'sys')",
          "interval": 300,
          "outputFormat": "table"
        },
        {
          "name": "slow_queries_summary",
          "sql": "SELECT count(*) as slow_count, avg(query_time) as avg_time FROM mysql.slow_log WHERE start_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)",
          "interval": 3600,
          "outputFormat": "metrics"
        }
      ]
    }
  }
}
```

### 增量数据采集

```json
{
  "connections": {
    "default": {
      "host": "localhost",
      "port": 3306,
      "user": "monitor",
      "password": "secret",
      "database": "application_db"
    }
  },
  "collection": {
    "data": {
      "incrementalQueries": [
        {
          "name": "user_activity",
          "sql": "SELECT user_id, action, created_at FROM user_logs WHERE created_at > ?",
          "timestampColumn": "created_at",
          "interval": 60,
          "outputFormat": "json"
        }
      ]
    }
  }
}
```

## 性能优化

### 连接池配置

```json
{
  "connections": {
    "default": {
      "host": "localhost",
      "port": 3306,
      "user": "monitor",
      "password": "secret",
      "database": "mysql",
      "connectionPool": {
        "maxOpenConnections": 20,
        "maxIdleConnections": 10,
        "connectionLifetime": 3600,
        "healthCheckInterval": 30
      }
    }
  }
}
```

### 采集优化

```json
{
  "collection": {
    "performance": {
      "enableConcurrentCollection": true,
      "collectionTimeout": 30,
      "retryAttempts": 3,
      "retryDelay": 5
    },
    "metrics": {
      "system": {
        "enabled": true,
        "interval": 30,
        "priority": 1
      },
      "performance": {
        "enabled": true,
        "interval": 60,
        "priority": 2
      },
      "innodb": {
        "enabled": true,
        "interval": 120,
        "priority": 3
      }
    }
  }
}
```

## 监控指标说明

### 系统指标
- `mysql.connections.current` - 当前连接数
- `mysql.connections.total` - 总连接数
- `mysql.uptime` - 运行时间（秒）
- `mysql.version` - MySQL版本

### 性能指标
- `mysql.queries.select` - SELECT查询数
- `mysql.queries.insert` - INSERT查询数
- `mysql.queries.update` - UPDATE查询数
- `mysql.queries.delete` - DELETE查询数
- `mysql.queries.qps` - 每秒查询数
- `mysql.queries.tps` - 每秒事务数
- `mysql.slow_queries` - 慢查询数

### InnoDB指标
- `mysql.innodb.buffer_pool.pages.total` - 缓冲池总页数
- `mysql.innodb.buffer_pool.pages.free` - 缓冲池空闲页数
- `mysql.innodb.buffer_pool.pages.data` - 缓冲池数据页数
- `mysql.innodb.locks.current` - 当前锁数量
- `mysql.innodb.transactions.current` - 当前事务数

### 复制指标
- `mysql.replication.slave_running` - 从库运行状态
- `mysql.replication.seconds_behind_master` - 复制延迟（秒）
- `mysql.replication.slave_io_running` - IO线程状态
- `mysql.replication.slave_sql_running` - SQL线程状态

## 故障排除

### 常见问题

1. **连接失败**
   ```
   Error: failed to connect to MySQL: dial tcp: connection refused
   ```
   - 检查MySQL服务是否运行
   - 验证主机名和端口号
   - 检查防火墙设置

2. **权限不足**
   ```
   Error: Access denied for user 'monitor'@'localhost'
   ```
   - 确保用户有正确的权限
   - 检查用户是否可以从指定主机连接

3. **SSL连接问题**
   ```
   Error: x509: certificate signed by unknown authority
   ```
   - 验证SSL证书配置
   - 考虑设置 `skipVerify: true` 进行测试

4. **查询超时**
   ```
   Error: context deadline exceeded
   ```
   - 增加 `collectionTimeout` 设置
   - 优化慢查询
   - 检查数据库负载

### 调试模式

启用详细日志：

```json
{
  "collection": {
    "debug": {
      "enableVerboseLogging": true,
      "logSlowQueries": true,
      "slowQueryThreshold": 1.0
    }
  }
}
```

### 性能监控

监控插件本身的性能：

```json
{
  "collection": {
    "monitoring": {
      "enableSelfMonitoring": true,
      "metricsRetention": 3600
    }
  }
}
```

## 最佳实践

### 1. 权限最小化
只授予监控必需的权限：
```sql
GRANT SELECT ON information_schema.* TO 'monitor'@'%';
GRANT SELECT ON performance_schema.* TO 'monitor'@'%';
GRANT PROCESS ON *.* TO 'monitor'@'%';
GRANT REPLICATION CLIENT ON *.* TO 'monitor'@'%';
```

### 2. 连接池优化
根据数据库负载调整连接池：
- 低负载：maxOpenConnections = 5-10
- 中等负载：maxOpenConnections = 10-20
- 高负载：maxOpenConnections = 20-50

### 3. 采集频率
根据需求调整采集频率：
- 系统指标：30秒
- 性能指标：60秒
- InnoDB指标：120秒
- 自定义查询：300秒或更长

### 4. 错误处理
配置适当的重试策略：
```json
{
  "collection": {
    "performance": {
      "retryAttempts": 3,
      "retryDelay": 5,
      "enableGracefulDegradation": true
    }
  }
}
```

### 5. 监控告警
设置关键指标的阈值：
- 连接数 > 80% 最大连接数
- 慢查询数 > 正常水平的200%
- 复制延迟 > 30秒
- InnoDB锁等待 > 10秒

## 集成示例

### 与Prometheus集成

插件输出的指标可以直接导出到Prometheus：

```yaml
# prometheus.yml
scrape_configs:
  - job_name: 'mysql-collector'
    static_configs:
      - targets: ['localhost:8080']
```

### 与Grafana集成

使用预定义的仪表板模板监控MySQL指标。

### 与告警系统集成

配置告警规则：

```yaml
# alert.rules
groups:
  - name: mysql
    rules:
      - alert: MySQLHighConnections
        expr: mysql_connections_current / mysql_max_connections > 0.8
        for: 5m
        annotations:
          summary: "MySQL连接数过高"
```

## 扩展开发

### 添加自定义指标

1. 修改 `metrics.go` 添加新的采集函数
2. 在配置中启用新指标类型
3. 更新文档和测试

### 添加新的数据源

1. 扩展 `ConnectionConfig` 支持新的连接类型
2. 实现相应的连接管理逻辑
3. 添加相应的指标采集器

### 性能优化

1. 使用连接池减少连接开销
2. 实现指标缓存减少重复查询
3. 使用异步采集提高并发性能
4. 实现增量采集减少数据传输

## 版本历史

- v2.0.0: 完整重构，支持多连接、自定义查询、增量采集
- v1.0.0: 基础版本，支持基本的系统指标采集
