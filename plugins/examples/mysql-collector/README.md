# MySQL Collector Plugin v2.0.0

一个功能强大的MySQL数据库采集器插件，支持高级特性如跨数据库查询、增量数据采集和全面的性能监控。

## 主要特性

### 🚀 核心功能
- **多连接支持**: 支持配置多个MySQL数据库连接
- **连接池管理**: 自动连接池管理和健康检查
- **全面指标采集**: 系统、性能、InnoDB、复制指标
- **自定义查询**: 支持自定义SQL查询采集
- **跨数据库查询**: 支持在多个数据库间执行查询
- **增量数据采集**: 支持基于时间戳、自增ID等的增量采集

### 📊 支持的指标类型

#### 系统指标
- 当前连接数和最大连接数
- 服务器运行时间
- MySQL版本信息
- 系统变量

#### 性能指标  
- QPS (每秒查询数)
- TPS (每秒事务数)
- 慢查询统计
- 表锁和线程缓存状态
- 查询缓存命中率

#### InnoDB指标
- 缓冲池命中率和使用情况
- 日志文件状态
- 锁等待和事务状态
- 行操作统计

#### 复制指标
- 主从复制状态
- 复制延迟
- 中继日志状态

## 配置说明

### 基本配置结构

```json
{
  "connections": [
    {
      "name": "primary",
      "host": "localhost",
      "port": 3306,
      "username": "monitoring_user",
      "password": "secure_password",
      "database": "information_schema",
      "max_open_conns": 10,
      "max_idle_conns": 5,
      "conn_max_lifetime": "3600s",
      "conn_max_idle_time": "300s"
    }
  ],
  "collection": {
    "metrics": {
      "enabled": true,
      "system": {
        "enabled": true,
        "connections": true,
        "uptime": true,
        "version": true
      },
      "performance": {
        "enabled": true,
        "qps": true,
        "tps": true,
        "slow_queries": true
      },
      "innodb": {
        "enabled": true,
        "buffer_pool": true,
        "transactions": true
      }
    },
    "data": {
      "enabled": true,
      "queries": [
        {
          "name": "custom_table_stats",
          "connection": "primary",
          "sql": "SELECT table_name, table_rows FROM information_schema.tables WHERE table_schema = 'myapp'",
          "timeout": "30s",
          "processing": {
            "format": "metrics"
          }
        }
      ]
    },
    "interval": "30s",
    "timeout": "10s"
  }
}
```

### 高级配置

#### 跨数据库查询
```json
{
  "collection": {
    "data": {
      "cross_database": {
        "enabled": true,
        "queries": [
          {
            "name": "multi_db_stats",
            "connections": ["primary", "secondary"],
            "sql": "SELECT database_name, COUNT(*) as table_count FROM (SELECT table_schema as database_name FROM information_schema.tables WHERE table_schema NOT IN ('information_schema', 'mysql', 'performance_schema', 'sys')) t GROUP BY database_name",
            "processing": {
              "format": "metrics",
              "aggregation": "sum"
            }
          }
        ]
      }
    }
  }
}
```

#### 增量数据采集
```json
{
  "collection": {
    "data": {
      "incremental": {
        "enabled": true,
        "queries": [
          {
            "name": "new_users",
            "connection": "primary",
            "table": "users",
            "incremental_column": "created_at",
            "column_type": "timestamp",
            "query": "SELECT id, username, created_at FROM users",
            "processing": {
              "format": "events"
            }
          },
          {
            "name": "order_updates",
            "connection": "primary", 
            "table": "orders",
            "incremental_column": "id",
            "column_type": "auto_increment",
            "conditions": "status = 'completed'",
            "processing": {
              "format": "metrics",
              "field_mapping": {
                "total_amount": "order.total_value"
              }
            }
          }
        ]
      }
    }
  }
}
```

## 权限要求

为了正常工作，MySQL用户需要以下权限：

```sql
-- 基础监控权限
GRANT SELECT ON *.* TO 'monitoring_user'@'%';
GRANT PROCESS ON *.* TO 'monitoring_user'@'%';
GRANT REPLICATION CLIENT ON *.* TO 'monitoring_user'@'%';

-- 对于performance_schema的访问
GRANT SELECT ON performance_schema.* TO 'monitoring_user'@'%';

-- 对于information_schema的访问  
GRANT SELECT ON information_schema.* TO 'monitoring_user'@'%';

-- 刷新权限
FLUSH PRIVILEGES;
```

## 数据格式

### 指标数据格式
采集的指标数据将以以下格式发送：

```json
{
  "device_id": "mysql-primary-localhost-3306",
  "metric_key": "mysql.system.connections.current",
  "numeric_value": 42,
  "timestamp": 1640995200,
  "labels": {
    "host": "localhost",
    "port": "3306",
    "connection": "primary"
  }
}
```

### 支持的指标键
- `mysql.system.connections.*` - 连接相关指标
- `mysql.system.uptime.seconds` - 运行时间
- `mysql.performance.qps` - 每秒查询数
- `mysql.performance.tps` - 每秒事务数
- `mysql.performance.slow_queries.*` - 慢查询指标
- `mysql.innodb.buffer_pool.*` - InnoDB缓冲池指标
- `mysql.innodb.transactions.*` - 事务指标
- `mysql.replication.*` - 复制相关指标

## 性能优化

### 连接池配置
```json
{
  "max_open_conns": 10,     // 最大开放连接数
  "max_idle_conns": 5,      // 最大空闲连接数
  "conn_max_lifetime": "3600s",  // 连接最大生命周期
  "conn_max_idle_time": "300s"   // 连接最大空闲时间
}
```

### 采集间隔优化
```json
{
  "collection": {
    "interval": "30s",  // 采集间隔
    "timeout": "10s"    // 查询超时
  }
}
```

## 故障排除

### 常见问题

1. **连接失败**
   - 检查MySQL服务器是否运行
   - 验证用户名密码
   - 确认网络连接

2. **权限不足**
   - 确保用户有必要的SELECT权限
   - 检查performance_schema访问权限

3. **查询超时**
   - 增加查询超时时间
   - 优化自定义SQL查询
   - 检查数据库负载

### 调试模式
```json
{
  "performance": {
    "enable_query_logging": true,
    "log_slow_queries": true,
    "slow_query_threshold": "1s"
  }
}
```

## 版本历史

### v2.0.0 (Current)
- 新增跨数据库查询支持
- 新增增量数据采集功能
- 重构连接管理系统
- 增强配置系统
- 改进错误处理和监控

### v1.0.0
- 基础MySQL指标采集
- 简单连接管理
- 基本配置支持

## 许可证

MIT License

## 贡献

欢迎提交问题报告和功能请求！
