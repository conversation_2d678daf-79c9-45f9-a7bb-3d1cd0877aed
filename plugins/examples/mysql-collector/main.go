package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	"aiops/pkg/pipeline"
	pb "aiops/pkg/proto"

	_ "github.com/go-sql-driver/mysql"
)

// MySQLCollectorPlugin MySQL 采集器插件
type MySQLCollectorPlugin struct {
	name    string
	version string
	config  map[string]interface{}
	running bool

	// MySQL连接配置
	host     string
	port     int
	username string
	password string
	database string

	// 数据库连接
	db *sql.DB

	// 统计信息
	collectCount int64
	errorCount   int64
	lastError    error
	lastCollect  time.Time
	interval     time.Duration
}

// MySQLPluginFactory MySQL插件工厂
type MySQLPluginFactory struct{}

// NewPluginFactory 插件工厂入口函数
func NewPluginFactory() pipeline.PluginFactory {
	return &MySQLPluginFactory{}
}

// CreatePlugin 创建插件实例
func (f *MySQLPluginFactory) CreatePlugin(pluginType pipeline.PluginType, config map[string]interface{}) (pipeline.PipelinePlugin, error) {
	if pluginType != pipeline.CollectorType {
		return nil, ErrUnsupportedPluginType
	}

	plugin := &MySQLCollectorPlugin{
		name:     "mysql_collector",
		version:  "1.0.0",
		config:   config,
		interval: 30 * time.Second,
	}

	return plugin, nil
}

// GetSupportedTypes 获取支持的插件类型
func (f *MySQLPluginFactory) GetSupportedTypes() []pipeline.PluginType {
	return []pipeline.PluginType{pipeline.CollectorType}
}

// GetPluginInfo 获取插件信息
func (f *MySQLPluginFactory) GetPluginInfo() *pipeline.PluginInfo {
	return &pipeline.PluginInfo{
		Name:     "mysql_collector",
		Version:  "1.0.0",
		Type:     pipeline.CollectorType,
		LoadedAt: time.Now(),
		Metadata: map[string]interface{}{
			"description": "MySQL database monitoring collector",
			"author":      "DevInsight Team",
			"category":    "database",
		},
	}
}

// ValidateConfig 验证配置
func (f *MySQLPluginFactory) ValidateConfig(config map[string]interface{}) error {
	// 检查必需的配置项
	requiredFields := []string{"host", "port", "username", "password"}
	for _, field := range requiredFields {
		if _, exists := config[field]; !exists {
			return fmt.Errorf("missing required field: %s", field)
		}
	}
	return nil
}

// GetInfo 获取插件信息
func (p *MySQLCollectorPlugin) GetInfo() *plugininterface.EnhancedPluginInfo {
	return p.info
}

// GetStatus 获取插件状态
func (p *MySQLCollectorPlugin) GetStatus() plugininterface.PluginStatus {
	return p.status
}

// Initialize 初始化插件
func (p *MySQLCollectorPlugin) Initialize(ctx context.Context, config map[string]interface{}) error {
	p.config = config

	// 解析MySQL配置
	configJSON, err := json.Marshal(config)
	if err != nil {
		return fmt.Errorf("failed to marshal config: %w", err)
	}

	p.mysqlConfig = &MySQLConfig{}
	if err := json.Unmarshal(configJSON, p.mysqlConfig); err != nil {
		return fmt.Errorf("failed to unmarshal MySQL config: %w", err)
	}

	// 验证配置
	if err := p.mysqlConfig.ValidateConfig(); err != nil {
		return fmt.Errorf("invalid MySQL config: %w", err)
	}

	// 初始化连接管理器
	p.connManager = NewConnectionManager(p.mysqlConfig)
	if err := p.connManager.Initialize(ctx); err != nil {
		return fmt.Errorf("failed to initialize connection manager: %w", err)
	}

	// 初始化采集器
	p.metricsCollector = NewMetricsCollector(p.connManager, p.mysqlConfig)
	p.dataCollector = NewDataCollector(p.connManager, p.mysqlConfig)

	p.status = plugininterface.StatusLoaded
	return nil
}

// Start 启动插件
func (p *MySQLCollectorPlugin) Start(ctx context.Context) error {
	fmt.Printf("MySQL Collector Plugin started with %d connections\n", len(p.mysqlConfig.Connections))

	// 健康检查所有连接
	healthResults := p.connManager.HealthCheck(ctx)
	for connName, err := range healthResults {
		if err != nil {
			fmt.Printf("Warning: Connection %s health check failed: %v\n", connName, err)
		} else {
			fmt.Printf("Connection %s is healthy\n", connName)
		}
	}

	p.status = plugininterface.StatusRunning
	return nil
}

// Stop 停止插件
func (p *MySQLCollectorPlugin) Stop(ctx context.Context) error {
	fmt.Printf("MySQL Collector Plugin stopping...\n")

	// 关闭连接管理器
	if p.connManager != nil {
		if err := p.connManager.Close(); err != nil {
			fmt.Printf("Warning: Failed to close connection manager: %v\n", err)
		}
	}

	p.status = plugininterface.StatusStopped
	fmt.Printf("MySQL Collector Plugin stopped\n")
	return nil
}

// Reload 重新加载插件
func (p *MySQLCollectorPlugin) Reload(ctx context.Context, config map[string]interface{}) error {
	return p.Initialize(ctx, config)
}

// Shutdown 关闭插件
func (p *MySQLCollectorPlugin) Shutdown(ctx context.Context) error {
	return p.Stop(ctx)
}

// Health 健康检查
func (p *MySQLCollectorPlugin) Health(ctx context.Context) error {
	return nil
}

// Ping 检查插件响应
func (p *MySQLCollectorPlugin) Ping(ctx context.Context) error {
	return nil
}

// ValidateConfig 验证配置
func (p *MySQLCollectorPlugin) ValidateConfig(config map[string]interface{}) error {
	// 解析配置
	configJSON, err := json.Marshal(config)
	if err != nil {
		return fmt.Errorf("failed to marshal config: %w", err)
	}

	mysqlConfig := &MySQLConfig{}
	if err := json.Unmarshal(configJSON, mysqlConfig); err != nil {
		return fmt.Errorf("failed to unmarshal MySQL config: %w", err)
	}

	// 验证配置
	return mysqlConfig.ValidateConfig()
}

// UpdateConfig 更新配置
func (p *MySQLCollectorPlugin) UpdateConfig(ctx context.Context, config map[string]interface{}) error {
	return p.Initialize(ctx, config)
}

// HandleEvent 处理事件
func (p *MySQLCollectorPlugin) HandleEvent(ctx context.Context, event *plugininterface.PluginEvent) error {
	return nil
}

// GetMetrics 获取插件指标
func (p *MySQLCollectorPlugin) GetMetrics(ctx context.Context) (map[string]interface{}, error) {
	metrics := map[string]interface{}{
		"plugin_type":       "collector",
		"plugin_name":       "mysql-collector",
		"plugin_version":    "2.0.0",
		"supports_enhanced": true,
	}

	if p.connManager != nil {
		connections := p.connManager.GetAllConnections()
		metrics["connections_count"] = len(connections)

		// 添加连接池统计
		poolStats := make(map[string]interface{})
		for name, pool := range connections {
			stats := pool.Stats()
			poolStats[name] = map[string]interface{}{
				"max_open_connections": stats.MaxOpenConnections,
				"open_connections":     stats.OpenConnections,
				"in_use":               stats.InUse,
				"idle":                 stats.Idle,
			}
		}
		metrics["pool_stats"] = poolStats

		// 健康检查结果
		healthResults := p.connManager.HealthCheck(ctx)
		healthyConnections := 0
		for _, err := range healthResults {
			if err == nil {
				healthyConnections++
			}
		}
		metrics["healthy_connections"] = healthyConnections
	}

	return metrics, nil
}

// GetSupportedDeviceTypes 获取支持的设备类型
func (p *MySQLCollectorPlugin) GetSupportedDeviceTypes() []string {
	return []string{"mysql", "mariadb"}
}

// GetSupportedMetrics 获取支持的指标
func (p *MySQLCollectorPlugin) GetSupportedMetrics(deviceType string) ([]*pb.SupportedMetric, error) {
	var metrics []*pb.SupportedMetric

	// 系统指标
	systemMetrics := []*pb.SupportedMetric{
		{
			MetricKey:     "mysql.connections.current",
			MetricName:    "Current Connections",
			Description:   "Number of currently open connections",
			DataType:      "numeric",
			Unit:          "count",
			IsActive:      true,
			CollectorType: "mysql",
		},
		{
			MetricKey:     "mysql.connections.max",
			MetricName:    "Max Connections",
			Description:   "Maximum number of allowed connections",
			DataType:      "numeric",
			Unit:          "count",
			IsActive:      true,
			CollectorType: "mysql",
		},
		{
			MetricKey:     "mysql.connections.usage_percent",
			MetricName:    "Connection Usage Percentage",
			Description:   "Percentage of connections in use",
			DataType:      "numeric",
			Unit:          "percent",
			IsActive:      true,
			CollectorType: "mysql",
		},
		{
			MetricKey:     "mysql.uptime.seconds",
			MetricName:    "Server Uptime",
			Description:   "Number of seconds the server has been running",
			DataType:      "numeric",
			Unit:          "seconds",
			IsActive:      true,
			CollectorType: "mysql",
		},
	}

	// 性能指标
	performanceMetrics := []*pb.SupportedMetric{
		{
			MetricKey:     "mysql.performance.qps",
			MetricName:    "Queries Per Second",
			Description:   "Average queries executed per second",
			DataType:      "numeric",
			Unit:          "qps",
			IsActive:      true,
			CollectorType: "mysql",
		},
		{
			MetricKey:     "mysql.performance.tps",
			MetricName:    "Transactions Per Second",
			Description:   "Average transactions per second",
			DataType:      "numeric",
			Unit:          "tps",
			IsActive:      true,
			CollectorType: "mysql",
		},
		{
			MetricKey:     "mysql.performance.slow_queries.total",
			MetricName:    "Total Slow Queries",
			Description:   "Total number of slow queries",
			DataType:      "numeric",
			Unit:          "count",
			IsActive:      true,
			CollectorType: "mysql",
		},
	}

	// InnoDB指标
	innodbMetrics := []*pb.SupportedMetric{
		{
			MetricKey:     "mysql.innodb.buffer_pool.hit_ratio",
			MetricName:    "InnoDB Buffer Pool Hit Ratio",
			Description:   "InnoDB buffer pool hit ratio percentage",
			DataType:      "numeric",
			Unit:          "percent",
			IsActive:      true,
			CollectorType: "mysql",
		},
		{
			MetricKey:     "mysql.innodb.buffer_pool.size",
			MetricName:    "InnoDB Buffer Pool Size",
			Description:   "Total size of InnoDB buffer pool in pages",
			DataType:      "numeric",
			Unit:          "pages",
			IsActive:      true,
			CollectorType: "mysql",
		},
		{
			MetricKey:     "mysql.innodb.buffer_pool.used",
			MetricName:    "InnoDB Buffer Pool Used",
			Description:   "Used pages in InnoDB buffer pool",
			DataType:      "numeric",
			Unit:          "pages",
			IsActive:      true,
			CollectorType: "mysql",
		},
	}

	metrics = append(metrics, systemMetrics...)
	metrics = append(metrics, performanceMetrics...)
	metrics = append(metrics, innodbMetrics...)

	return metrics, nil
}

// StartCollection 开始采集
func (p *MySQLCollectorPlugin) StartCollection(ctx context.Context, taskConfig *pb.CollectorTaskConfig) error {
	fmt.Printf("Started collection for task %s on device %s\n", taskConfig.TaskId, taskConfig.DeviceName)

	// 验证连接
	var connName string
	for _, conn := range p.mysqlConfig.Connections {
		if conn.Host == taskConfig.Host && conn.Port == int(taskConfig.Port) {
			connName = conn.Name
			break
		}
	}

	if connName == "" {
		return fmt.Errorf("no matching connection found for %s:%d", taskConfig.Host, taskConfig.Port)
	}

	// 测试连接
	_, err := p.connManager.GetConnection(connName)
	if err != nil {
		return fmt.Errorf("connection validation failed: %w", err)
	}

	return nil
}

// StopCollection 停止采集
func (p *MySQLCollectorPlugin) StopCollection(ctx context.Context, taskID string) error {
	fmt.Printf("Stopped collection for task %s\n", taskID)
	return nil
}

// CollectMetrics 采集指标数据
func (p *MySQLCollectorPlugin) CollectMetrics(ctx context.Context, taskConfig *pb.CollectorTaskConfig) ([]*pb.MetricData, error) {
	if p.metricsCollector == nil {
		return nil, fmt.Errorf("metrics collector not initialized")
	}

	now := time.Now().Unix()
	var allMetrics []*pb.MetricData

	// 查找匹配的连接配置
	var connName string
	for _, conn := range p.mysqlConfig.Connections {
		if conn.Host == taskConfig.Host && conn.Port == int(taskConfig.Port) {
			connName = conn.Name
			break
		}
	}

	if connName == "" {
		// 使用第一个可用连接
		if len(p.mysqlConfig.Connections) > 0 {
			connName = p.mysqlConfig.Connections[0].Name
		}
	}

	if connName == "" {
		return nil, fmt.Errorf("no MySQL connection available")
	}

	// 获取连接池
	pool, err := p.connManager.GetConnection(connName)
	if err != nil {
		return nil, fmt.Errorf("connection %s not found: %w", connName, err)
	}

	// 采集系统指标
	if p.mysqlConfig.Collection.Metrics.System.Enabled {
		systemMetrics, err := p.metricsCollector.collectSystemMetrics(ctx, pool, taskConfig.DeviceId, connName, now)
		if err == nil {
			allMetrics = append(allMetrics, systemMetrics...)
		}
	}

	// 采集性能指标
	if p.mysqlConfig.Collection.Metrics.Performance.Enabled {
		perfMetrics, err := p.metricsCollector.collectPerformanceMetrics(ctx, pool, taskConfig.DeviceId, connName, now)
		if err == nil {
			allMetrics = append(allMetrics, perfMetrics...)
		}
	}

	// 采集InnoDB指标
	if p.mysqlConfig.Collection.Metrics.InnoDB.Enabled {
		innodbMetrics, err := p.metricsCollector.collectInnoDBMetrics(ctx, pool, taskConfig.DeviceId, connName, now)
		if err == nil {
			allMetrics = append(allMetrics, innodbMetrics...)
		}
	}

	// 采集复制指标
	if p.mysqlConfig.Collection.Metrics.Replication.Enabled {
		replMetrics, err := p.metricsCollector.collectReplicationMetrics(ctx, pool, taskConfig.DeviceId, connName, now)
		if err == nil {
			allMetrics = append(allMetrics, replMetrics...)
		}
	}

	// 为每个指标添加标签
	for _, metric := range allMetrics {
		if metric.Labels == nil {
			metric.Labels = make(map[string]string)
		}
		metric.Labels["host"] = taskConfig.Host
		metric.Labels["port"] = fmt.Sprintf("%d", taskConfig.Port)
		metric.Labels["connection"] = connName
	}

	return allMetrics, nil
}

// DiscoverDevices 发现设备
func (p *MySQLCollectorPlugin) DiscoverDevices(ctx context.Context, criteria *plugininterface.DiscoveryCriteria) ([]*plugininterface.DeviceInfo, error) {
	var devices []*plugininterface.DeviceInfo

	// 基于配置中的连接发现设备
	for _, conn := range p.mysqlConfig.Connections {
		// 尝试连接以验证设备可用性
		pool, err := p.connManager.GetConnection(conn.Name)
		if err != nil {
			continue // 跳过不可用的连接
		}

		// 获取数据库信息
		var version, engine string
		dbInfo, err := pool.GetDatabaseInfo(ctx)
		if err == nil {
			if v, ok := dbInfo["version"].(string); ok {
				version = v
			}
			if e, ok := dbInfo["default_storage_engine"].(string); ok {
				engine = e
			}
		}

		device := &plugininterface.DeviceInfo{
			ID:           fmt.Sprintf("mysql-%s-%s-%d", conn.Name, conn.Host, conn.Port),
			Name:         fmt.Sprintf("MySQL Server (%s)", conn.Name),
			Type:         "mysql",
			Address:      fmt.Sprintf("%s:%d", conn.Host, conn.Port),
			Status:       "active",
			Capabilities: []string{"metrics", "monitoring", "cross-database-queries"},
			Metadata: map[string]string{
				"version":    version,
				"engine":     engine,
				"connection": conn.Name,
				"database":   conn.Database,
			},
			LastSeen: time.Now(),
		}
		devices = append(devices, device)
	}

	// 如果没有配置的连接，返回本地默认实例
	if len(devices) == 0 {
		devices = append(devices, &plugininterface.DeviceInfo{
			ID:           "mysql-localhost",
			Name:         "Local MySQL Server",
			Type:         "mysql",
			Address:      "localhost:3306",
			Status:       "unknown",
			Capabilities: []string{"metrics", "monitoring"},
			Metadata: map[string]string{
				"version": "unknown",
				"engine":  "InnoDB",
			},
			LastSeen: time.Now(),
		})
	}

	return devices, nil
}

// CreateCollectionTask 创建采集任务
func (p *MySQLCollectorPlugin) CreateCollectionTask(ctx context.Context, config *pb.CollectorTaskConfig) (*plugininterface.CollectionTask, error) {
	task := &plugininterface.CollectionTask{
		ID:        config.TaskId,
		Name:      config.DeviceName, // 使用设备名作为任务名
		Config:    config,
		Status:    "created",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		Statistics: &plugininterface.CollectionStatistics{
			TotalRuns:        0,
			SuccessfulRuns:   0,
			FailedRuns:       0,
			MetricsCollected: 0,
		},
	}
	return task, nil
}

// UpdateCollectionTask 更新采集任务
func (p *MySQLCollectorPlugin) UpdateCollectionTask(ctx context.Context, taskID string, config *pb.CollectorTaskConfig) error {
	return nil
}

// DeleteCollectionTask 删除采集任务
func (p *MySQLCollectorPlugin) DeleteCollectionTask(ctx context.Context, taskID string) error {
	return nil
}

// ListCollectionTasks 列出采集任务
func (p *MySQLCollectorPlugin) ListCollectionTasks(ctx context.Context) ([]*plugininterface.CollectionTask, error) {
	return []*plugininterface.CollectionTask{}, nil
}

// StartContinuousCollection 开始连续采集
func (p *MySQLCollectorPlugin) StartContinuousCollection(ctx context.Context, taskID string) error {
	return nil
}

// StopContinuousCollection 停止连续采集
func (p *MySQLCollectorPlugin) StopContinuousCollection(ctx context.Context, taskID string) error {
	return nil
}

// BatchCollect 批量采集
func (p *MySQLCollectorPlugin) BatchCollect(ctx context.Context, tasks []*pb.CollectorTaskConfig) (map[string][]*pb.MetricData, error) {
	result := make(map[string][]*pb.MetricData)

	// 并发采集以提高性能
	type collectResult struct {
		taskID  string
		metrics []*pb.MetricData
		err     error
	}

	resultCh := make(chan collectResult, len(tasks))

	// 启动goroutine进行并发采集
	for _, task := range tasks {
		go func(t *pb.CollectorTaskConfig) {
			metrics, err := p.CollectMetrics(ctx, t)
			resultCh <- collectResult{
				taskID:  t.TaskId,
				metrics: metrics,
				err:     err,
			}
		}(task)
	}

	// 收集结果
	for i := 0; i < len(tasks); i++ {
		select {
		case res := <-resultCh:
			if res.err == nil {
				result[res.taskID] = res.metrics
			}
		case <-ctx.Done():
			return result, ctx.Err()
		}
	}

	return result, nil
}

// PreviewCollection 预览采集
func (p *MySQLCollectorPlugin) PreviewCollection(ctx context.Context, config *pb.CollectorTaskConfig) (*plugininterface.CollectionPreview, error) {
	// 估算指标数量
	var estimatedMetrics int
	var warnings []string
	var recommendations []string

	if p.mysqlConfig.Collection.Metrics.System.Enabled {
		estimatedMetrics += 10 // 基础系统指标
	}
	if p.mysqlConfig.Collection.Metrics.Performance.Enabled {
		estimatedMetrics += 15 // 性能指标
	}
	if p.mysqlConfig.Collection.Metrics.InnoDB.Enabled {
		estimatedMetrics += 20 // InnoDB指标
	}
	if p.mysqlConfig.Collection.Metrics.Replication.Enabled {
		estimatedMetrics += 8 // 复制指标
	}

	// 添加自定义查询指标
	estimatedMetrics += len(p.mysqlConfig.Collection.Data.Queries)

	// 生成样本数据
	sampleData := []*pb.MetricData{
		{
			DeviceId:  config.DeviceId,
			MetricKey: "mysql.system.connections.current",
			Timestamp: time.Now().Unix(),
			ValueType: &pb.MetricData_NumericValue{NumericValue: 42.0},
			Labels: map[string]string{
				"host": config.Host,
				"port": fmt.Sprintf("%d", config.Port),
			},
		},
		{
			DeviceId:  config.DeviceId,
			MetricKey: "mysql.performance.qps",
			Timestamp: time.Now().Unix(),
			ValueType: &pb.MetricData_NumericValue{NumericValue: 156.7},
			Labels: map[string]string{
				"host": config.Host,
				"port": fmt.Sprintf("%d", config.Port),
			},
		},
	}

	// 添加警告
	if !p.mysqlConfig.Collection.Metrics.Performance.Enabled {
		warnings = append(warnings, "Performance metrics are disabled - consider enabling for better monitoring")
	}

	// 添加建议
	recommendations = append(recommendations, "Consider enabling slow query log for better monitoring")
	if p.mysqlConfig.Collection.Interval > time.Minute {
		recommendations = append(recommendations, "Collection interval is longer than 1 minute - consider shorter intervals for real-time monitoring")
	}

	return &plugininterface.CollectionPreview{
		EstimatedMetrics: estimatedMetrics,
		SampleData:       sampleData,
		Warnings:         warnings,
		Recommendations:  recommendations,
	}, nil
}

// GetCollectionHistory 获取采集历史
func (p *MySQLCollectorPlugin) GetCollectionHistory(ctx context.Context, taskID string, timeRange *plugininterface.TimeRange) ([]*pb.MetricData, error) {
	return []*pb.MetricData{}, nil
}

// EnableCaching 启用缓存
func (p *MySQLCollectorPlugin) EnableCaching(enabled bool, ttl time.Duration) error {
	return nil
}

// InvalidateCache 清除缓存
func (p *MySQLCollectorPlugin) InvalidateCache(ctx context.Context, keys []string) error {
	return nil
}

// GetConfigSchema 获取配置模式
func (p *MySQLCollectorPlugin) GetConfigSchema() *plugininterface.ConfigSchema {
	return &plugininterface.ConfigSchema{
		Schema: map[string]any{
			"type": "object",
			"properties": map[string]any{
				"host": map[string]any{
					"type":        "string",
					"description": "MySQL server host",
					"default":     "localhost",
				},
				"port": map[string]any{
					"type":        "integer",
					"description": "MySQL server port",
					"default":     3306,
					"minimum":     1,
					"maximum":     65535,
				},
				"username": map[string]any{
					"type":        "string",
					"description": "MySQL username",
					"default":     "root",
				},
				"password": map[string]any{
					"type":        "string",
					"description": "MySQL password",
				},
				"database": map[string]any{
					"type":        "string",
					"description": "MySQL database name",
				},
				"collection_interval": map[string]any{
					"type":        "integer",
					"description": "Collection interval in seconds",
					"default":     60,
					"minimum":     1,
				},
			},
			"required": []string{"host", "username"},
		},
		Required: []string{"host", "username"},
		Defaults: map[string]any{
			"host":                "localhost",
			"port":                3306,
			"username":            "root",
			"collection_interval": 60,
		},
		Examples: []map[string]any{
			{
				"host":                "localhost",
				"port":                3306,
				"username":            "monitoring",
				"password":            "monitor123",
				"database":            "performance_schema",
				"collection_interval": 30,
			},
		},
	}
}

// MySQLPluginFactory MySQL 插件工厂
type MySQLPluginFactory struct{}

// CreatePlugin 创建插件实例
func (f *MySQLPluginFactory) CreatePlugin(pluginType plugininterface.PluginType, config map[string]interface{}) (plugininterface.Plugin, error) {
	if pluginType != plugininterface.CollectorPlugin {
		return nil, fmt.Errorf("unsupported plugin type: %s", pluginType)
	}

	plugin := &MySQLCollectorPlugin{
		info: &plugininterface.EnhancedPluginInfo{
			Name:        "mysql-collector",
			Version:     "2.0.0",
			Type:        plugininterface.CollectorPlugin,
			Description: "Advanced MySQL database metrics and data collector with cross-database query support",
			Author:      "DevInsight Team",
			Homepage:    "https://github.com/devinsight/mysql-collector",
			License:     "MIT",
			Tags:        []string{"mysql", "database", "collector", "cross-database", "advanced"},
			CreatedAt:   time.Now(),
			APIVersion:  "2.0",
		},
		status: plugininterface.StatusRegistered,
	}

	return plugin, nil
}

// CheckCompatibility 检查兼容性
func (f *MySQLPluginFactory) CheckCompatibility(systemVersion string) error {
	// 简单的版本兼容性检查
	// 这里可以根据实际需求实现更复杂的检查逻辑
	return nil
}

// GetSupportedTypes 获取支持的插件类型
func (f *MySQLPluginFactory) GetSupportedTypes() []plugininterface.PluginType {
	return []plugininterface.PluginType{plugininterface.CollectorPlugin}
}

// GetPluginInfo 获取插件信息
func (f *MySQLPluginFactory) GetPluginInfo() *plugininterface.EnhancedPluginInfo {
	return &plugininterface.EnhancedPluginInfo{
		Name:        "mysql-collector",
		Version:     "2.0.0",
		Type:        plugininterface.CollectorPlugin,
		Description: "Advanced MySQL database metrics and data collector with cross-database query support",
		Author:      "DevInsight Team",
		Homepage:    "https://github.com/devinsight/mysql-collector",
		License:     "MIT",
		Tags:        []string{"mysql", "database", "collector", "cross-database", "advanced"},
		CreatedAt:   time.Now(),
		APIVersion:  "2.0",
	}
}

// ValidateConfig 验证配置
func (f *MySQLPluginFactory) ValidateConfig(pluginType plugininterface.PluginType, config map[string]interface{}) error {
	if pluginType != plugininterface.CollectorPlugin {
		return fmt.Errorf("unsupported plugin type: %s", pluginType)
	}

	if host, ok := config["host"].(string); !ok || host == "" {
		return fmt.Errorf("MySQL host is required")
	}

	if port, ok := config["port"].(int); !ok || port <= 0 {
		return fmt.Errorf("MySQL port must be positive")
	}

	if username, ok := config["username"].(string); !ok || username == "" {
		return fmt.Errorf("MySQL username is required")
	}

	return nil
}

// GetConfigSchema 获取配置模式
func (f *MySQLPluginFactory) GetConfigSchema(pluginType plugininterface.PluginType) *plugininterface.ConfigSchema {
	return &plugininterface.ConfigSchema{
		Schema: map[string]any{
			"type": "object",
			"properties": map[string]any{
				"host": map[string]any{
					"type":        "string",
					"description": "MySQL server host",
					"default":     "localhost",
				},
				"port": map[string]any{
					"type":        "integer",
					"description": "MySQL server port",
					"default":     3306,
					"minimum":     1,
					"maximum":     65535,
				},
				"username": map[string]any{
					"type":        "string",
					"description": "MySQL username",
					"default":     "root",
				},
				"password": map[string]any{
					"type":        "string",
					"description": "MySQL password",
				},
				"database": map[string]any{
					"type":        "string",
					"description": "MySQL database name",
				},
				"collection_interval": map[string]any{
					"type":        "integer",
					"description": "Collection interval in seconds",
					"default":     60,
					"minimum":     1,
				},
			},
			"required": []string{"host", "username"},
		},
		Required: []string{"host", "username"},
		Defaults: map[string]any{
			"host":                "localhost",
			"port":                3306,
			"username":            "root",
			"collection_interval": 60,
		},
		Examples: []map[string]any{
			{
				"host":                "localhost",
				"port":                3306,
				"username":            "monitoring",
				"password":            "monitor123",
				"database":            "performance_schema",
				"collection_interval": 30,
			},
		},
	}
}

// GetDependencies 获取依赖
func (f *MySQLPluginFactory) GetDependencies() []plugininterface.PluginDependency {
	return []plugininterface.PluginDependency{}
}

// Initialize 初始化工厂
func (f *MySQLPluginFactory) Initialize(ctx context.Context) error {
	return nil
}

// Shutdown 关闭工厂
func (f *MySQLPluginFactory) Shutdown(ctx context.Context) error {
	return nil
}

// GetPluginInfo 导出函数 - 插件加载器需要
func GetPluginInfo() *plugininterface.PluginInfo {
	return &plugininterface.EnhancedPluginInfo{
		Name:        "mysql-collector",
		Version:     "2.0.0",
		Type:        plugininterface.CollectorPlugin,
		Description: "Advanced MySQL database metrics and data collector with cross-database query support",
		Author:      "DevInsight Team",
		Homepage:    "https://github.com/devinsight/mysql-collector",
		License:     "MIT",
		Tags:        []string{"mysql", "database", "collector", "cross-database", "advanced"},
		CreatedAt:   time.Now(),
		APIVersion:  "2.0",
	}
}

// CreatePluginFactory 导出函数 - 插件加载器需要
func CreatePluginFactory() plugininterface.PluginFactory {
	return &MySQLPluginFactory{}
}

func main() {
	// 这个文件会被编译为动态库(.so文件)
	// 不需要main函数，但为了编译通过而保留
}
