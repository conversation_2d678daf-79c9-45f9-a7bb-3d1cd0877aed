package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"reflect"
	"strings"
	"time"

	pb "aiops/pkg/proto"
)

// DataCollector 数据采集器
type DataCollector struct {
	connManager *ConnectionManager
	config      *MySQLConfig
	stateStore  StateStore
}

// StateStore 状态存储接口
type StateStore interface {
	Get(key string) (any, error)
	Set(key string, value any) error
	Delete(key string) error
}

// MemoryStateStore 内存状态存储
type MemoryStateStore struct {
	data map[string]any
}

func NewMemoryStateStore() *MemoryStateStore {
	return &MemoryStateStore{
		data: make(map[string]any),
	}
}

func (m *MemoryStateStore) Get(key string) (any, error) {
	value, exists := m.data[key]
	if !exists {
		return nil, fmt.Errorf("key not found: %s", key)
	}
	return value, nil
}

func (m *MemoryStateStore) Set(key string, value any) error {
	m.data[key] = value
	return nil
}

func (m *MemoryStateStore) Delete(key string) error {
	delete(m.data, key)
	return nil
}

// NewDataCollector 创建数据采集器
func NewDataCollector(connManager *ConnectionManager, config *MySQLConfig) *DataCollector {
	return &DataCollector{
		connManager: connManager,
		config:      config,
		stateStore:  NewMemoryStateStore(), // 默认使用内存存储
	}
}

// CollectAllData 采集所有数据
func (dc *DataCollector) CollectAllData(ctx context.Context, deviceID string) ([]*pb.MetricData, error) {
	var allData []*pb.MetricData
	timestamp := time.Now().Unix()

	// 采集自定义查询数据
	if dc.config.Collection.Data.Enabled {
		for _, queryConfig := range dc.config.Collection.Data.Queries {
			data, err := dc.executeQuery(ctx, deviceID, queryConfig, timestamp)
			if err != nil {
				// 记录错误但继续处理其他查询
				continue
			}
			allData = append(allData, data...)
		}

		// 采集跨库查询数据
		if dc.config.Collection.Data.CrossDatabase.Enabled {
			for _, crossDBQuery := range dc.config.Collection.Data.CrossDatabase.Queries {
				data, err := dc.executeCrossDBQuery(ctx, deviceID, crossDBQuery, timestamp)
				if err != nil {
					continue
				}
				allData = append(allData, data...)
			}
		}

		// 采集增量数据
		if dc.config.Collection.Data.Incremental.Enabled {
			for _, incrementalQuery := range dc.config.Collection.Data.Incremental.Queries {
				data, err := dc.executeIncrementalQuery(ctx, deviceID, incrementalQuery, timestamp)
				if err != nil {
					continue
				}
				allData = append(allData, data...)
			}
		}
	}

	return allData, nil
}

// executeQuery 执行自定义查询
func (dc *DataCollector) executeQuery(ctx context.Context, deviceID string, queryConfig QueryConfig, timestamp int64) ([]*pb.MetricData, error) {
	pool, err := dc.connManager.GetConnection(queryConfig.Connection)
	if err != nil {
		return nil, fmt.Errorf("failed to get connection %s: %w", queryConfig.Connection, err)
	}

	// 设置查询超时
	queryCtx, cancel := context.WithTimeout(ctx, queryConfig.Timeout)
	defer cancel()

	// 切换到指定数据库（如果有）
	if queryConfig.Database != "" {
		if _, err := pool.Exec(queryCtx, fmt.Sprintf("USE `%s`", queryConfig.Database)); err != nil {
			return nil, fmt.Errorf("failed to switch to database %s: %w", queryConfig.Database, err)
		}
	}

	// 执行查询
	rows, err := pool.Query(queryCtx, queryConfig.SQL)
	if err != nil {
		return nil, fmt.Errorf("failed to execute query %s: %w", queryConfig.Name, err)
	}
	defer rows.Close()

	// 处理查询结果
	return dc.processQueryResults(rows, deviceID, queryConfig, timestamp)
}

// executeCrossDBQuery 执行跨库查询
func (dc *DataCollector) executeCrossDBQuery(ctx context.Context, deviceID string, crossDBQuery CrossDBQueryConfig, timestamp int64) ([]*pb.MetricData, error) {
	// 跨库查询需要特殊处理，这里提供基础实现
	// 实际应用中可能需要更复杂的逻辑来处理多个数据库连接

	if len(crossDBQuery.Connections) == 0 {
		return nil, fmt.Errorf("no connections specified for cross-database query %s", crossDBQuery.Name)
	}

	// 使用第一个连接执行查询（简化实现）
	pool, err := dc.connManager.GetConnection(crossDBQuery.Connections[0])
	if err != nil {
		return nil, fmt.Errorf("failed to get connection %s: %w", crossDBQuery.Connections[0], err)
	}

	rows, err := pool.Query(ctx, crossDBQuery.SQL)
	if err != nil {
		return nil, fmt.Errorf("failed to execute cross-database query %s: %w", crossDBQuery.Name, err)
	}
	defer rows.Close()

	// 创建查询配置用于结果处理
	queryConfig := QueryConfig{
		Name:       crossDBQuery.Name,
		Connection: crossDBQuery.Connections[0],
		Processing: crossDBQuery.Processing,
	}

	return dc.processQueryResults(rows, deviceID, queryConfig, timestamp)
}

// executeIncrementalQuery 执行增量查询
func (dc *DataCollector) executeIncrementalQuery(ctx context.Context, deviceID string, incrementalQuery IncrementalQueryConfig, timestamp int64) ([]*pb.MetricData, error) {
	pool, err := dc.connManager.GetConnection(incrementalQuery.Connection)
	if err != nil {
		return nil, fmt.Errorf("failed to get connection %s: %w", incrementalQuery.Connection, err)
	}

	// 获取上次采集的状态
	stateKey := fmt.Sprintf("incremental_%s_%s", incrementalQuery.Connection, incrementalQuery.Name)
	lastValue, err := dc.stateStore.Get(stateKey)
	if err != nil {
		// 如果没有状态，设置默认值
		lastValue = dc.getDefaultIncrementalValue(incrementalQuery.ColumnType)
	}

	// 构建增量查询SQL
	query := dc.buildIncrementalQuery(incrementalQuery, lastValue)

	rows, err := pool.Query(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to execute incremental query %s: %w", incrementalQuery.Name, err)
	}
	defer rows.Close()

	// 处理结果并更新状态
	results, newMaxValue, err := dc.processIncrementalResults(rows, deviceID, incrementalQuery, timestamp)
	if err != nil {
		return nil, err
	}

	// 更新状态
	if newMaxValue != nil {
		if err := dc.stateStore.Set(stateKey, newMaxValue); err != nil {
			// 记录错误但不影响数据返回
			// 在生产环境中应该使用日志库记录这个错误
			_ = err // 忽略状态存储错误
		}
	}

	return results, nil
}

// processQueryResults 处理查询结果
func (dc *DataCollector) processQueryResults(rows *sql.Rows, deviceID string, queryConfig QueryConfig, timestamp int64) ([]*pb.MetricData, error) {
	var results []*pb.MetricData

	// 获取列信息
	columns, err := rows.Columns()
	if err != nil {
		return nil, fmt.Errorf("failed to get columns: %w", err)
	}

	columnTypes, err := rows.ColumnTypes()
	if err != nil {
		return nil, fmt.Errorf("failed to get column types: %w", err)
	}

	// 处理每一行数据
	for rows.Next() {
		// 创建扫描目标
		values := make([]any, len(columns))
		scanArgs := make([]any, len(columns))

		for i := range values {
			scanArgs[i] = &values[i]
		}

		// 扫描行数据
		if err := rows.Scan(scanArgs...); err != nil {
			continue // 跳过错误行
		}

		// 根据处理配置转换数据
		rowResults, err := dc.processRowData(values, columns, columnTypes, deviceID, queryConfig, timestamp)
		if err != nil {
			continue // 跳过错误行
		}

		results = append(results, rowResults...)
	}

	return results, nil
}

// processRowData 处理行数据
func (dc *DataCollector) processRowData(values []any, columns []string, columnTypes []*sql.ColumnType, deviceID string, queryConfig QueryConfig, timestamp int64) ([]*pb.MetricData, error) {
	var results []*pb.MetricData

	// 基础标签
	labels := make(map[string]string)
	labels["query"] = queryConfig.Name
	labels["connection"] = queryConfig.Connection

	// 添加自定义标签
	for k, v := range queryConfig.Labels {
		labels[k] = v
	}

	switch queryConfig.Processing.Format {
	case FormatMetrics:
		// 指标格式：每个数值列作为一个指标
		for i, column := range columns {
			value := values[i]
			if value == nil {
				continue
			}

			// 应用字段映射
			metricKey := column
			if mappedKey, exists := queryConfig.Processing.FieldMapping[column]; exists {
				metricKey = mappedKey
			}

			// 转换为指标数据
			metric, err := dc.convertToMetric(deviceID, metricKey, value, labels, timestamp)
			if err != nil {
				continue
			}

			results = append(results, metric)
		}

	case FormatRaw:
		// 原始格式：整行作为JSON
		rowData := make(map[string]any)
		for i, column := range columns {
			rowData[column] = values[i]
		}

		jsonData, err := json.Marshal(rowData)
		if err != nil {
			return nil, err
		}

		results = append(results, &pb.MetricData{
			DeviceId:  deviceID,
			MetricKey: fmt.Sprintf("mysql.data.%s.raw", queryConfig.Name),
			Timestamp: timestamp,
			ValueType: &pb.MetricData_StringValue{StringValue: string(jsonData)},
			Labels:    labels,
		})

	case FormatJSON:
		// JSON格式：结构化JSON
		rowData := make(map[string]any)
		for i, column := range columns {
			rowData[column] = dc.convertSQLValueToJSON(values[i])
		}

		jsonData, err := json.Marshal(rowData)
		if err != nil {
			return nil, err
		}

		results = append(results, &pb.MetricData{
			DeviceId:  deviceID,
			MetricKey: fmt.Sprintf("mysql.data.%s.json", queryConfig.Name),
			Timestamp: timestamp,
			ValueType: &pb.MetricData_StringValue{StringValue: string(jsonData)},
			Labels:    labels,
		})
	}

	return results, nil
}

// processIncrementalResults 处理增量查询结果
func (dc *DataCollector) processIncrementalResults(rows *sql.Rows, deviceID string, incrementalQuery IncrementalQueryConfig, timestamp int64) ([]*pb.MetricData, any, error) {
	var results []*pb.MetricData
	var maxValue any

	// 获取列信息
	columns, err := rows.Columns()
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get columns: %w", err)
	}

	columnTypes, err := rows.ColumnTypes()
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get column types: %w", err)
	}

	// 找到增量列的索引
	incrementalColumnIndex := -1
	for i, column := range columns {
		if column == incrementalQuery.IncrementalColumn {
			incrementalColumnIndex = i
			break
		}
	}

	if incrementalColumnIndex == -1 {
		return nil, nil, fmt.Errorf("incremental column %s not found in query results", incrementalQuery.IncrementalColumn)
	}

	// 处理每一行数据
	for rows.Next() {
		values := make([]any, len(columns))
		scanArgs := make([]any, len(columns))

		for i := range values {
			scanArgs[i] = &values[i]
		}

		if err := rows.Scan(scanArgs...); err != nil {
			continue
		}

		// 更新最大值
		currentValue := values[incrementalColumnIndex]
		if maxValue == nil || dc.compareIncrementalValues(currentValue, maxValue, incrementalQuery.ColumnType) > 0 {
			maxValue = currentValue
		}

		// 创建查询配置用于行处理
		queryConfig := QueryConfig{
			Name:       incrementalQuery.Name,
			Connection: incrementalQuery.Connection,
			Labels:     incrementalQuery.Labels,
		}

		// 处理行数据
		rowResults, err := dc.processRowData(values, columns, columnTypes, deviceID, queryConfig, timestamp)
		if err != nil {
			continue
		}

		results = append(results, rowResults...)
	}

	return results, maxValue, nil
}

// buildIncrementalQuery 构建增量查询
func (dc *DataCollector) buildIncrementalQuery(incrementalQuery IncrementalQueryConfig, lastValue any) string {
	var query string

	if incrementalQuery.Query != "" {
		// 使用自定义查询
		query = incrementalQuery.Query
	} else {
		// 构建默认查询
		query = fmt.Sprintf("SELECT * FROM `%s`", incrementalQuery.Table)
	}

	// 添加增量条件
	var condition string
	if lastValue != nil {
		switch incrementalQuery.ColumnType {
		case IncrementalTimestamp:
			condition = fmt.Sprintf("`%s` > '%v'", incrementalQuery.IncrementalColumn, lastValue)
		case IncrementalAutoIncrement, IncrementalVersion:
			condition = fmt.Sprintf("`%s` > %v", incrementalQuery.IncrementalColumn, lastValue)
		}
	}

	// 添加额外条件
	if incrementalQuery.Conditions != "" {
		if condition != "" {
			condition = fmt.Sprintf("(%s) AND (%s)", condition, incrementalQuery.Conditions)
		} else {
			condition = incrementalQuery.Conditions
		}
	}

	// 构建完整查询
	if condition != "" {
		if strings.Contains(strings.ToUpper(query), "WHERE") {
			query = fmt.Sprintf("%s AND %s", query, condition)
		} else {
			query = fmt.Sprintf("%s WHERE %s", query, condition)
		}
	}

	// 添加排序
	query = fmt.Sprintf("%s ORDER BY `%s` ASC", query, incrementalQuery.IncrementalColumn)

	return query
}

// getDefaultIncrementalValue 获取默认增量值
func (dc *DataCollector) getDefaultIncrementalValue(columnType IncrementalColumnType) any {
	switch columnType {
	case IncrementalTimestamp:
		return time.Unix(0, 0) // 1970-01-01
	case IncrementalAutoIncrement, IncrementalVersion:
		return 0
	default:
		return nil
	}
}

// compareIncrementalValues 比较增量值
func (dc *DataCollector) compareIncrementalValues(a, b any, columnType IncrementalColumnType) int {
	switch columnType {
	case IncrementalTimestamp:
		aTime, aOk := a.(time.Time)
		bTime, bOk := b.(time.Time)
		if !aOk || !bOk {
			return 0
		}
		if aTime.After(bTime) {
			return 1
		} else if aTime.Before(bTime) {
			return -1
		}
		return 0

	case IncrementalAutoIncrement, IncrementalVersion:
		aVal := dc.toFloat64(a)
		bVal := dc.toFloat64(b)
		if aVal > bVal {
			return 1
		} else if aVal < bVal {
			return -1
		}
		return 0

	default:
		return 0
	}
}

// convertToMetric 转换为指标数据
func (dc *DataCollector) convertToMetric(deviceID, metricKey string, value any, labels map[string]string, timestamp int64) (*pb.MetricData, error) {
	metric := &pb.MetricData{
		DeviceId:  deviceID,
		MetricKey: metricKey,
		Timestamp: timestamp,
		Labels:    labels,
	}

	// 根据值类型设置指标值
	switch v := value.(type) {
	case nil:
		return nil, fmt.Errorf("cannot convert nil value to metric")
	case bool:
		if v {
			metric.ValueType = &pb.MetricData_NumericValue{NumericValue: 1.0}
		} else {
			metric.ValueType = &pb.MetricData_NumericValue{NumericValue: 0.0}
		}
	case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64:
		metric.ValueType = &pb.MetricData_NumericValue{NumericValue: dc.toFloat64(v)}
	case float32, float64:
		metric.ValueType = &pb.MetricData_NumericValue{NumericValue: dc.toFloat64(v)}
	case string:
		metric.ValueType = &pb.MetricData_StringValue{StringValue: v}
	case []byte:
		metric.ValueType = &pb.MetricData_StringValue{StringValue: string(v)}
	case time.Time:
		metric.ValueType = &pb.MetricData_NumericValue{NumericValue: float64(v.Unix())}
	default:
		// 其他类型转为字符串
		metric.ValueType = &pb.MetricData_StringValue{StringValue: fmt.Sprintf("%v", v)}
	}

	return metric, nil
}

// convertSQLValueToJSON 转换SQL值为JSON兼容格式
func (dc *DataCollector) convertSQLValueToJSON(value any) any {
	if value == nil {
		return nil
	}

	rv := reflect.ValueOf(value)
	switch rv.Kind() {
	case reflect.Ptr:
		if rv.IsNil() {
			return nil
		}
		return dc.convertSQLValueToJSON(rv.Elem().Interface())
	case reflect.Slice:
		if rv.Type().Elem().Kind() == reflect.Uint8 {
			// []byte -> string
			return string(value.([]byte))
		}
	case reflect.Struct:
		if t, ok := value.(time.Time); ok {
			return t.Format(time.RFC3339)
		}
	}

	return value
}

// toFloat64 转换为float64
func (dc *DataCollector) toFloat64(value any) float64 {
	switch v := value.(type) {
	case int:
		return float64(v)
	case int8:
		return float64(v)
	case int16:
		return float64(v)
	case int32:
		return float64(v)
	case int64:
		return float64(v)
	case uint:
		return float64(v)
	case uint8:
		return float64(v)
	case uint16:
		return float64(v)
	case uint32:
		return float64(v)
	case uint64:
		return float64(v)
	case float32:
		return float64(v)
	case float64:
		return v
	default:
		return 0
	}
}
