{"connections": [{"name": "primary", "host": "localhost", "port": 3306, "username": "monitoring", "password": "monitor123", "database": "", "max_open_conns": 10, "max_idle_conns": 5, "conn_max_lifetime": "3600s", "conn_max_idle_time": "300s", "ssl": {"enabled": false, "skip_verify": false}, "params": {"charset": "utf8mb4", "parseTime": "true", "loc": "Local"}}, {"name": "secondary", "host": "slave.mysql.local", "port": 3306, "username": "monitoring", "password": "monitor123", "database": "", "max_open_conns": 5, "max_idle_conns": 2, "conn_max_lifetime": "3600s", "conn_max_idle_time": "300s"}], "collection": {"metrics": {"enabled": true, "system": {"enabled": true, "connections": true, "uptime": true, "version": true, "variables": ["innodb_buffer_pool_size", "max_connections", "query_cache_size"]}, "performance": {"enabled": true, "qps": true, "tps": true, "slow_queries": true, "table_locks": true, "thread_cache": true, "query_cache": true, "binary_log": true}, "replication": {"enabled": true, "slave_status": true, "master_info": true, "relay_log": true}, "innodb": {"enabled": true, "buffer_pool": true, "log_files": true, "locks": true, "transactions": true, "row_operations": true}}, "data": {"enabled": true, "queries": [{"name": "user_statistics", "connection": "primary", "sql": "SELECT user, host, current_connections, total_connections, total_ssl_connections FROM information_schema.user_statistics ORDER BY total_connections DESC LIMIT 10", "database": "", "timeout": "30s", "labels": {"type": "user_stats", "source": "information_schema"}, "processing": {"format": "metrics", "field_mapping": {"current_connections": "mysql.user.current_connections", "total_connections": "mysql.user.total_connections", "total_ssl_connections": "mysql.user.ssl_connections"}}, "schedule": {"interval": "300s", "enabled": true}}, {"name": "table_sizes", "connection": "primary", "sql": "SELECT table_schema, table_name, ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb, table_rows FROM information_schema.tables WHERE table_schema NOT IN ('information_schema', 'mysql', 'performance_schema', 'sys') ORDER BY size_mb DESC LIMIT 20", "database": "information_schema", "timeout": "60s", "labels": {"type": "table_stats"}, "processing": {"format": "metrics", "field_mapping": {"size_mb": "mysql.table.size_mb", "table_rows": "mysql.table.rows"}}, "schedule": {"interval": "600s", "enabled": true}}, {"name": "slow_query_analysis", "connection": "primary", "sql": "SELECT db, sql_text, query_time, lock_time, rows_sent, rows_examined, last_seen FROM mysql.slow_log WHERE last_seen > DATE_SUB(NOW(), INTERVAL 1 HOUR) ORDER BY query_time DESC LIMIT 50", "database": "mysql", "timeout": "45s", "labels": {"type": "slow_query", "analysis": "true"}, "processing": {"format": "json"}, "schedule": {"interval": "300s", "enabled": true}}, {"name": "custom_business_metrics", "connection": "primary", "sql": "SELECT 'orders' as metric, COUNT(*) as value FROM orders WHERE created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR) UNION ALL SELECT 'users' as metric, COUNT(*) as value FROM users WHERE created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)", "database": "business_db", "timeout": "30s", "labels": {"type": "business", "interval": "1h"}, "processing": {"format": "metrics", "field_mapping": {"value": "business.hourly_count"}}, "schedule": {"interval": "3600s", "enabled": true}}], "cross_database": {"enabled": true, "queries": [{"name": "cross_db_user_activity", "description": "分析跨多个数据库的用户活动", "connections": ["primary", "secondary"], "sql": "SELECT u.user_id, u.username, p.total_purchases, l.last_login FROM user_db.users u LEFT JOIN purchase_db.user_purchases p ON u.user_id = p.user_id LEFT JOIN log_db.user_logins l ON u.user_id = l.user_id WHERE u.status = 'active'", "processing": {"format": "json"}, "schedule": {"interval": "1800s", "enabled": true}}, {"name": "multi_db_performance_summary", "description": "多数据库性能汇总", "connections": ["primary", "secondary"], "sql": "SELECT 'primary' as db_role, COUNT(*) as active_connections FROM information_schema.processlist WHERE db IS NOT NULL UNION ALL SELECT 'secondary' as db_role, COUNT(*) as active_connections FROM information_schema.processlist WHERE db IS NOT NULL", "processing": {"format": "metrics", "field_mapping": {"active_connections": "mysql.cross_db.active_connections"}}, "schedule": {"interval": "60s", "enabled": true}}]}, "incremental": {"enabled": true, "queries": [{"name": "user_orders_incremental", "connection": "primary", "table": "orders", "incremental_column": "created_at", "column_type": "timestamp", "query": "SELECT order_id, user_id, amount, status, created_at FROM orders", "conditions": "status IN ('completed', 'processing')", "labels": {"type": "incremental", "table": "orders"}, "state_store": {"type": "memory"}, "schedule": {"interval": "30s", "enabled": true}}, {"name": "audit_log_incremental", "connection": "primary", "table": "audit_logs", "incremental_column": "log_id", "column_type": "auto_increment", "query": "SELECT log_id, user_id, action, table_name, record_id, old_values, new_values, created_at FROM audit_logs", "conditions": "action IN ('INSERT', 'UPDATE', 'DELETE')", "labels": {"type": "audit", "incremental": "true"}, "state_store": {"type": "memory"}, "schedule": {"interval": "10s", "enabled": true}}, {"name": "product_inventory_changes", "connection": "primary", "table": "product_inventory", "incremental_column": "version", "column_type": "version", "query": "SELECT product_id, warehouse_id, quantity, reserved_quantity, version, updated_at FROM product_inventory", "conditions": "", "labels": {"type": "inventory", "change_tracking": "true"}, "state_store": {"type": "memory"}, "schedule": {"interval": "60s", "enabled": true}}]}}, "interval": "30s", "timeout": "10s"}, "performance": {"max_concurrent": 10, "cache": {"enabled": true, "ttl": "300s", "max_size": 1000}, "batch": {"enabled": false, "size": 100, "timeout": "30s", "max_wait": "10s"}, "retry": {"enabled": true, "max_attempts": 3, "initial_delay": "1s", "max_delay": "30s", "backoff": "exponential"}}, "security": {"encryption": {"enabled": false, "algorithm": "AES-256"}, "access_control": {"enabled": false, "allowed_ips": ["127.0.0.1", "10.0.0.0/8"], "allowed_users": ["monitoring", "admin"], "required_roles": ["collector", "monitor"]}, "audit": {"enabled": true, "log_file": "/var/log/mysql-collector-audit.log", "log_queries": false, "log_data": false}}}