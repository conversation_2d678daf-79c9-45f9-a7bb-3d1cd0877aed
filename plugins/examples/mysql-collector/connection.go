package main

import (
	"context"
	"database/sql"
	"fmt"
	"sync"
	"time"

	_ "github.com/go-sql-driver/mysql"
)

// ConnectionManager 连接管理器
type ConnectionManager struct {
	connections map[string]*ConnectionPool
	config      *MySQLConfig
	mutex       sync.RWMutex
}

// ConnectionPool 连接池
type ConnectionPool struct {
	Name   string
	Config ConnectionConfig
	DB     *sql.DB
	mutex  sync.RWMutex
}

// NewConnectionManager 创建连接管理器
func NewConnectionManager(config *MySQLConfig) *ConnectionManager {
	return &ConnectionManager{
		connections: make(map[string]*ConnectionPool),
		config:      config,
	}
}

// Initialize 初始化所有连接
func (cm *ConnectionManager) Initialize(ctx context.Context) error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	for _, connConfig := range cm.config.Connections {
		pool, err := cm.createConnectionPool(connConfig)
		if err != nil {
			return fmt.Errorf("failed to create connection pool for %s: %w", connConfig.Name, err)
		}

		// 测试连接
		if err := pool.Ping(ctx); err != nil {
			pool.Close()
			return fmt.Errorf("failed to ping connection %s: %w", connConfig.Name, err)
		}

		cm.connections[connConfig.Name] = pool
	}

	return nil
}

// createConnectionPool 创建连接池
func (cm *ConnectionManager) createConnectionPool(config ConnectionConfig) (*ConnectionPool, error) {
	dsn := cm.buildDSN(config)

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	// 配置连接池参数
	db.SetMaxOpenConns(config.MaxOpenConns)
	db.SetMaxIdleConns(config.MaxIdleConns)
	db.SetConnMaxLifetime(config.ConnMaxLifetime)
	db.SetConnMaxIdleTime(config.ConnMaxIdleTime)

	pool := &ConnectionPool{
		Name:   config.Name,
		Config: config,
		DB:     db,
	}

	return pool, nil
}

// buildDSN 构建数据源名称
func (cm *ConnectionManager) buildDSN(config ConnectionConfig) string {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/",
		config.Username,
		config.Password,
		config.Host,
		config.Port,
	)

	if config.Database != "" {
		dsn += config.Database
	}

	// 添加参数
	params := make(map[string]string)

	// 默认参数
	params["charset"] = "utf8mb4"
	params["parseTime"] = "true"
	params["loc"] = "Local"
	params["timeout"] = "10s"
	params["readTimeout"] = "30s"
	params["writeTimeout"] = "30s"

	// SSL配置
	if config.SSL != nil && config.SSL.Enabled {
		if config.SSL.SkipVerify {
			params["tls"] = "skip-verify"
		} else {
			params["tls"] = "true"
		}
	}

	// 自定义参数覆盖默认参数
	for k, v := range config.Params {
		params[k] = v
	}

	// 构建参数字符串
	if len(params) > 0 {
		dsn += "?"
		first := true
		for k, v := range params {
			if !first {
				dsn += "&"
			}
			dsn += fmt.Sprintf("%s=%s", k, v)
			first = false
		}
	}

	return dsn
}

// GetConnection 获取连接
func (cm *ConnectionManager) GetConnection(name string) (*ConnectionPool, error) {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	pool, exists := cm.connections[name]
	if !exists {
		return nil, fmt.Errorf("connection %s not found", name)
	}

	return pool, nil
}

// GetAllConnections 获取所有连接
func (cm *ConnectionManager) GetAllConnections() map[string]*ConnectionPool {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	result := make(map[string]*ConnectionPool)
	for name, pool := range cm.connections {
		result[name] = pool
	}

	return result
}

// UpdateConnection 更新连接配置
func (cm *ConnectionManager) UpdateConnection(ctx context.Context, name string, config ConnectionConfig) error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	// 关闭旧连接
	if oldPool, exists := cm.connections[name]; exists {
		oldPool.Close()
	}

	// 创建新连接
	newPool, err := cm.createConnectionPool(config)
	if err != nil {
		return fmt.Errorf("failed to create new connection pool: %w", err)
	}

	// 测试连接
	if err := newPool.Ping(ctx); err != nil {
		newPool.Close()
		return fmt.Errorf("failed to ping new connection: %w", err)
	}

	cm.connections[name] = newPool
	return nil
}

// RemoveConnection 移除连接
func (cm *ConnectionManager) RemoveConnection(name string) error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	if pool, exists := cm.connections[name]; exists {
		pool.Close()
		delete(cm.connections, name)
	}

	return nil
}

// Close 关闭所有连接
func (cm *ConnectionManager) Close() error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	var lastErr error
	for name, pool := range cm.connections {
		if err := pool.Close(); err != nil {
			lastErr = fmt.Errorf("failed to close connection %s: %w", name, err)
		}
	}

	cm.connections = make(map[string]*ConnectionPool)
	return lastErr
}

// HealthCheck 健康检查
func (cm *ConnectionManager) HealthCheck(ctx context.Context) map[string]error {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	results := make(map[string]error)
	for name, pool := range cm.connections {
		results[name] = pool.Ping(ctx)
	}

	return results
}

// ConnectionPool 方法

// Ping 测试连接
func (cp *ConnectionPool) Ping(ctx context.Context) error {
	cp.mutex.RLock()
	defer cp.mutex.RUnlock()

	if cp.DB == nil {
		return fmt.Errorf("database connection is nil")
	}

	return cp.DB.PingContext(ctx)
}

// Query 执行查询
func (cp *ConnectionPool) Query(ctx context.Context, query string, args ...interface{}) (*sql.Rows, error) {
	cp.mutex.RLock()
	defer cp.mutex.RUnlock()

	if cp.DB == nil {
		return nil, fmt.Errorf("database connection is nil")
	}

	return cp.DB.QueryContext(ctx, query, args...)
}

// QueryRow 执行单行查询
func (cp *ConnectionPool) QueryRow(ctx context.Context, query string, args ...interface{}) *sql.Row {
	cp.mutex.RLock()
	defer cp.mutex.RUnlock()

	return cp.DB.QueryRowContext(ctx, query, args...)
}

// Exec 执行语句
func (cp *ConnectionPool) Exec(ctx context.Context, query string, args ...interface{}) (sql.Result, error) {
	cp.mutex.RLock()
	defer cp.mutex.RUnlock()

	if cp.DB == nil {
		return nil, fmt.Errorf("database connection is nil")
	}

	return cp.DB.ExecContext(ctx, query, args...)
}

// Begin 开始事务
func (cp *ConnectionPool) Begin(ctx context.Context) (*sql.Tx, error) {
	cp.mutex.RLock()
	defer cp.mutex.RUnlock()

	if cp.DB == nil {
		return nil, fmt.Errorf("database connection is nil")
	}

	return cp.DB.BeginTx(ctx, nil)
}

// Stats 获取连接池统计
func (cp *ConnectionPool) Stats() sql.DBStats {
	cp.mutex.RLock()
	defer cp.mutex.RUnlock()

	if cp.DB == nil {
		return sql.DBStats{}
	}

	return cp.DB.Stats()
}

// Close 关闭连接池
func (cp *ConnectionPool) Close() error {
	cp.mutex.Lock()
	defer cp.mutex.Unlock()

	if cp.DB == nil {
		return nil
	}

	err := cp.DB.Close()
	cp.DB = nil
	return err
}

// GetDatabaseInfo 获取数据库信息
func (cp *ConnectionPool) GetDatabaseInfo(ctx context.Context) (map[string]interface{}, error) {
	info := make(map[string]interface{})

	// 获取版本信息
	var version string
	if err := cp.QueryRow(ctx, "SELECT VERSION()").Scan(&version); err != nil {
		return nil, fmt.Errorf("failed to get version: %w", err)
	}
	info["version"] = version

	// 获取数据库列表
	rows, err := cp.Query(ctx, "SHOW DATABASES")
	if err != nil {
		return nil, fmt.Errorf("failed to get databases: %w", err)
	}
	defer rows.Close()

	var databases []string
	for rows.Next() {
		var dbName string
		if err := rows.Scan(&dbName); err != nil {
			continue
		}
		databases = append(databases, dbName)
	}
	info["databases"] = databases

	// 获取字符集
	var charset, collation string
	query := "SELECT @@character_set_server, @@collation_server"
	if err := cp.QueryRow(ctx, query).Scan(&charset, &collation); err != nil {
		return nil, fmt.Errorf("failed to get charset: %w", err)
	}
	info["charset"] = charset
	info["collation"] = collation

	// 获取连接池统计
	stats := cp.Stats()
	info["pool_stats"] = map[string]interface{}{
		"max_open_connections": stats.MaxOpenConnections,
		"open_connections":     stats.OpenConnections,
		"in_use":               stats.InUse,
		"idle":                 stats.Idle,
		"wait_count":           stats.WaitCount,
		"wait_duration":        stats.WaitDuration,
		"max_idle_closed":      stats.MaxIdleClosed,
		"max_idle_time_closed": stats.MaxIdleTimeClosed,
		"max_lifetime_closed":  stats.MaxLifetimeClosed,
	}

	return info, nil
}

// ValidateConnection 验证连接配置
func (cp *ConnectionPool) ValidateConnection(ctx context.Context) error {
	// 基本连接测试
	if err := cp.Ping(ctx); err != nil {
		return fmt.Errorf("ping failed: %w", err)
	}

	// 权限测试
	if _, err := cp.Query(ctx, "SELECT 1"); err != nil {
		return fmt.Errorf("query test failed: %w", err)
	}

	// 检查必要权限
	requiredPrivileges := []string{
		"SELECT",
		"SHOW VIEW",
		"SHOW DATABASES",
		"PROCESS",
	}

	for _, privilege := range requiredPrivileges {
		if !cp.hasPrivilege(ctx, privilege) {
			return fmt.Errorf("missing required privilege: %s", privilege)
		}
	}

	return nil
}

// hasPrivilege 检查是否有特定权限
func (cp *ConnectionPool) hasPrivilege(ctx context.Context, privilege string) bool {
	query := "SELECT COUNT(*) FROM information_schema.user_privileges WHERE PRIVILEGE_TYPE = ? AND GRANTEE LIKE CONCAT('%', USER(), '%')"

	var count int
	if err := cp.QueryRow(ctx, query, privilege).Scan(&count); err != nil {
		return false
	}

	return count > 0
}

// ReconnectWithRetry 重连机制
func (cp *ConnectionPool) ReconnectWithRetry(ctx context.Context, maxRetries int, retryDelay time.Duration) error {
	for i := 0; i < maxRetries; i++ {
		if err := cp.Ping(ctx); err == nil {
			return nil
		}

		if i < maxRetries-1 {
			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(retryDelay):
				retryDelay *= 2 // 指数退避
				if retryDelay > 60*time.Second {
					retryDelay = 60 * time.Second
				}
			}
		}
	}

	return fmt.Errorf("failed to reconnect after %d attempts", maxRetries)
}
