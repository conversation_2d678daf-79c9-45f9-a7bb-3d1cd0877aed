package main

import (
	"context"
	"fmt"
	"math"
	"strings"
	"time"

	pb "aiops/pkg/proto"
	plugininterface "aiops/plugins/interface"
)

// SimpleAnalyzerPlugin 简单分析器插件
type SimpleAnalyzerPlugin struct {
	info         *plugininterface.EnhancedPluginInfo
	config       map[string]any
	windowSize   int
	threshold    float64
	metricBuffer map[string][]*pb.MetricData
	status       plugininterface.PluginStatus
}

// GetInfo 获取插件信息
func (p *SimpleAnalyzerPlugin) GetInfo() *plugininterface.EnhancedPluginInfo {
	return p.info
}

// GetStatus 获取插件状态
func (p *SimpleAnalyzerPlugin) GetStatus() plugininterface.PluginStatus {
	return p.status
}

// Initialize 初始化插件
func (p *SimpleAnalyzerPlugin) Initialize(ctx context.Context, config map[string]interface{}) error {
	p.config = config
	p.metricBuffer = make(map[string][]*pb.MetricData)
	p.status = plugininterface.StatusLoaded

	// 从配置中读取参数
	if windowSize, ok := config["window_size"].(int); ok {
		p.windowSize = windowSize
	} else {
		p.windowSize = 100 // 默认窗口大小
	}

	if threshold, ok := config["threshold"].(float64); ok {
		p.threshold = threshold
	} else {
		p.threshold = 1.5 // 默认阈值 (1.5 个标准差) - 更敏感的异常检测
	}

	return nil
}

// Start 启动插件
func (p *SimpleAnalyzerPlugin) Start(ctx context.Context) error {
	fmt.Printf("Simple Analyzer Plugin started\n")
	p.status = plugininterface.StatusRunning
	return nil
}

// Stop 停止插件
func (p *SimpleAnalyzerPlugin) Stop(ctx context.Context) error {
	fmt.Printf("Simple Analyzer Plugin stopped\n")
	p.status = plugininterface.StatusStopped
	return nil
}

// Reload 重新加载插件
func (p *SimpleAnalyzerPlugin) Reload(ctx context.Context, config map[string]interface{}) error {
	return p.Initialize(ctx, config)
}

// Shutdown 关闭插件
func (p *SimpleAnalyzerPlugin) Shutdown(ctx context.Context) error {
	return p.Stop(ctx)
}

// Health 健康检查
func (p *SimpleAnalyzerPlugin) Health(ctx context.Context) error {
	return nil
}

// Ping 检查插件响应
func (p *SimpleAnalyzerPlugin) Ping(ctx context.Context) error {
	return nil
}

// ValidateConfig 验证配置
func (p *SimpleAnalyzerPlugin) ValidateConfig(config map[string]interface{}) error {
	return nil
}

// UpdateConfig 更新配置
func (p *SimpleAnalyzerPlugin) UpdateConfig(ctx context.Context, config map[string]interface{}) error {
	return p.Initialize(ctx, config)
}

// HandleEvent 处理事件
func (p *SimpleAnalyzerPlugin) HandleEvent(ctx context.Context, event *plugininterface.PluginEvent) error {
	return nil
}

// GetConfigSchema 获取配置架构
func (p *SimpleAnalyzerPlugin) GetConfigSchema() *plugininterface.ConfigSchema {
	return &plugininterface.ConfigSchema{
		Schema: map[string]any{
			"type": "object",
			"properties": map[string]any{
				"window_size": map[string]any{
					"type":        "integer",
					"minimum":     10,
					"maximum":     1000,
					"default":     50,
					"description": "Number of data points to keep in sliding window for analysis",
				},
				"threshold": map[string]any{
					"type":        "number",
					"minimum":     1.0,
					"maximum":     10.0,
					"default":     2.0,
					"description": "Z-score threshold for anomaly detection",
				},
			},
			"required": []string{"window_size", "threshold"},
		},
	}
}

// GetMetrics 获取插件指标
func (p *SimpleAnalyzerPlugin) GetMetrics(ctx context.Context) (map[string]interface{}, error) {
	totalMetrics := 0
	for _, buffer := range p.metricBuffer {
		totalMetrics += len(buffer)
	}

	return map[string]interface{}{
		"window_size":       p.windowSize,
		"threshold":         p.threshold,
		"tracked_metrics":   len(p.metricBuffer),
		"total_data_points": totalMetrics,
		"plugin_type":       "analyzer",
		"plugin_name":       "simple-analyzer",
		"plugin_version":    "1.0.0",
		"supports_enhanced": false,
	}, nil
}

// AnalyzeMetrics 分析指标
func (p *SimpleAnalyzerPlugin) AnalyzeMetrics(ctx context.Context, metrics []*pb.MetricData) (*plugininterface.AnalysisResult, error) {
	if len(metrics) == 0 {
		return &plugininterface.AnalysisResult{
			Status:  "no_data",
			Message: "No metrics to analyze",
		}, nil
	}

	// 更新指标缓冲区
	for _, metric := range metrics {
		key := fmt.Sprintf("%s:%s", metric.DeviceId, metric.MetricKey)
		p.addToBuffer(key, metric)
	}

	// 检测异常
	anomalies := p.detectAnomalies()

	result := &plugininterface.AnalysisResult{
		Status:    "analyzed",
		Message:   fmt.Sprintf("Analyzed %d metrics, found %d anomalies", len(metrics), len(anomalies)),
		Timestamp: time.Now(),
		Anomalies: anomalies,
	}

	return result, nil
}

// PredictTrend 预测趋势
func (p *SimpleAnalyzerPlugin) PredictTrend(ctx context.Context, metricKey string, deviceID string, timeHorizon time.Duration) (*plugininterface.TrendPrediction, error) {
	key := fmt.Sprintf("%s:%s", deviceID, metricKey)
	buffer, exists := p.metricBuffer[key]

	if !exists || len(buffer) < 10 {
		return &plugininterface.TrendPrediction{
			MetricKey:      metricKey,
			DeviceID:       deviceID,
			Direction:      "unknown",
			Confidence:     0.0,
			PredictedValue: 0.0,
			TimeHorizon:    time.Duration(timeHorizon) * time.Second,
			Timestamp:      time.Now(),
		}, nil
	}

	// 简单线性趋势分析
	direction, slope, confidence := p.analyzeLinearTrend(buffer)

	// 预测未来值
	lastValue := p.getLastValue(buffer)
	predictedValue := lastValue + slope*timeHorizon.Seconds()

	return &plugininterface.TrendPrediction{
		MetricKey:      metricKey,
		DeviceID:       deviceID,
		Direction:      direction,
		Confidence:     confidence,
		PredictedValue: predictedValue,
		TimeHorizon:    timeHorizon,
		Timestamp:      time.Now(),
	}, nil
}

// DetectAnomalies 检测异常（实现接口方法）
func (p *SimpleAnalyzerPlugin) DetectAnomalies(ctx context.Context, data interface{}) ([]*plugininterface.Anomaly, error) {
	// 尝试将 data 转换为 []*pb.MetricData
	var metrics []*pb.MetricData

	switch v := data.(type) {
	case []*pb.MetricData:
		metrics = v
	case []interface{}:
		// 尝试转换每个元素
		for _, item := range v {
			if metric, ok := item.(*pb.MetricData); ok {
				metrics = append(metrics, metric)
			}
		}
	default:
		return nil, fmt.Errorf("unsupported data type for anomaly detection: %T", data)
	}

	// 更新指标缓冲区
	for _, metric := range metrics {
		key := fmt.Sprintf("%s:%s", metric.DeviceId, metric.MetricKey)
		p.addToBuffer(key, metric)
	}

	// 检测异常
	anomalies := p.detectAnomalies()
	return anomalies, nil
}

// AnalyzeLogs 分析日志（简单实现）
func (p *SimpleAnalyzerPlugin) AnalyzeLogs(ctx context.Context, logs []*pb.LogEntry) (*plugininterface.AnalysisResult, error) {
	return &plugininterface.AnalysisResult{
		Status:    "not_supported",
		Message:   "Log analysis not supported by simple analyzer",
		Timestamp: time.Now(),
	}, nil
}

// AnalyzeEvents 分析事件（简单实现）
func (p *SimpleAnalyzerPlugin) AnalyzeEvents(ctx context.Context, events []*plugininterface.PluginEvent) (*plugininterface.AnalysisResult, error) {
	return &plugininterface.AnalysisResult{
		Status:    "not_supported",
		Message:   "Event analysis not supported by simple analyzer",
		Timestamp: time.Now(),
	}, nil
}

// TrainAnomalyModel 训练异常检测模型（简单实现）
func (p *SimpleAnalyzerPlugin) TrainAnomalyModel(ctx context.Context, trainingData interface{}) error {
	return fmt.Errorf("model training not supported by simple analyzer")
}

// GetAnomalyModel 获取异常检测模型（简单实现）
func (p *SimpleAnalyzerPlugin) GetAnomalyModel() (*plugininterface.AnomalyModel, error) {
	return nil, fmt.Errorf("model access not supported by simple analyzer")
}

// AnalyzeTrends 分析趋势（简单实现）
func (p *SimpleAnalyzerPlugin) AnalyzeTrends(ctx context.Context, data interface{}, timeWindow time.Duration) ([]*plugininterface.TrendAnalysis, error) {
	return []*plugininterface.TrendAnalysis{}, nil
}

// AnalyzeRootCause 根因分析（简单实现）
func (p *SimpleAnalyzerPlugin) AnalyzeRootCause(ctx context.Context, incident *plugininterface.Incident) (*plugininterface.RootCauseAnalysis, error) {
	return nil, fmt.Errorf("root cause analysis not supported by simple analyzer")
}

// GetCorrelations 获取关联性（简单实现）
func (p *SimpleAnalyzerPlugin) GetCorrelations(ctx context.Context, data interface{}) ([]*plugininterface.Correlation, error) {
	return []*plugininterface.Correlation{}, nil
}

// PredictCapacity 容量预测（简单实现）
func (p *SimpleAnalyzerPlugin) PredictCapacity(ctx context.Context, resource string, timeHorizon time.Duration) (*plugininterface.CapacityPrediction, error) {
	return nil, fmt.Errorf("capacity prediction not supported by simple analyzer")
}

// GenerateRecommendations 生成建议（简单实现）
func (p *SimpleAnalyzerPlugin) GenerateRecommendations(ctx context.Context, analysis *plugininterface.AnalysisResult) ([]*plugininterface.Recommendation, error) {
	return []*plugininterface.Recommendation{}, nil
}

// ListModels 列出模型（简单实现）
func (p *SimpleAnalyzerPlugin) ListModels() ([]*plugininterface.AnalysisModel, error) {
	return []*plugininterface.AnalysisModel{}, nil
}

// LoadModel 加载模型（简单实现）
func (p *SimpleAnalyzerPlugin) LoadModel(modelID string) error {
	return fmt.Errorf("model loading not supported by simple analyzer")
}

// SaveModel 保存模型（简单实现）
func (p *SimpleAnalyzerPlugin) SaveModel(model *plugininterface.AnalysisModel) error {
	return fmt.Errorf("model saving not supported by simple analyzer")
}

// DeleteModel 删除模型（简单实现）
func (p *SimpleAnalyzerPlugin) DeleteModel(modelID string) error {
	return fmt.Errorf("model deletion not supported by simple analyzer")
}

// addToBuffer 添加指标到缓冲区
func (p *SimpleAnalyzerPlugin) addToBuffer(key string, metric *pb.MetricData) {
	buffer := p.metricBuffer[key]
	buffer = append(buffer, metric)

	// 保持窗口大小
	if len(buffer) > p.windowSize {
		buffer = buffer[1:]
	}

	p.metricBuffer[key] = buffer
}

// detectAnomalies 检测异常
func (p *SimpleAnalyzerPlugin) detectAnomalies() []*plugininterface.Anomaly {
	var anomalies []*plugininterface.Anomaly

	for key, buffer := range p.metricBuffer {
		if len(buffer) < 5 { // 降低最小数据点要求（从10改为5）
			continue
		}

		// 计算统计信息
		values := p.extractValues(buffer)
		mean := p.calculateMean(values)
		stdDev := p.calculateStdDev(values, mean)

		// 检查最新值是否异常
		latest := values[len(values)-1]
		zScore := math.Abs(latest-mean) / stdDev

		if zScore > p.threshold {
			// 解析 key (deviceID:metricKey)
			parts := strings.Split(key, ":")
			deviceID := ""
			metricKey := ""

			if len(parts) >= 2 {
				deviceID = parts[0]
				metricKey = parts[1]
			}

			anomaly := &plugininterface.Anomaly{
				ID:             fmt.Sprintf("anomaly-%s-%d", metricKey, time.Now().UnixNano()),
				DeviceID:       deviceID,
				MetricKey:      metricKey,
				ActualValue:    latest,
				ExpectedValue:  mean,
				DeviationScore: zScore,
				Severity:       p.calculateSeverity(zScore),
				Description:    fmt.Sprintf("Value %.2f deviates from expected %.2f by %.2f standard deviations", latest, mean, zScore),
				DetectedAt:     time.Now(),
			}

			anomalies = append(anomalies, anomaly)
		}
	}

	return anomalies
}

// analyzeLinearTrend 分析线性趋势
func (p *SimpleAnalyzerPlugin) analyzeLinearTrend(buffer []*pb.MetricData) (string, float64, float64) {
	if len(buffer) < 2 {
		return "unknown", 0.0, 0.0
	}

	// 简单线性回归
	n := len(buffer)
	var sumX, sumY, sumXY, sumX2 float64

	for i, metric := range buffer {
		x := float64(i)
		y := p.getNumericValue(metric)

		sumX += x
		sumY += y
		sumXY += x * y
		sumX2 += x * x
	}

	slope := (float64(n)*sumXY - sumX*sumY) / (float64(n)*sumX2 - sumX*sumX)

	// 确定趋势方向
	direction := "stable"
	if slope > 0.1 {
		direction = "increasing"
	} else if slope < -0.1 {
		direction = "decreasing"
	}

	// 计算置信度 (简化版本)
	confidence := math.Min(math.Abs(slope)*10, 1.0)

	return direction, slope, confidence
}

// extractValues 提取数值
func (p *SimpleAnalyzerPlugin) extractValues(buffer []*pb.MetricData) []float64 {
	values := make([]float64, len(buffer))
	for i, metric := range buffer {
		values[i] = p.getNumericValue(metric)
	}
	return values
}

// getNumericValue 获取指标的数值
func (p *SimpleAnalyzerPlugin) getNumericValue(metric *pb.MetricData) float64 {
	switch v := metric.ValueType.(type) {
	case *pb.MetricData_NumericValue:
		return v.NumericValue
	default:
		return 0.0
	}
}

// getLastValue 获取最后一个值
func (p *SimpleAnalyzerPlugin) getLastValue(buffer []*pb.MetricData) float64 {
	if len(buffer) == 0 {
		return 0.0
	}
	return p.getNumericValue(buffer[len(buffer)-1])
}

// calculateMean 计算均值
func (p *SimpleAnalyzerPlugin) calculateMean(values []float64) float64 {
	if len(values) == 0 {
		return 0.0
	}

	sum := 0.0
	for _, v := range values {
		sum += v
	}
	return sum / float64(len(values))
}

// calculateStdDev 计算标准差
func (p *SimpleAnalyzerPlugin) calculateStdDev(values []float64, mean float64) float64 {
	if len(values) <= 1 {
		return 1.0
	}

	sum := 0.0
	for _, v := range values {
		diff := v - mean
		sum += diff * diff
	}

	variance := sum / float64(len(values)-1)
	return math.Sqrt(variance)
}

// calculateSeverity 计算严重程度
func (p *SimpleAnalyzerPlugin) calculateSeverity(zScore float64) string {
	if zScore >= 3.0 {
		return "critical"
	} else if zScore >= 2.5 {
		return "high"
	} else if zScore >= 2.0 {
		return "medium"
	}
	return "low"
}

// SimpleAnalyzerPluginFactory 简单分析器插件工厂
type SimpleAnalyzerPluginFactory struct{}

// CreatePlugin 创建插件实例
func (f *SimpleAnalyzerPluginFactory) CreatePlugin(pluginType plugininterface.PluginType, config map[string]interface{}) (plugininterface.Plugin, error) {
	if pluginType != plugininterface.AnalyzerPlugin {
		return nil, fmt.Errorf("unsupported plugin type: %s", pluginType)
	}

	plugin := &SimpleAnalyzerPlugin{
		info: &plugininterface.EnhancedPluginInfo{
			Name:        "simple-analyzer",
			Version:     "1.0.0",
			Type:        plugininterface.AnalyzerPlugin,
			Description: "Simple statistical analyzer for anomaly detection and trend prediction",
			Author:      "DevInsight Team",
			Homepage:    "https://github.com/devinsight/simple-analyzer",
			License:     "MIT",
			Tags:        []string{"analyzer", "statistics", "anomaly", "trend"},
			CreatedAt:   time.Now(),
			APIVersion:  "1.0",
		},
		status: plugininterface.StatusRegistered,
	}

	return plugin, nil
}

// GetSupportedTypes 获取支持的插件类型
func (f *SimpleAnalyzerPluginFactory) GetSupportedTypes() []plugininterface.PluginType {
	return []plugininterface.PluginType{plugininterface.AnalyzerPlugin}
}

// CheckCompatibility 检查系统兼容性
func (f *SimpleAnalyzerPluginFactory) CheckCompatibility(systemVersion string) error {
	// 简单的版本兼容性检查
	// 实际应用中可以实现更复杂的版本比较逻辑
	if systemVersion == "" {
		return fmt.Errorf("system version is required")
	}

	// 假设支持 1.0.0 及以上版本
	// 这里可以添加更复杂的版本比较逻辑
	return nil
}

// GetPluginInfo 获取插件信息
func (f *SimpleAnalyzerPluginFactory) GetPluginInfo() *plugininterface.EnhancedPluginInfo {
	return &plugininterface.EnhancedPluginInfo{
		Name:        "simple-analyzer",
		Version:     "1.0.0",
		Type:        plugininterface.AnalyzerPlugin,
		Description: "Simple statistical analyzer for anomaly detection and trend prediction",
		Author:      "DevInsight Team",
		Homepage:    "https://github.com/devinsight/simple-analyzer",
		License:     "MIT",
		Tags:        []string{"analyzer", "statistics", "anomaly", "trend"},
		CreatedAt:   time.Now(),
		APIVersion:  "1.0",
	}
}

// ValidateConfig 验证配置
func (f *SimpleAnalyzerPluginFactory) ValidateConfig(pluginType plugininterface.PluginType, config map[string]interface{}) error {
	if pluginType != plugininterface.AnalyzerPlugin {
		return fmt.Errorf("unsupported plugin type: %s", pluginType)
	}
	return nil
}

// GetConfigSchema 获取配置模式
func (f *SimpleAnalyzerPluginFactory) GetConfigSchema(pluginType plugininterface.PluginType) *plugininterface.ConfigSchema {
	return &plugininterface.ConfigSchema{
		Schema: map[string]any{
			"type": "object",
			"properties": map[string]any{
				"threshold": map[string]any{
					"type":        "number",
					"description": "Anomaly detection threshold",
					"default":     1.5,
				},
				"window_size": map[string]any{
					"type":        "integer",
					"description": "Analysis window size",
					"default":     100,
				},
			},
		},
		Defaults: map[string]any{
			"threshold":   1.5,
			"window_size": 100,
		},
	}
}

// GetDependencies 获取依赖
func (f *SimpleAnalyzerPluginFactory) GetDependencies() []plugininterface.PluginDependency {
	return []plugininterface.PluginDependency{}
}

// Initialize 初始化工厂
func (f *SimpleAnalyzerPluginFactory) Initialize(ctx context.Context) error {
	return nil
}

// Shutdown 关闭工厂
func (f *SimpleAnalyzerPluginFactory) Shutdown(ctx context.Context) error {
	return nil
}

// GetPluginInfo 导出函数 - 插件加载器需要
func GetPluginInfo() *plugininterface.PluginInfo {
	return &plugininterface.PluginInfo{
		Name:        "simple-analyzer",
		Version:     "1.0.0",
		Type:        plugininterface.AnalyzerPlugin,
		Description: "Simple statistical analyzer for anomaly detection and trend prediction",
		Author:      "DevInsight Team",
		Homepage:    "https://github.com/devinsight/simple-analyzer",
		License:     "MIT",
		Tags:        []string{"analyzer", "statistics", "anomaly", "trend"},
		CreatedAt:   time.Now(),
	}
}

// CreatePluginFactory 导出函数 - 插件加载器需要
func CreatePluginFactory() plugininterface.PluginFactory {
	return &SimpleAnalyzerPluginFactory{}
}

func main() {
	// 这个文件会被编译为动态库(.so文件)
	// 不需要main函数，但为了编译通过而保留
}
