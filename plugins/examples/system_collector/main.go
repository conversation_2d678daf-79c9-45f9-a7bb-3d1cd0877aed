package main

import (
	"context"
	"fmt"
	"runtime"
	"time"

	"aiops/pkg/pipeline"
	pb "aiops/pkg/proto"
)

// SystemCollectorPlugin 系统监控采集器插件
type SystemCollectorPlugin struct {
	name     string
	version  string
	config   map[string]interface{}
	interval time.Duration
	running  bool
}

// SystemPluginFactory 系统插件工厂
type SystemPluginFactory struct{}

// NewPluginFactory 插件工厂入口函数
func NewPluginFactory() pipeline.PluginFactory {
	return &SystemPluginFactory{}
}

// CreatePlugin 创建插件实例
func (f *SystemPluginFactory) CreatePlugin(pluginType pipeline.PluginType, config map[string]interface{}) (pipeline.PipelinePlugin, error) {
	if pluginType != pipeline.CollectorType {
		return nil, ErrUnsupportedPluginType
	}

	plugin := &SystemCollectorPlugin{
		name:     "system_collector",
		version:  "1.0.0",
		config:   config,
		interval: 30 * time.Second,
	}

	return plugin, nil
}

// GetSupportedTypes 获取支持的插件类型
func (f *SystemPluginFactory) GetSupportedTypes() []pipeline.PluginType {
	return []pipeline.PluginType{pipeline.CollectorType}
}

// GetPluginInfo 获取插件信息
func (f *SystemPluginFactory) GetPluginInfo() *pipeline.PluginInfo {
	return &pipeline.PluginInfo{
		Name:     "system_collector",
		Version:  "1.0.0",
		Type:     pipeline.CollectorType,
		LoadedAt: time.Now(),
		Metadata: map[string]interface{}{
			"description": "System resource monitoring collector",
			"author":      "DevInsight Team",
			"category":    "system",
		},
	}
}

// ValidateConfig 验证配置
func (f *SystemPluginFactory) ValidateConfig(config map[string]interface{}) error {
	// 简单的配置验证
	return nil
}

// ==================== 插件实现 ====================

// GetName 获取插件名称
func (p *SystemCollectorPlugin) GetName() string {
	return p.name
}

// GetType 获取插件类型
func (p *SystemCollectorPlugin) GetType() pipeline.PluginType {
	return pipeline.CollectorType
}

// GetVersion 获取插件版本
func (p *SystemCollectorPlugin) GetVersion() string {
	return p.version
}

// Initialize 初始化插件
func (p *SystemCollectorPlugin) Initialize(config map[string]interface{}) error {
	p.config = config

	// 解析配置
	if interval, ok := config["interval"]; ok {
		if intervalStr, ok := interval.(string); ok {
			if d, err := time.ParseDuration(intervalStr); err == nil {
				p.interval = d
			}
		}
	}

	return nil
}

// Start 启动插件
func (p *SystemCollectorPlugin) Start(ctx context.Context) error {
	p.running = true
	return nil
}

// Stop 停止插件
func (p *SystemCollectorPlugin) Stop() error {
	p.running = false
	return nil
}

// Health 健康检查
func (p *SystemCollectorPlugin) Health() error {
	if !p.running {
		return ErrPluginNotRunning
	}
	return nil
}

// Process 处理数据（采集器不需要实现）
func (p *SystemCollectorPlugin) Process(ctx context.Context, data *pipeline.PipelineData) (*pipeline.PipelineData, error) {
	return data, nil
}

// GetConfig 获取配置
func (p *SystemCollectorPlugin) GetConfig() map[string]interface{} {
	return p.config
}

// UpdateConfig 更新配置
func (p *SystemCollectorPlugin) UpdateConfig(config map[string]interface{}) error {
	p.config = config
	return nil
}

// GetInputSchema 获取输入模式
func (p *SystemCollectorPlugin) GetInputSchema() *pipeline.Schema {
	return nil // 采集器没有输入
}

// GetOutputSchema 获取输出模式
func (p *SystemCollectorPlugin) GetOutputSchema() *pipeline.Schema {
	return &pipeline.Schema{
		Fields: map[string]pipeline.FieldSchema{
			"cpu_usage": {
				Type:        "float",
				Description: "CPU使用率百分比",
				Required:    true,
			},
			"memory_usage": {
				Type:        "float",
				Description: "内存使用率百分比",
				Required:    true,
			},
			"goroutine_count": {
				Type:        "int",
				Description: "Goroutine数量",
				Required:    true,
			},
		},
		Description: "系统资源监控数据",
		Version:     "1.0",
	}
}

// GetMetrics 获取插件指标
func (p *SystemCollectorPlugin) GetMetrics() *pipeline.PluginMetrics {
	return &pipeline.PluginMetrics{
		Name:            p.name,
		ProcessedCount:  0,
		ErrorCount:      0,
		AvgLatency:      0,
		LastProcessTime: time.Now(),
		Status:          "running",
		CustomMetrics: map[string]any{
			"interval": p.interval.String(),
		},
	}
}

// ==================== 采集器专用方法 ====================

// Collect 采集数据
func (p *SystemCollectorPlugin) Collect(ctx context.Context) (*pipeline.PipelineData, error) {
	if !p.running {
		return nil, ErrPluginNotRunning
	}

	// 获取系统信息
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	// 创建流水线数据
	data := &pipeline.PipelineData{
		ID:        generateDataID(),
		Type:      pipeline.MetricDataType,
		Source:    p.name,
		DeviceID:  "localhost",
		Timestamp: time.Now(),
		Metadata: map[string]interface{}{
			"collector": p.name,
			"version":   p.version,
		},
		Context: map[string]interface{}{
			"host": "localhost",
		},
		Tags: map[string]string{
			"type":   "system",
			"source": "go_runtime",
		},
	}

	// 添加系统指标
	data.Metrics = []*pb.MetricData{
		{
			MetricKey: "system.memory.alloc",
			ValueType: &pb.MetricData_NumericValue{
				NumericValue: float64(memStats.Alloc),
			},
			Timestamp: time.Now().Unix(),
			DeviceId:  "localhost",
			Labels: map[string]string{
				"unit": "bytes",
				"type": "memory",
			},
		},
		{
			MetricKey: "system.memory.sys",
			ValueType: &pb.MetricData_NumericValue{
				NumericValue: float64(memStats.Sys),
			},
			Timestamp: time.Now().Unix(),
			DeviceId:  "localhost",
			Labels: map[string]string{
				"unit": "bytes",
				"type": "memory",
			},
		},
		{
			MetricKey: "system.goroutines",
			ValueType: &pb.MetricData_NumericValue{
				NumericValue: float64(runtime.NumGoroutine()),
			},
			Timestamp: time.Now().Unix(),
			DeviceId:  "localhost",
			Labels: map[string]string{
				"type": "runtime",
			},
		},
	}

	return data, nil
}

// GetCollectInterval 获取采集间隔
func (p *SystemCollectorPlugin) GetCollectInterval() time.Duration {
	return p.interval
}

// SetCollectInterval 设置采集间隔
func (p *SystemCollectorPlugin) SetCollectInterval(interval time.Duration) error {
	p.interval = interval
	return nil
}

// generateDataID 生成数据ID
func generateDataID() string {
	return fmt.Sprintf("system_%d", time.Now().UnixNano())
}

// 定义错误类型
var (
	ErrUnsupportedPluginType = fmt.Errorf("unsupported plugin type")
	ErrPluginNotRunning      = fmt.Errorf("plugin not running")
)

// main 函数（插件编译时需要）
func main() {
	// 插件作为共享库时不需要main函数
	// 但为了编译通过，保留空的main函数
}
