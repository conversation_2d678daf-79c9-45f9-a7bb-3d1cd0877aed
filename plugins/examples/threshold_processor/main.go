package main

import (
	"context"
	"fmt"
	"time"

	"aiops/pkg/pipeline"
	pb "aiops/pkg/proto"
)

// ThresholdProcessorPlugin 阈值处理器插件
type ThresholdProcessorPlugin struct {
	name     string
	version  string
	config   map[string]interface{}
	running  bool
	
	// 阈值配置
	thresholds map[string]float64
	
	// 统计信息
	processedCount int64
	alertCount     int64
	errorCount     int64
	lastError      error
	lastProcess    time.Time
}

// ThresholdPluginFactory 阈值插件工厂
type ThresholdPluginFactory struct{}

// NewPluginFactory 插件工厂入口函数
func NewPluginFactory() pipeline.PluginFactory {
	return &ThresholdPluginFactory{}
}

// CreatePlugin 创建插件实例
func (f *ThresholdPluginFactory) CreatePlugin(pluginType pipeline.PluginType, config map[string]interface{}) (pipeline.PipelinePlugin, error) {
	if pluginType != pipeline.ProcessorType {
		return nil, ErrUnsupportedPluginType
	}

	plugin := &ThresholdProcessorPlugin{
		name:       "threshold_processor",
		version:    "1.0.0",
		config:     config,
		thresholds: make(map[string]float64),
	}

	return plugin, nil
}

// GetSupportedTypes 获取支持的插件类型
func (f *ThresholdPluginFactory) GetSupportedTypes() []pipeline.PluginType {
	return []pipeline.PluginType{pipeline.ProcessorType}
}

// GetPluginInfo 获取插件信息
func (f *ThresholdPluginFactory) GetPluginInfo() *pipeline.PluginInfo {
	return &pipeline.PluginInfo{
		Name:     "threshold_processor",
		Version:  "1.0.0",
		Type:     pipeline.ProcessorType,
		LoadedAt: time.Now(),
		Metadata: map[string]interface{}{
			"description": "Threshold-based alert processor",
			"author":      "DevInsight Team",
			"category":    "processor",
		},
	}
}

// ValidateConfig 验证配置
func (f *ThresholdPluginFactory) ValidateConfig(config map[string]interface{}) error {
	return nil
}

// ==================== 插件实现 ====================

// GetName 获取插件名称
func (p *ThresholdProcessorPlugin) GetName() string {
	return p.name
}

// GetType 获取插件类型
func (p *ThresholdProcessorPlugin) GetType() pipeline.PluginType {
	return pipeline.ProcessorType
}

// GetVersion 获取插件版本
func (p *ThresholdProcessorPlugin) GetVersion() string {
	return p.version
}

// Initialize 初始化插件
func (p *ThresholdProcessorPlugin) Initialize(config map[string]interface{}) error {
	p.config = config
	
	// 解析阈值配置
	if thresholds, ok := config["thresholds"]; ok {
		if thresholdMap, ok := thresholds.(map[string]interface{}); ok {
			for key, value := range thresholdMap {
				if floatValue, ok := value.(float64); ok {
					p.thresholds[key] = floatValue
				}
			}
		}
	}
	
	// 设置默认阈值
	if len(p.thresholds) == 0 {
		p.thresholds["system.memory.alloc"] = 1024 * 1024 * 1024 // 1GB
		p.thresholds["system.goroutines"] = 1000
	}

	return nil
}

// Start 启动插件
func (p *ThresholdProcessorPlugin) Start(ctx context.Context) error {
	p.running = true
	return nil
}

// Stop 停止插件
func (p *ThresholdProcessorPlugin) Stop() error {
	p.running = false
	return nil
}

// Health 健康检查
func (p *ThresholdProcessorPlugin) Health() error {
	if !p.running {
		return ErrPluginNotRunning
	}
	return nil
}

// Process 处理数据
func (p *ThresholdProcessorPlugin) Process(ctx context.Context, data *pipeline.PipelineData) (*pipeline.PipelineData, error) {
	if !p.running {
		return nil, ErrPluginNotRunning
	}

	// 更新统计信息
	defer func() {
		p.processedCount++
		p.lastProcess = time.Now()
	}()

	// 检查是否有指标数据
	if data.Metrics == nil || len(data.Metrics) == 0 {
		return data, nil
	}

	// 处理每个指标
	alerts := make([]*pipeline.Alert, 0)
	
	for _, metric := range data.Metrics {
		if threshold, exists := p.thresholds[metric.MetricKey]; exists {
			var value float64
			
			// 提取数值
			switch v := metric.ValueType.(type) {
			case *pb.MetricData_NumericValue:
				value = v.NumericValue
			default:
				continue // 跳过非数值类型
			}
			
			// 检查是否超过阈值
			if value > threshold {
				alert := &pipeline.Alert{
					ID:          fmt.Sprintf("alert_%d", time.Now().UnixNano()),
					Type:        "threshold_exceeded",
					Severity:    p.getSeverity(value, threshold),
					Status:      "active",
					Title:       fmt.Sprintf("Threshold exceeded for %s", metric.MetricKey),
					Description: fmt.Sprintf("Value %.2f exceeds threshold %.2f", value, threshold),
					Timestamp:   time.Now(),
					DeviceID:    metric.DeviceId,
					RuleID:      fmt.Sprintf("threshold_%s", metric.MetricKey),
					Attributes: map[string]interface{}{
						"metric_key": metric.MetricKey,
						"value":      value,
						"threshold":  threshold,
						"ratio":      value / threshold,
					},
				}
				
				alerts = append(alerts, alert)
				p.alertCount++
			}
		}
	}

	// 如果有告警，添加到数据中
	if len(alerts) > 0 {
		data.Alerts = alerts
		data.Type = pipeline.AlertDataType
	}

	// 清除上次错误
	p.lastError = nil
	return data, nil
}

// getSeverity 根据超出程度确定严重级别
func (p *ThresholdProcessorPlugin) getSeverity(value, threshold float64) string {
	ratio := value / threshold
	if ratio > 2.0 {
		return "critical"
	} else if ratio > 1.5 {
		return "warning"
	}
	return "info"
}

// GetConfig 获取配置
func (p *ThresholdProcessorPlugin) GetConfig() map[string]interface{} {
	return p.config
}

// UpdateConfig 更新配置
func (p *ThresholdProcessorPlugin) UpdateConfig(config map[string]interface{}) error {
	p.config = config
	return p.Initialize(config)
}

// GetInputSchema 获取输入模式
func (p *ThresholdProcessorPlugin) GetInputSchema() *pipeline.Schema {
	return &pipeline.Schema{
		Fields: map[string]pipeline.FieldSchema{
			"metrics": {
				Type:        "array",
				Description: "指标数据数组",
				Required:    true,
			},
		},
		Description: "需要包含指标数据的流水线数据",
		Version:     "1.0",
	}
}

// GetOutputSchema 获取输出模式
func (p *ThresholdProcessorPlugin) GetOutputSchema() *pipeline.Schema {
	return &pipeline.Schema{
		Fields: map[string]pipeline.FieldSchema{
			"alerts": {
				Type:        "array",
				Description: "生成的告警数组",
				Required:    false,
			},
		},
		Description: "可能包含告警信息的流水线数据",
		Version:     "1.0",
	}
}

// GetMetrics 获取插件指标
func (p *ThresholdProcessorPlugin) GetMetrics() *pipeline.PluginMetrics {
	status := "stopped"
	if p.running {
		status = "running"
	}
	
	return &pipeline.PluginMetrics{
		Name:            p.name,
		ProcessedCount:  p.processedCount,
		ErrorCount:      p.errorCount,
		AvgLatency:      0,
		LastProcessTime: p.lastProcess,
		Status:          status,
		CustomMetrics: map[string]interface{}{
			"alert_count":      p.alertCount,
			"threshold_count":  len(p.thresholds),
			"thresholds":       p.thresholds,
			"last_error":       p.getLastErrorString(),
		},
	}
}

// getLastErrorString 获取最后错误的字符串表示
func (p *ThresholdProcessorPlugin) getLastErrorString() string {
	if p.lastError != nil {
		return p.lastError.Error()
	}
	return ""
}

// 定义错误类型
var (
	ErrUnsupportedPluginType = fmt.Errorf("unsupported plugin type")
	ErrPluginNotRunning      = fmt.Errorf("plugin not running")
)

// main 函数（插件编译时需要）
func main() {
	// 插件作为共享库时不需要main函数
	// 但为了编译通过，保留空的main函数
}
