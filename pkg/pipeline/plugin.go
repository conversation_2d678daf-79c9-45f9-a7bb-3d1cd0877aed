package pipeline

import (
	"context"
	"time"
)

// PipelinePlugin - 流水线插件基础接口
type PipelinePlugin interface {
	// 基础方法
	GetName() string
	GetType() PluginType
	GetVersion() string

	// 生命周期管理
	Initialize(config map[string]interface{}) error
	Start(ctx context.Context) error
	Stop() error
	Health() error

	// 核心处理方法
	Process(ctx context.Context, data *PipelineData) (*PipelineData, error)

	// 配置管理
	GetConfig() map[string]interface{}
	UpdateConfig(config map[string]interface{}) error

	// 元数据
	GetInputSchema() *Schema
	GetOutputSchema() *Schema
	GetMetrics() *PluginMetrics
}

// CollectorPlugin - 采集器专用接口
type CollectorPlugin interface {
	PipelinePlugin

	// 采集器必须实现数据生成
	Collect(ctx context.Context) (*PipelineData, error)
	GetCollectInterval() time.Duration
	SetCollectInterval(interval time.Duration) error
}

// ProcessorPlugin - 处理器专用接口
type ProcessorPlugin interface {
	PipelinePlugin

	// 处理器可以设置并发度
	GetConcurrency() int
	SetConcurrency(concurrency int) error

	// 批处理支持
	SupportsBatch() bool
	ProcessBatch(ctx context.Context, data []*PipelineData) ([]*PipelineData, error)
}

// AnalyzerPlugin - 分析器专用接口
type AnalyzerPlugin interface {
	PipelinePlugin

	// 分析器特定方法
	Analyze(ctx context.Context, data *PipelineData) (*AnalysisResult, error)
	GetAnalysisConfig() *AnalysisConfig
	UpdateAnalysisConfig(config *AnalysisConfig) error
}

// AlerterPlugin - 告警器专用接口
type AlerterPlugin interface {
	PipelinePlugin

	// 告警器特定方法
	SendAlert(ctx context.Context, alert *Alert) error
	GetAlertConfig() *AlertConfig
	UpdateAlertConfig(config *AlertConfig) error
}

// EnricherPlugin - 数据增强插件接口
type EnricherPlugin interface {
	PipelinePlugin

	// 数据增强方法
	Enrich(ctx context.Context, data *PipelineData) (*PipelineData, error)
	GetEnrichmentRules() []EnrichmentRule
	UpdateEnrichmentRules(rules []EnrichmentRule) error
}

// FilterPlugin - 过滤器插件接口
type FilterPlugin interface {
	PipelinePlugin

	// 过滤方法
	Filter(ctx context.Context, data *PipelineData) (bool, error)
	GetFilterRules() []FilterRule
	UpdateFilterRules(rules []FilterRule) error
}

// AnalysisResult 分析结果
type AnalysisResult struct {
	ID          string                 `json:"id"`
	Timestamp   time.Time              `json:"timestamp"`
	Type        string                 `json:"type"`
	Confidence  float64                `json:"confidence"`
	Severity    string                 `json:"severity"`
	Description string                 `json:"description"`
	Anomalies   []*Anomaly             `json:"anomalies"`
	Alerts      []*Alert               `json:"alerts"`
	Metadata    map[string]interface{} `json:"metadata"`
	DeviceID    string                 `json:"device_id"`
	PluginName  string                 `json:"plugin_name"`
}

// AnalysisConfig 分析配置
type AnalysisConfig struct {
	Algorithm       string                 `json:"algorithm"`
	Sensitivity     float64                `json:"sensitivity"`
	TrainingWindow  time.Duration          `json:"training_window"`
	UpdateInterval  time.Duration          `json:"update_interval"`
	Features        []string               `json:"features"`
	Thresholds      map[string]float64     `json:"thresholds"`
	Parameters      map[string]interface{} `json:"parameters"`
	EnableLearning  bool                   `json:"enable_learning"`
	ModelPath       string                 `json:"model_path"`
}

// AlertConfig 告警配置
type AlertConfig struct {
	Channels        []AlertChannel         `json:"channels"`
	EscalationRules []EscalationRule       `json:"escalation_rules"`
	Templates       map[string]string      `json:"templates"`
	RateLimits      map[string]RateLimit   `json:"rate_limits"`
	Filters         []AlertFilter          `json:"filters"`
	Parameters      map[string]interface{} `json:"parameters"`
}

// AlertChannel 告警通道
type AlertChannel struct {
	Type       string                 `json:"type"`
	Name       string                 `json:"name"`
	Enabled    bool                   `json:"enabled"`
	Config     map[string]interface{} `json:"config"`
	Recipients []string               `json:"recipients"`
}

// EscalationRule 升级规则
type EscalationRule struct {
	Level      int           `json:"level"`
	Delay      time.Duration `json:"delay"`
	Channels   []string      `json:"channels"`
	Conditions []Condition   `json:"conditions"`
}

// Condition 条件
type Condition struct {
	Field    string      `json:"field"`
	Operator string      `json:"operator"`
	Value    interface{} `json:"value"`
}

// RateLimit 速率限制
type RateLimit struct {
	MaxCount int           `json:"max_count"`
	Window   time.Duration `json:"window"`
}

// AlertFilter 告警过滤器
type AlertFilter struct {
	Name       string      `json:"name"`
	Conditions []Condition `json:"conditions"`
	Action     string      `json:"action"` // allow, deny, modify
}

// EnrichmentRule 数据增强规则
type EnrichmentRule struct {
	Name        string                 `json:"name"`
	Conditions  []Condition            `json:"conditions"`
	Actions     []EnrichmentAction     `json:"actions"`
	Priority    int                    `json:"priority"`
	Enabled     bool                   `json:"enabled"`
	Description string                 `json:"description"`
}

// EnrichmentAction 增强动作
type EnrichmentAction struct {
	Type       string                 `json:"type"`
	Field      string                 `json:"field"`
	Value      interface{}            `json:"value"`
	Source     string                 `json:"source"`
	Parameters map[string]interface{} `json:"parameters"`
}

// FilterRule 过滤规则
type FilterRule struct {
	Name        string      `json:"name"`
	Conditions  []Condition `json:"conditions"`
	Action      string      `json:"action"` // pass, drop, modify
	Priority    int         `json:"priority"`
	Enabled     bool        `json:"enabled"`
	Description string      `json:"description"`
}
