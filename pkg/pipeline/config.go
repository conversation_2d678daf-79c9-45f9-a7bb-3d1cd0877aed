package pipeline

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"gopkg.in/yaml.v3"
)

// ConfigLoader 配置加载器
type ConfigLoader struct {
	searchPaths []string
	templates   map[string]*PipelineTemplate
}

// PipelineTemplate 流水线模板
type PipelineTemplate struct {
	ID          string                 `yaml:"id"`
	Name        string                 `yaml:"name"`
	Description string                 `yaml:"description"`
	Category    string                 `yaml:"category"`
	Tags        []string               `yaml:"tags"`
	Template    *PipelineConfig        `yaml:"template"`
	Parameters  []TemplateParameter    `yaml:"parameters"`
	Defaults    map[string]interface{} `yaml:"defaults"`
}

// TemplateParameter 模板参数
type TemplateParameter struct {
	Name         string      `yaml:"name"`
	Type         string      `yaml:"type"`
	Description  string      `yaml:"description"`
	Required     bool        `yaml:"required"`
	DefaultValue interface{} `yaml:"default_value"`
	AllowedValues []string   `yaml:"allowed_values"`
	Validation   *ParameterValidation `yaml:"validation"`
}

// ParameterValidation 参数验证
type ParameterValidation struct {
	Min    *float64 `yaml:"min"`
	Max    *float64 `yaml:"max"`
	Regex  string   `yaml:"regex"`
	Length *int     `yaml:"length"`
}

// ConfigValidator 配置验证器
type ConfigValidator struct {
	rules []ValidationRule
}

// ValidationRule 验证规则
type ValidationRule struct {
	Field       string
	Required    bool
	Type        string
	Validator   func(interface{}) error
	Description string
}

// ValidationResult 验证结果
type ValidationResult struct {
	Valid    bool
	Errors   []ValidationError
	Warnings []ValidationWarning
}

// ValidationError 验证错误
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
	Code    string `json:"code"`
}

// ValidationWarning 验证警告
type ValidationWarning struct {
	Field   string `json:"field"`
	Message string `json:"message"`
	Code    string `json:"code"`
}

// NewConfigLoader 创建配置加载器
func NewConfigLoader(searchPaths []string) *ConfigLoader {
	return &ConfigLoader{
		searchPaths: searchPaths,
		templates:   make(map[string]*PipelineTemplate),
	}
}

// LoadConfig 加载配置文件
func (cl *ConfigLoader) LoadConfig(configPath string) (*PipelineConfig, error) {
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var config PipelineConfig
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse config file: %w", err)
	}

	// 处理环境变量替换
	if err := cl.processEnvironmentVariables(&config); err != nil {
		return nil, fmt.Errorf("failed to process environment variables: %w", err)
	}

	return &config, nil
}

// LoadTemplate 加载模板
func (cl *ConfigLoader) LoadTemplate(templatePath string) (*PipelineTemplate, error) {
	data, err := os.ReadFile(templatePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read template file: %w", err)
	}

	var template PipelineTemplate
	if err := yaml.Unmarshal(data, &template); err != nil {
		return nil, fmt.Errorf("failed to parse template file: %w", err)
	}

	cl.templates[template.ID] = &template
	return &template, nil
}

// LoadTemplatesFromDir 从目录加载所有模板
func (cl *ConfigLoader) LoadTemplatesFromDir(dirPath string) error {
	return filepath.Walk(dirPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if !info.IsDir() && (strings.HasSuffix(path, ".yaml") || strings.HasSuffix(path, ".yml")) {
			if _, err := cl.LoadTemplate(path); err != nil {
				return fmt.Errorf("failed to load template %s: %w", path, err)
			}
		}

		return nil
	})
}

// GetTemplate 获取模板
func (cl *ConfigLoader) GetTemplate(templateID string) (*PipelineTemplate, error) {
	template, exists := cl.templates[templateID]
	if !exists {
		return nil, fmt.Errorf("template not found: %s", templateID)
	}
	return template, nil
}

// ListTemplates 列出所有模板
func (cl *ConfigLoader) ListTemplates() []*PipelineTemplate {
	templates := make([]*PipelineTemplate, 0, len(cl.templates))
	for _, template := range cl.templates {
		templates = append(templates, template)
	}
	return templates
}

// CreateConfigFromTemplate 从模板创建配置
func (cl *ConfigLoader) CreateConfigFromTemplate(templateID string, parameters map[string]interface{}) (*PipelineConfig, error) {
	template, err := cl.GetTemplate(templateID)
	if err != nil {
		return nil, err
	}

	// 验证参数
	if err := cl.validateTemplateParameters(template, parameters); err != nil {
		return nil, fmt.Errorf("parameter validation failed: %w", err)
	}

	// 克隆模板配置
	config := *template.Template

	// 应用参数
	if err := cl.applyTemplateParameters(&config, template, parameters); err != nil {
		return nil, fmt.Errorf("failed to apply parameters: %w", err)
	}

	return &config, nil
}

// processEnvironmentVariables 处理环境变量
func (cl *ConfigLoader) processEnvironmentVariables(config *PipelineConfig) error {
	// 处理采集器配置中的环境变量
	for key, value := range config.Collector.Config {
		if strValue, ok := value.(string); ok {
			config.Collector.Config[key] = cl.expandEnvironmentVariables(strValue)
		}
	}

	// 处理处理器配置中的环境变量
	for i := range config.Processors {
		for key, value := range config.Processors[i].Config {
			if strValue, ok := value.(string); ok {
				config.Processors[i].Config[key] = cl.expandEnvironmentVariables(strValue)
			}
		}
	}

	return nil
}

// expandEnvironmentVariables 展开环境变量
func (cl *ConfigLoader) expandEnvironmentVariables(value string) string {
	if strings.HasPrefix(value, "${") && strings.HasSuffix(value, "}") {
		envVar := value[2 : len(value)-1]
		if envValue := os.Getenv(envVar); envValue != "" {
			return envValue
		}
	}
	return value
}

// validateTemplateParameters 验证模板参数
func (cl *ConfigLoader) validateTemplateParameters(template *PipelineTemplate, parameters map[string]interface{}) error {
	for _, param := range template.Parameters {
		value, exists := parameters[param.Name]
		
		// 检查必需参数
		if param.Required && !exists {
			return fmt.Errorf("required parameter missing: %s", param.Name)
		}

		if exists {
			// 验证参数类型和值
			if err := cl.validateParameterValue(param, value); err != nil {
				return fmt.Errorf("parameter %s validation failed: %w", param.Name, err)
			}
		}
	}

	return nil
}

// validateParameterValue 验证参数值
func (cl *ConfigLoader) validateParameterValue(param TemplateParameter, value interface{}) error {
	// 类型检查
	switch param.Type {
	case "string":
		if _, ok := value.(string); !ok {
			return fmt.Errorf("expected string, got %T", value)
		}
	case "int":
		if _, ok := value.(int); !ok {
			return fmt.Errorf("expected int, got %T", value)
		}
	case "bool":
		if _, ok := value.(bool); !ok {
			return fmt.Errorf("expected bool, got %T", value)
		}
	case "float":
		if _, ok := value.(float64); !ok {
			return fmt.Errorf("expected float, got %T", value)
		}
	}

	// 允许值检查
	if len(param.AllowedValues) > 0 {
		strValue := fmt.Sprintf("%v", value)
		found := false
		for _, allowed := range param.AllowedValues {
			if strValue == allowed {
				found = true
				break
			}
		}
		if !found {
			return fmt.Errorf("value %v not in allowed values: %v", value, param.AllowedValues)
		}
	}

	// 验证规则检查
	if param.Validation != nil {
		if err := cl.validateParameterConstraints(param.Validation, value); err != nil {
			return err
		}
	}

	return nil
}

// validateParameterConstraints 验证参数约束
func (cl *ConfigLoader) validateParameterConstraints(validation *ParameterValidation, value interface{}) error {
	// 数值范围检查
	if validation.Min != nil || validation.Max != nil {
		var numValue float64
		switch v := value.(type) {
		case int:
			numValue = float64(v)
		case float64:
			numValue = v
		default:
			return fmt.Errorf("min/max validation only applies to numeric values")
		}

		if validation.Min != nil && numValue < *validation.Min {
			return fmt.Errorf("value %v is less than minimum %v", numValue, *validation.Min)
		}

		if validation.Max != nil && numValue > *validation.Max {
			return fmt.Errorf("value %v is greater than maximum %v", numValue, *validation.Max)
		}
	}

	// 长度检查
	if validation.Length != nil {
		if strValue, ok := value.(string); ok {
			if len(strValue) != *validation.Length {
				return fmt.Errorf("string length %d does not match required length %d", len(strValue), *validation.Length)
			}
		}
	}

	return nil
}

// applyTemplateParameters 应用模板参数
func (cl *ConfigLoader) applyTemplateParameters(config *PipelineConfig, template *PipelineTemplate, parameters map[string]interface{}) error {
	// 合并默认值和用户参数
	allParams := make(map[string]interface{})
	
	// 先应用默认值
	for key, value := range template.Defaults {
		allParams[key] = value
	}
	
	// 再应用用户参数
	for key, value := range parameters {
		allParams[key] = value
	}

	// 应用参数到配置
	// 这里可以实现更复杂的参数替换逻辑
	// 简化版本：直接替换已知的参数
	
	return nil
}

// NewConfigValidator 创建配置验证器
func NewConfigValidator() *ConfigValidator {
	validator := &ConfigValidator{
		rules: make([]ValidationRule, 0),
	}
	
	// 添加基础验证规则
	validator.addBasicRules()
	
	return validator
}

// addBasicRules 添加基础验证规则
func (cv *ConfigValidator) addBasicRules() {
	cv.rules = append(cv.rules, []ValidationRule{
		{
			Field:       "collector.name",
			Required:    true,
			Type:        "string",
			Description: "Collector name is required",
		},
		{
			Field:       "collector.type",
			Required:    true,
			Type:        "string",
			Description: "Collector type is required",
		},
		{
			Field:       "buffer_size",
			Required:    false,
			Type:        "int",
			Validator: func(value interface{}) error {
				if v, ok := value.(int); ok && v <= 0 {
					return fmt.Errorf("buffer_size must be positive")
				}
				return nil
			},
			Description: "Buffer size must be positive",
		},
		{
			Field:       "worker_count",
			Required:    false,
			Type:        "int",
			Validator: func(value interface{}) error {
				if v, ok := value.(int); ok && v <= 0 {
					return fmt.Errorf("worker_count must be positive")
				}
				return nil
			},
			Description: "Worker count must be positive",
		},
	}...)
}

// ValidateConfig 验证配置
func (cv *ConfigValidator) ValidateConfig(config *PipelineConfig) *ValidationResult {
	result := &ValidationResult{
		Valid:    true,
		Errors:   make([]ValidationError, 0),
		Warnings: make([]ValidationWarning, 0),
	}

	// 基础验证
	if config.Collector.Name == "" {
		result.Errors = append(result.Errors, ValidationError{
			Field:   "collector.name",
			Message: "Collector name is required",
			Code:    "REQUIRED_FIELD",
		})
		result.Valid = false
	}

	if config.Collector.Type == "" {
		result.Errors = append(result.Errors, ValidationError{
			Field:   "collector.type",
			Message: "Collector type is required",
			Code:    "REQUIRED_FIELD",
		})
		result.Valid = false
	}

	// 数值验证
	if config.BufferSize <= 0 {
		result.Warnings = append(result.Warnings, ValidationWarning{
			Field:   "buffer_size",
			Message: "Buffer size should be positive, using default value",
			Code:    "DEFAULT_VALUE",
		})
	}

	if config.WorkerCount <= 0 {
		result.Warnings = append(result.Warnings, ValidationWarning{
			Field:   "worker_count",
			Message: "Worker count should be positive, using default value",
			Code:    "DEFAULT_VALUE",
		})
	}

	// 超时验证
	if config.Timeout < time.Second {
		result.Warnings = append(result.Warnings, ValidationWarning{
			Field:   "timeout",
			Message: "Timeout is very short, may cause issues",
			Code:    "PERFORMANCE_WARNING",
		})
	}

	return result
}
