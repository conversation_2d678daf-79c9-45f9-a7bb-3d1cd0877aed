package pipeline

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
)

// PipelineManager - 流水线管理器
type PipelineManager struct {
	// 流水线管理
	pipelines       map[string]*Pipeline
	pipelineConfigs map[string]*PipelineConfig

	// 插件管理
	pluginRegistry *PluginRegistry
	pluginFactory  *PluginFactory

	// 配置管理
	configManager *ConfigManager
	configWatcher *ConfigWatcher

	// 事件和监控
	eventBus         *EventBus
	metricsCollector *MetricsCollector

	// 运行时
	ctx    context.Context
	cancel context.CancelFunc
	logger *zap.Logger
	mutex  sync.RWMutex
}

// PluginRegistry 插件注册表
type PluginRegistry struct {
	plugins map[string]PluginInfo
	mutex   sync.RWMutex
}

// PluginInfo 插件信息
type PluginInfo struct {
	Name     string
	Version  string
	Type     PluginType
	Path     string
	Factory  PluginFactory
	Metadata map[string]any
	LoadedAt time.Time
	LastUsed time.Time
}

// PluginFactory 插件工厂
type PluginFactory interface {
	CreatePlugin(pluginType PluginType, config map[string]any) (PipelinePlugin, error)
	GetSupportedTypes() []PluginType
	GetPluginInfo() *PluginInfo
	ValidateConfig(config map[string]any) error
}

// ConfigManager 配置管理器
type ConfigManager struct {
	configs map[string]*PipelineConfig
	mutex   sync.RWMutex
}

// ConfigWatcher 配置监听器
type ConfigWatcher struct {
	watchPaths []string
	callbacks  []ConfigChangeCallback
	mutex      sync.RWMutex
}

// ConfigChangeCallback 配置变更回调
type ConfigChangeCallback func(configPath string, config *PipelineConfig) error

// EventBus 事件总线
type EventBus struct {
	subscribers map[string][]EventHandler
	mutex       sync.RWMutex
}

// EventHandler 事件处理器
type EventHandler func(event *PipelineEvent) error

// PipelineEvent 流水线事件
type PipelineEvent struct {
	Type      string         `json:"type"`
	Source    string         `json:"source"`
	Timestamp time.Time      `json:"timestamp"`
	Data      map[string]any `json:"data"`
	Pipeline  *Pipeline      `json:"pipeline,omitempty"`
}

// MetricsCollector 指标收集器
type MetricsCollector struct {
	collectors map[string]MetricCollector
	interval   time.Duration
	mutex      sync.RWMutex
}

// MetricCollector 指标收集器接口
type MetricCollector interface {
	Collect() (map[string]any, error)
	GetName() string
}

// NewPipelineManager 创建新的流水线管理器
func NewPipelineManager(logger *zap.Logger) *PipelineManager {
	ctx, cancel := context.WithCancel(context.Background())

	return &PipelineManager{
		pipelines:        make(map[string]*Pipeline),
		pipelineConfigs:  make(map[string]*PipelineConfig),
		pluginRegistry:   NewPluginRegistry(),
		configManager:    NewConfigManager(),
		configWatcher:    NewConfigWatcher(),
		eventBus:         NewEventBus(),
		metricsCollector: NewMetricsCollector(time.Minute),
		ctx:              ctx,
		cancel:           cancel,
		logger:           logger,
	}
}

// NewPluginRegistry 创建插件注册表
func NewPluginRegistry() *PluginRegistry {
	return &PluginRegistry{
		plugins: make(map[string]PluginInfo),
	}
}

// NewConfigManager 创建配置管理器
func NewConfigManager() *ConfigManager {
	return &ConfigManager{
		configs: make(map[string]*PipelineConfig),
	}
}

// NewConfigWatcher 创建配置监听器
func NewConfigWatcher() *ConfigWatcher {
	return &ConfigWatcher{
		watchPaths: make([]string, 0),
		callbacks:  make([]ConfigChangeCallback, 0),
	}
}

// NewEventBus 创建事件总线
func NewEventBus() *EventBus {
	return &EventBus{
		subscribers: make(map[string][]EventHandler),
	}
}

// NewMetricsCollector 创建指标收集器
func NewMetricsCollector(interval time.Duration) *MetricsCollector {
	return &MetricsCollector{
		collectors: make(map[string]MetricCollector),
		interval:   interval,
	}
}

// CreatePipeline 创建流水线
func (pm *PipelineManager) CreatePipeline(config *PipelineConfig) (*Pipeline, error) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	// 1. 验证配置
	if err := pm.validateConfig(config); err != nil {
		return nil, fmt.Errorf("invalid config: %w", err)
	}

	// 2. 创建采集器插件
	collector, err := pm.createCollectorPlugin(config.Collector)
	if err != nil {
		return nil, fmt.Errorf("failed to create collector: %w", err)
	}

	// 3. 创建处理器插件链
	processors, err := pm.createProcessorPlugins(config.Processors)
	if err != nil {
		return nil, fmt.Errorf("failed to create processors: %w", err)
	}

	// 4. 创建流水线实例
	pipeline := &Pipeline{
		ID:         generatePipelineID(),
		Name:       config.Collector.Name,
		Collector:  collector,
		Processors: processors,
		Config:     config,
		State:      StateIdle,
		dataCh:     make(chan *PipelineData, config.BufferSize),
		errorCh:    make(chan error, 100),
		stopCh:     make(chan struct{}),
		Metrics:    NewPipelineMetrics(),
		logger:     pm.logger.With(zap.String("pipeline", config.Collector.Name)),
	}

	// 5. 注册流水线
	pm.pipelines[pipeline.ID] = pipeline

	// 6. 发布事件
	pm.publishEvent(&PipelineEvent{
		Type:      "pipeline_created",
		Source:    "pipeline_manager",
		Timestamp: time.Now(),
		Pipeline:  pipeline,
	})

	return pipeline, nil
}

// validateConfig 验证配置
func (pm *PipelineManager) validateConfig(config *PipelineConfig) error {
	if config == nil {
		return fmt.Errorf("config is nil")
	}

	if config.Collector.Name == "" {
		return fmt.Errorf("collector name is required")
	}

	if config.Collector.Type == "" {
		return fmt.Errorf("collector type is required")
	}

	if config.BufferSize <= 0 {
		config.BufferSize = 1000 // 默认值
	}

	if config.WorkerCount <= 0 {
		config.WorkerCount = 2 // 默认值
	}

	return nil
}

// createCollectorPlugin 创建采集器插件
func (pm *PipelineManager) createCollectorPlugin(config CollectorConfig) (CollectorPlugin, error) {
	// 从插件注册表获取插件工厂
	factory, err := pm.pluginRegistry.GetPluginFactory(config.Type)
	if err != nil {
		return nil, fmt.Errorf("plugin factory not found for type %s: %w", config.Type, err)
	}

	// 创建插件实例
	plugin, err := factory.CreatePlugin(CollectorType, config.Config)
	if err != nil {
		return nil, fmt.Errorf("failed to create plugin: %w", err)
	}

	// 类型断言
	collector, ok := plugin.(CollectorPlugin)
	if !ok {
		return nil, fmt.Errorf("plugin is not a collector")
	}

	return collector, nil
}

// createProcessorPlugins 创建处理器插件链
func (pm *PipelineManager) createProcessorPlugins(configs []ProcessorConfig) ([]PipelinePlugin, error) {
	processors := make([]PipelinePlugin, 0, len(configs))

	for _, config := range configs {
		if !config.Enabled {
			continue
		}

		factory, err := pm.pluginRegistry.GetPluginFactory(config.Type)
		if err != nil {
			return nil, fmt.Errorf("plugin factory not found for type %s: %w", config.Type, err)
		}

		plugin, err := factory.CreatePlugin(ProcessorType, config.Config)
		if err != nil {
			return nil, fmt.Errorf("failed to create processor %s: %w", config.Name, err)
		}

		processors = append(processors, plugin)
	}

	return processors, nil
}

// generatePipelineID 生成流水线ID
func generatePipelineID() string {
	return fmt.Sprintf("pipeline_%d", time.Now().UnixNano())
}

// publishEvent 发布事件
func (pm *PipelineManager) publishEvent(event *PipelineEvent) {
	if pm.eventBus != nil {
		pm.eventBus.Publish(event)
	}
}

// GetPluginFactory 获取插件工厂
func (pr *PluginRegistry) GetPluginFactory(pluginType string) (PluginFactory, error) {
	pr.mutex.RLock()
	defer pr.mutex.RUnlock()

	for _, info := range pr.plugins {
		if string(info.Type) == pluginType {
			return info.Factory, nil
		}
	}

	return nil, fmt.Errorf("plugin factory not found for type: %s", pluginType)
}

// Publish 发布事件
func (eb *EventBus) Publish(event *PipelineEvent) {
	eb.mutex.RLock()
	defer eb.mutex.RUnlock()

	if handlers, exists := eb.subscribers[event.Type]; exists {
		for _, handler := range handlers {
			go func(h EventHandler) {
				if err := h(event); err != nil {
					// 记录错误但不阻塞
				}
			}(handler)
		}
	}
}

// StartPipeline 启动流水线
func (pm *PipelineManager) StartPipeline(pipelineID string) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	pipeline, exists := pm.pipelines[pipelineID]
	if !exists {
		return fmt.Errorf("pipeline not found: %s", pipelineID)
	}

	if pipeline.State == StateRunning {
		return fmt.Errorf("pipeline already running: %s", pipelineID)
	}

	// 创建执行引擎
	engine := NewExecutionEngine(pipeline, pm.logger)

	// 启动流水线
	if err := engine.Start(pm.ctx); err != nil {
		return fmt.Errorf("failed to start pipeline: %w", err)
	}

	// 发布事件
	pm.publishEvent(&PipelineEvent{
		Type:      "pipeline_started",
		Source:    "pipeline_manager",
		Timestamp: time.Now(),
		Pipeline:  pipeline,
	})

	return nil
}

// StopPipeline 停止流水线
func (pm *PipelineManager) StopPipeline(pipelineID string) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	pipeline, exists := pm.pipelines[pipelineID]
	if !exists {
		return fmt.Errorf("pipeline not found: %s", pipelineID)
	}

	if pipeline.State != StateRunning {
		return fmt.Errorf("pipeline not running: %s", pipelineID)
	}

	// 停止流水线
	pipeline.State = StateStopping
	close(pipeline.stopCh)

	// 发布事件
	pm.publishEvent(&PipelineEvent{
		Type:      "pipeline_stopped",
		Source:    "pipeline_manager",
		Timestamp: time.Now(),
		Pipeline:  pipeline,
	})

	return nil
}

// GetPipeline 获取流水线
func (pm *PipelineManager) GetPipeline(pipelineID string) (*Pipeline, error) {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	pipeline, exists := pm.pipelines[pipelineID]
	if !exists {
		return nil, fmt.Errorf("pipeline not found: %s", pipelineID)
	}

	return pipeline, nil
}

// ListPipelines 列出所有流水线
func (pm *PipelineManager) ListPipelines() []*Pipeline {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	pipelines := make([]*Pipeline, 0, len(pm.pipelines))
	for _, pipeline := range pm.pipelines {
		pipelines = append(pipelines, pipeline)
	}

	return pipelines
}

// DeletePipeline 删除流水线
func (pm *PipelineManager) DeletePipeline(pipelineID string) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	pipeline, exists := pm.pipelines[pipelineID]
	if !exists {
		return fmt.Errorf("pipeline not found: %s", pipelineID)
	}

	// 如果流水线正在运行，先停止它
	if pipeline.State == StateRunning {
		if err := pm.StopPipeline(pipelineID); err != nil {
			return fmt.Errorf("failed to stop pipeline before deletion: %w", err)
		}
	}

	// 删除流水线
	delete(pm.pipelines, pipelineID)

	// 发布事件
	pm.publishEvent(&PipelineEvent{
		Type:      "pipeline_deleted",
		Source:    "pipeline_manager",
		Timestamp: time.Now(),
		Pipeline:  pipeline,
	})

	return nil
}

// UpdatePipelineConfig 更新流水线配置
func (pm *PipelineManager) UpdatePipelineConfig(pipelineID string, config *PipelineConfig) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	pipeline, exists := pm.pipelines[pipelineID]
	if !exists {
		return fmt.Errorf("pipeline not found: %s", pipelineID)
	}

	// 验证新配置
	if err := pm.validateConfig(config); err != nil {
		return fmt.Errorf("invalid config: %w", err)
	}

	// 如果流水线正在运行，需要重启
	wasRunning := pipeline.State == StateRunning
	if wasRunning {
		if err := pm.StopPipeline(pipelineID); err != nil {
			return fmt.Errorf("failed to stop pipeline for config update: %w", err)
		}
	}

	// 更新配置
	pipeline.Config = config

	// 如果之前在运行，重新启动
	if wasRunning {
		if err := pm.StartPipeline(pipelineID); err != nil {
			return fmt.Errorf("failed to restart pipeline after config update: %w", err)
		}
	}

	// 发布事件
	pm.publishEvent(&PipelineEvent{
		Type:      "pipeline_config_updated",
		Source:    "pipeline_manager",
		Timestamp: time.Now(),
		Pipeline:  pipeline,
	})

	return nil
}

// RegisterPlugin 注册插件
func (pm *PipelineManager) RegisterPlugin(info *PluginInfo, factory PluginFactory) error {
	return pm.pluginRegistry.RegisterPlugin(info, factory)
}

// RegisterPlugin 注册插件到注册表
func (pr *PluginRegistry) RegisterPlugin(info *PluginInfo, factory PluginFactory) error {
	pr.mutex.Lock()
	defer pr.mutex.Unlock()

	key := fmt.Sprintf("%s:%s", info.Name, info.Version)
	if _, exists := pr.plugins[key]; exists {
		return fmt.Errorf("plugin already registered: %s", key)
	}

	pr.plugins[key] = *info
	return nil
}

// GetPluginInfo 获取插件信息
func (pr *PluginRegistry) GetPluginInfo(name, version string) (*PluginInfo, error) {
	pr.mutex.RLock()
	defer pr.mutex.RUnlock()

	key := fmt.Sprintf("%s:%s", name, version)
	if info, exists := pr.plugins[key]; exists {
		return &info, nil
	}

	return nil, fmt.Errorf("plugin not found: %s", key)
}

// ListPlugins 列出所有插件
func (pr *PluginRegistry) ListPlugins() []PluginInfo {
	pr.mutex.RLock()
	defer pr.mutex.RUnlock()

	plugins := make([]PluginInfo, 0, len(pr.plugins))
	for _, info := range pr.plugins {
		plugins = append(plugins, info)
	}

	return plugins
}

// Shutdown 关闭管理器
func (pm *PipelineManager) Shutdown() error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	pm.logger.Info("Shutting down pipeline manager")

	// 停止所有运行中的流水线
	for pipelineID, pipeline := range pm.pipelines {
		if pipeline.State == StateRunning {
			if err := pm.StopPipeline(pipelineID); err != nil {
				pm.logger.Error("Failed to stop pipeline during shutdown",
					zap.String("pipeline_id", pipelineID),
					zap.Error(err))
			}
		}
	}

	// 取消上下文
	pm.cancel()

	pm.logger.Info("Pipeline manager shutdown complete")
	return nil
}
