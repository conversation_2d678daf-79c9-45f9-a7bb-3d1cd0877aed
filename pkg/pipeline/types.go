package pipeline

import (
	"time"

	pb "aiops/pkg/proto"
)

// DataType 数据类型枚举
type DataType string

const (
	MetricDataType  DataType = "metric"
	LogDataType     DataType = "log"
	EventDataType   DataType = "event"
	AnomalyDataType DataType = "anomaly"
	AlertDataType   DataType = "alert"
	MixedDataType   DataType = "mixed"
)

// PipelineData - 流水线统一数据格式
type PipelineData struct {
	// 基础信息
	ID        string    `json:"id"`        // 数据唯一标识
	Type      DataType  `json:"type"`      // 数据类型
	Source    string    `json:"source"`    // 数据源
	DeviceID  string    `json:"device_id"` // 设备ID
	Timestamp time.Time `json:"timestamp"` // 时间戳

	// 数据载荷
	Metrics   []*pb.MetricData `json:"metrics,omitempty"`   // 指标数据
	Logs      []*pb.LogEntry   `json:"logs,omitempty"`      // 日志数据
	Events    []*Event         `json:"events,omitempty"`    // 事件数据
	Anomalies []*Anomaly       `json:"anomalies,omitempty"` // 异常信息
	Alerts    []*Alert         `json:"alerts,omitempty"`    // 告警信息

	// 扩展信息
	Metadata map[string]interface{} `json:"metadata"` // 元数据
	Context  map[string]interface{} `json:"context"`  // 上下文信息
	Tags     map[string]string      `json:"tags"`     // 标签信息

	// 流水线信息
	PipelineID  string   `json:"pipeline_id"`  // 所属流水线
	Stage       string   `json:"stage"`        // 当前处理阶段
	ProcessedBy []string `json:"processed_by"` // 已处理的插件列表
}

// Event 事件数据结构
type Event struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	Source      string                 `json:"source"`
	Timestamp   time.Time              `json:"timestamp"`
	Severity    string                 `json:"severity"`
	Message     string                 `json:"message"`
	Attributes  map[string]interface{} `json:"attributes"`
	DeviceID    string                 `json:"device_id"`
	ServiceName string                 `json:"service_name"`
}

// Anomaly 异常数据结构
type Anomaly struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	Severity    string                 `json:"severity"`
	Score       float64                `json:"score"`
	Threshold   float64                `json:"threshold"`
	Description string                 `json:"description"`
	Timestamp   time.Time              `json:"timestamp"`
	DeviceID    string                 `json:"device_id"`
	MetricName  string                 `json:"metric_name"`
	Value       float64                `json:"value"`
	Expected    float64                `json:"expected"`
	Attributes  map[string]interface{} `json:"attributes"`
}

// Alert 告警数据结构
type Alert struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	Severity    string                 `json:"severity"`
	Status      string                 `json:"status"`
	Title       string                 `json:"title"`
	Description string                 `json:"description"`
	Timestamp   time.Time              `json:"timestamp"`
	DeviceID    string                 `json:"device_id"`
	RuleID      string                 `json:"rule_id"`
	Attributes  map[string]interface{} `json:"attributes"`
	Actions     []AlertAction          `json:"actions"`
}

// AlertAction 告警动作
type AlertAction struct {
	Type       string                 `json:"type"`
	Target     string                 `json:"target"`
	Parameters map[string]interface{} `json:"parameters"`
	Status     string                 `json:"status"`
	Timestamp  time.Time              `json:"timestamp"`
}

// PluginType 插件类型
type PluginType string

const (
	CollectorType PluginType = "collector" // 采集器插件
	ProcessorType PluginType = "processor" // 处理器插件
	AnalyzerType  PluginType = "analyzer"  // 分析器插件
	AlerterType   PluginType = "alerter"   // 告警器插件
	EnricherType  PluginType = "enricher"  // 数据增强插件
	FilterType    PluginType = "filter"    // 过滤器插件
)

// PipelineState 流水线状态
type PipelineState string

const (
	StateIdle     PipelineState = "idle"
	StateStarting PipelineState = "starting"
	StateRunning  PipelineState = "running"
	StateStopping PipelineState = "stopping"
	StateStopped  PipelineState = "stopped"
	StateError    PipelineState = "error"
)

// Schema 数据模式定义
type Schema struct {
	Fields      map[string]FieldSchema `json:"fields"`
	Required    []string               `json:"required"`
	Description string                 `json:"description"`
	Version     string                 `json:"version"`
}

// FieldSchema 字段模式
type FieldSchema struct {
	Type        string      `json:"type"`
	Description string      `json:"description"`
	Required    bool        `json:"required"`
	Default     interface{} `json:"default,omitempty"`
	Validation  *Validation `json:"validation,omitempty"`
}

// Validation 验证规则
type Validation struct {
	Min    *float64 `json:"min,omitempty"`
	Max    *float64 `json:"max,omitempty"`
	Regex  string   `json:"regex,omitempty"`
	Enum   []string `json:"enum,omitempty"`
	Length *int     `json:"length,omitempty"`
}

// PluginMetrics 插件指标
type PluginMetrics struct {
	Name            string                 `json:"name"`
	ProcessedCount  int64                  `json:"processed_count"`
	ErrorCount      int64                  `json:"error_count"`
	AvgLatency      time.Duration          `json:"avg_latency"`
	LastProcessTime time.Time              `json:"last_process_time"`
	Status          string                 `json:"status"`
	CustomMetrics   map[string]interface{} `json:"custom_metrics"`
}
