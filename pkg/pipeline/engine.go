package pipeline

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
)

// ExecutionEngine 流水线执行引擎
type ExecutionEngine struct {
	pipeline *Pipeline
	logger   *zap.Logger

	// 执行状态
	running    bool
	workers    []*Worker
	workerPool chan *Worker

	// 数据处理
	dataProcessor *DataProcessor
	errorHandler  *ErrorHandler

	// 监控
	metricsCollector *PipelineMetricsCollector

	// 同步
	mutex sync.RWMutex
	wg    sync.WaitGroup
}

// Worker 工作协程
type Worker struct {
	ID       int
	pipeline *Pipeline
	logger   *zap.Logger
	ctx      context.Context
	cancel   context.CancelFunc
}

// DataProcessor 数据处理器
type DataProcessor struct {
	pipeline *Pipeline
	logger   *zap.Logger
}

// ErrorHandler 错误处理器
type ErrorHandler struct {
	config *ErrorHandlingConfig
	logger *zap.Logger

	// 熔断器状态
	circuitBreaker *CircuitBreaker

	// 死信队列
	deadLetterQueue chan *PipelineData
}

// CircuitBreaker 熔断器
type CircuitBreaker struct {
	config       *CircuitBreakerConfig
	state        CircuitState
	failureCount int
	lastFailure  time.Time
	mutex        sync.RWMutex
}

// CircuitState 熔断器状态
type CircuitState int

const (
	CircuitClosed CircuitState = iota
	CircuitOpen
	CircuitHalfOpen
)

// PipelineMetricsCollector 流水线指标收集器
type PipelineMetricsCollector struct {
	pipeline *Pipeline
	logger   *zap.Logger
	interval time.Duration
	stopCh   chan struct{}
}

// NewExecutionEngine 创建执行引擎
func NewExecutionEngine(pipeline *Pipeline, logger *zap.Logger) *ExecutionEngine {
	return &ExecutionEngine{
		pipeline:         pipeline,
		logger:           logger,
		workers:          make([]*Worker, 0),
		workerPool:       make(chan *Worker, pipeline.Config.WorkerCount),
		dataProcessor:    NewDataProcessor(pipeline, logger),
		errorHandler:     NewErrorHandler(&pipeline.Config.ErrorHandling, logger),
		metricsCollector: NewPipelineMetricsCollector(pipeline, logger, pipeline.Config.MetricsInterval),
	}
}

// NewDataProcessor 创建数据处理器
func NewDataProcessor(pipeline *Pipeline, logger *zap.Logger) *DataProcessor {
	return &DataProcessor{
		pipeline: pipeline,
		logger:   logger,
	}
}

// NewErrorHandler 创建错误处理器
func NewErrorHandler(config *ErrorHandlingConfig, logger *zap.Logger) *ErrorHandler {
	eh := &ErrorHandler{
		config:          config,
		logger:          logger,
		deadLetterQueue: make(chan *PipelineData, 1000),
	}

	if config.CircuitBreaker.Enabled {
		eh.circuitBreaker = &CircuitBreaker{
			config: &config.CircuitBreaker,
			state:  CircuitClosed,
		}
	}

	return eh
}

// NewPipelineMetricsCollector 创建指标收集器
func NewPipelineMetricsCollector(pipeline *Pipeline, logger *zap.Logger, interval time.Duration) *PipelineMetricsCollector {
	return &PipelineMetricsCollector{
		pipeline: pipeline,
		logger:   logger,
		interval: interval,
		stopCh:   make(chan struct{}),
	}
}

// Start 启动执行引擎
func (ee *ExecutionEngine) Start(ctx context.Context) error {
	ee.mutex.Lock()
	defer ee.mutex.Unlock()

	if ee.running {
		return fmt.Errorf("execution engine already running")
	}

	ee.logger.Info("Starting pipeline execution engine",
		zap.String("pipeline_id", ee.pipeline.ID),
		zap.Int("worker_count", ee.pipeline.Config.WorkerCount))

	// 启动采集器
	if err := ee.startCollector(ctx); err != nil {
		return fmt.Errorf("failed to start collector: %w", err)
	}

	// 启动处理器
	if err := ee.startProcessors(ctx); err != nil {
		return fmt.Errorf("failed to start processors: %w", err)
	}

	// 启动工作协程
	if err := ee.startWorkers(ctx); err != nil {
		return fmt.Errorf("failed to start workers: %w", err)
	}

	// 启动数据流处理
	ee.wg.Add(1)
	go ee.runDataFlow(ctx)

	// 启动错误处理
	ee.wg.Add(1)
	go ee.runErrorHandling(ctx)

	// 启动指标收集
	if ee.pipeline.Config.EnableMetrics {
		ee.wg.Add(1)
		go ee.runMetricsCollection(ctx)
	}

	ee.running = true
	ee.pipeline.State = StateRunning
	ee.pipeline.StartTime = time.Now()

	ee.logger.Info("Pipeline execution engine started",
		zap.String("pipeline_id", ee.pipeline.ID))

	return nil
}

// Stop 停止执行引擎
func (ee *ExecutionEngine) Stop() error {
	ee.mutex.Lock()
	defer ee.mutex.Unlock()

	if !ee.running {
		return fmt.Errorf("execution engine not running")
	}

	ee.logger.Info("Stopping pipeline execution engine",
		zap.String("pipeline_id", ee.pipeline.ID))

	// 发送停止信号
	close(ee.pipeline.stopCh)

	// 停止采集器
	if err := ee.pipeline.Collector.Stop(); err != nil {
		ee.logger.Error("Failed to stop collector", zap.Error(err))
	}

	// 停止处理器
	for _, processor := range ee.pipeline.Processors {
		if err := processor.Stop(); err != nil {
			ee.logger.Error("Failed to stop processor",
				zap.String("processor", processor.GetName()),
				zap.Error(err))
		}
	}

	// 停止工作协程
	ee.stopWorkers()

	// 停止指标收集
	close(ee.metricsCollector.stopCh)

	// 等待所有协程结束
	ee.wg.Wait()

	ee.running = false
	ee.pipeline.State = StateStopped
	ee.pipeline.StopTime = time.Now()

	ee.logger.Info("Pipeline execution engine stopped",
		zap.String("pipeline_id", ee.pipeline.ID))

	return nil
}

// startCollector 启动采集器
func (ee *ExecutionEngine) startCollector(ctx context.Context) error {
	if err := ee.pipeline.Collector.Initialize(ee.pipeline.Config.Collector.Config); err != nil {
		return fmt.Errorf("failed to initialize collector: %w", err)
	}

	if err := ee.pipeline.Collector.Start(ctx); err != nil {
		return fmt.Errorf("failed to start collector: %w", err)
	}

	return nil
}

// startProcessors 启动处理器
func (ee *ExecutionEngine) startProcessors(ctx context.Context) error {
	for _, processor := range ee.pipeline.Processors {
		if err := processor.Start(ctx); err != nil {
			return fmt.Errorf("failed to start processor %s: %w", processor.GetName(), err)
		}
	}
	return nil
}

// startWorkers 启动工作协程
func (ee *ExecutionEngine) startWorkers(ctx context.Context) error {
	for i := 0; i < ee.pipeline.Config.WorkerCount; i++ {
		worker := &Worker{
			ID:       i,
			pipeline: ee.pipeline,
			logger:   ee.logger.With(zap.Int("worker_id", i)),
		}
		worker.ctx, worker.cancel = context.WithCancel(ctx)

		ee.workers = append(ee.workers, worker)
		ee.workerPool <- worker

		ee.wg.Add(1)
		go ee.runWorker(worker)
	}
	return nil
}

// stopWorkers 停止工作协程
func (ee *ExecutionEngine) stopWorkers() {
	for _, worker := range ee.workers {
		worker.cancel()
	}
}

// runDataFlow 运行数据流
func (ee *ExecutionEngine) runDataFlow(ctx context.Context) {
	defer ee.wg.Done()

	ticker := time.NewTicker(ee.pipeline.Collector.GetCollectInterval())
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ee.pipeline.stopCh:
			return
		case <-ticker.C:
			// 执行数据采集
			data, err := ee.pipeline.Collector.Collect(ctx)
			if err != nil {
				ee.pipeline.errorCh <- fmt.Errorf("collection failed: %w", err)
				continue
			}

			if data != nil {
				// 设置流水线信息
				data.PipelineID = ee.pipeline.ID
				data.Stage = "collected"
				data.ProcessedBy = []string{ee.pipeline.Collector.GetName()}

				// 发送到处理通道
				select {
				case ee.pipeline.dataCh <- data:
					ee.pipeline.Metrics.IncrementCollected()
				case <-ctx.Done():
					return
				default:
					ee.pipeline.Metrics.IncrementDropped()
					ee.logger.Warn("Data channel full, dropping data")
				}
			}
		}
	}
}

// runWorker 运行工作协程
func (ee *ExecutionEngine) runWorker(worker *Worker) {
	defer ee.wg.Done()

	for {
		select {
		case <-worker.ctx.Done():
			return
		case <-ee.pipeline.stopCh:
			return
		case data := <-ee.pipeline.dataCh:
			// 处理数据
			if err := ee.dataProcessor.ProcessData(worker.ctx, data); err != nil {
				ee.pipeline.errorCh <- fmt.Errorf("processing failed: %w", err)
				ee.pipeline.Metrics.IncrementErrors()
			} else {
				ee.pipeline.Metrics.IncrementProcessed()
			}
		}
	}
}

// runErrorHandling 运行错误处理
func (ee *ExecutionEngine) runErrorHandling(ctx context.Context) {
	defer ee.wg.Done()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ee.pipeline.stopCh:
			return
		case err := <-ee.pipeline.errorCh:
			ee.errorHandler.HandleError(err)
		}
	}
}

// runMetricsCollection 运行指标收集
func (ee *ExecutionEngine) runMetricsCollection(ctx context.Context) {
	defer ee.wg.Done()

	ticker := time.NewTicker(ee.metricsCollector.interval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ee.pipeline.stopCh:
			return
		case <-ee.metricsCollector.stopCh:
			return
		case <-ticker.C:
			ee.metricsCollector.Collect()
		}
	}
}

// ProcessData 处理数据
func (dp *DataProcessor) ProcessData(ctx context.Context, data *PipelineData) error {
	currentData := data

	// 依次通过每个处理器
	for i, processor := range dp.pipeline.Processors {
		start := time.Now()

		// 更新处理阶段信息
		currentData.Stage = processor.GetName()

		// 执行处理
		result, err := processor.Process(ctx, currentData)
		if err != nil {
			return fmt.Errorf("processor %s failed: %w", processor.GetName(), err)
		}

		// 更新已处理插件列表
		if result != nil {
			result.ProcessedBy = append(result.ProcessedBy, processor.GetName())
			currentData = result
		}

		// 记录处理时间
		duration := time.Since(start)
		dp.pipeline.Metrics.RecordProcessorLatency(processor.GetName(), duration)

		dp.logger.Debug("Processor completed",
			zap.String("processor", processor.GetName()),
			zap.Int("stage", i),
			zap.Duration("duration", duration))
	}

	// 最终数据处理完成
	currentData.Stage = "completed"

	return nil
}

// HandleError 处理错误
func (eh *ErrorHandler) HandleError(err error) {
	eh.logger.Error("Pipeline error", zap.Error(err))

	// 检查熔断器
	if eh.circuitBreaker != nil {
		if eh.circuitBreaker.ShouldTrip() {
			eh.logger.Warn("Circuit breaker tripped")
			return
		}
	}

	// 根据策略处理错误
	switch eh.config.Strategy {
	case "ignore":
		// 忽略错误
		return
	case "retry":
		// 重试逻辑
		eh.handleRetry(err)
	case "circuit_breaker":
		// 熔断器逻辑
		eh.handleCircuitBreaker(err)
	default:
		eh.logger.Error("Unknown error handling strategy", zap.String("strategy", eh.config.Strategy))
	}
}

// handleRetry 处理重试
func (eh *ErrorHandler) handleRetry(err error) {
	// 简化的重试逻辑
	eh.logger.Info("Retrying after error", zap.Error(err))
}

// handleCircuitBreaker 处理熔断器
func (eh *ErrorHandler) handleCircuitBreaker(err error) {
	if eh.circuitBreaker != nil {
		eh.circuitBreaker.RecordFailure()
	}
}

// ShouldTrip 检查是否应该触发熔断
func (cb *CircuitBreaker) ShouldTrip() bool {
	cb.mutex.RLock()
	defer cb.mutex.RUnlock()

	switch cb.state {
	case CircuitOpen:
		// 检查是否可以进入半开状态
		if time.Since(cb.lastFailure) > time.Duration(cb.config.RecoveryTimeout) {
			cb.state = CircuitHalfOpen
			return false
		}
		return true
	case CircuitHalfOpen:
		// 半开状态，允许少量请求通过
		return false
	default:
		return false
	}
}

// RecordFailure 记录失败
func (cb *CircuitBreaker) RecordFailure() {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	cb.failureCount++
	cb.lastFailure = time.Now()

	if cb.failureCount >= cb.config.FailureThreshold {
		cb.state = CircuitOpen
	}
}

// RecordSuccess 记录成功
func (cb *CircuitBreaker) RecordSuccess() {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	cb.failureCount = 0
	cb.state = CircuitClosed
}

// Collect 收集指标
func (pmc *PipelineMetricsCollector) Collect() {
	// 更新基础指标
	pmc.pipeline.Metrics.LastUpdate = time.Now()

	// 计算吞吐量
	elapsed := time.Since(pmc.pipeline.Metrics.StartTime)
	if elapsed > 0 {
		pmc.pipeline.Metrics.Throughput = float64(pmc.pipeline.Metrics.ProcessedCount) / elapsed.Seconds()
	}

	// 更新通道大小
	pmc.pipeline.Metrics.ChannelSize = len(pmc.pipeline.dataCh)

	pmc.logger.Debug("Metrics collected",
		zap.String("pipeline_id", pmc.pipeline.ID),
		zap.Int64("processed", pmc.pipeline.Metrics.ProcessedCount),
		zap.Float64("throughput", pmc.pipeline.Metrics.Throughput))
}
