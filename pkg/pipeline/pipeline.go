package pipeline

import (
	"context"
	"sync"
	"time"

	"go.uber.org/zap"
)

// Pipeline - 流水线核心结构
type Pipeline struct {
	// 基础信息
	ID          string `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Version     string `json:"version"`

	// 插件链路
	Collector  CollectorPlugin  `json:"-"` // 采集器(必须)
	Processors []PipelinePlugin `json:"-"` // 处理器链路

	// 配置信息
	Config *PipelineConfig `json:"config"`

	// 运行时状态
	State     PipelineState `json:"state"`
	StartTime time.Time     `json:"start_time"`
	StopTime  time.Time     `json:"stop_time"`

	// 数据通道
	dataCh  chan *PipelineData `json:"-"` // 数据流通道
	errorCh chan error         `json:"-"` // 错误通道
	stopCh  chan struct{}      `json:"-"` // 停止信号

	// 监控指标
	Metrics *PipelineMetrics `json:"metrics"`

	// 上下文和日志
	ctx    context.Context    `json:"-"`
	cancel context.CancelFunc `json:"-"`
	logger *zap.Logger        `json:"-"`
	mutex  sync.RWMutex       `json:"-"`
}

// PipelineConfig - 流水线配置
type PipelineConfig struct {
	// 基础配置
	Enabled       bool          `yaml:"enabled"`
	BufferSize    int           `yaml:"buffer_size"`
	WorkerCount   int           `yaml:"worker_count"`
	Timeout       time.Duration `yaml:"timeout"`
	RetryAttempts int           `yaml:"retry_attempts"`
	RetryDelay    time.Duration `yaml:"retry_delay"`

	// 采集器配置
	Collector CollectorConfig `yaml:"collector"`

	// 处理器配置
	Processors []ProcessorConfig `yaml:"processors"`

	// 监控配置
	EnableMetrics   bool          `yaml:"enable_metrics"`
	EnableTracing   bool          `yaml:"enable_tracing"`
	MetricsInterval time.Duration `yaml:"metrics_interval"`

	// 错误处理配置
	ErrorHandling ErrorHandlingConfig `yaml:"error_handling"`

	// 资源限制
	ResourceLimits ResourceLimits `yaml:"resource_limits"`
}

// CollectorConfig - 采集器配置
type CollectorConfig struct {
	Name     string                 `yaml:"name"`
	Type     string                 `yaml:"type"`
	Interval time.Duration          `yaml:"interval"`
	Config   map[string]interface{} `yaml:"config"`
	Enabled  bool                   `yaml:"enabled"`
}

// ProcessorConfig - 处理器配置
type ProcessorConfig struct {
	Name        string                 `yaml:"name"`
	Type        string                 `yaml:"type"`
	Enabled     bool                   `yaml:"enabled"`
	Config      map[string]interface{} `yaml:"config"`
	Concurrency int                    `yaml:"concurrency"`
	Timeout     time.Duration          `yaml:"timeout"`
	Order       int                    `yaml:"order"`
}

// ErrorHandlingConfig 错误处理配置
type ErrorHandlingConfig struct {
	Strategy       string               `yaml:"strategy"` // ignore, retry, circuit_breaker
	MaxRetries     int                  `yaml:"max_retries"`
	RetryDelay     time.Duration        `yaml:"retry_delay"`
	CircuitBreaker CircuitBreakerConfig `yaml:"circuit_breaker"`
	DeadLetter     DeadLetterConfig     `yaml:"dead_letter"`
}

// CircuitBreakerConfig 熔断器配置
type CircuitBreakerConfig struct {
	Enabled          bool          `yaml:"enabled"`
	FailureThreshold int           `yaml:"failure_threshold"`
	RecoveryTimeout  time.Duration `yaml:"recovery_timeout"`
	HalfOpenRequests int           `yaml:"half_open_requests"`
}

// DeadLetterConfig 死信队列配置
type DeadLetterConfig struct {
	Enabled    bool   `yaml:"enabled"`
	QueueSize  int    `yaml:"queue_size"`
	Persistent bool   `yaml:"persistent"`
	Path       string `yaml:"path"`
}

// ResourceLimits 资源限制
type ResourceLimits struct {
	MaxMemoryMB    int64         `yaml:"max_memory_mb"`
	MaxCPUPercent  int           `yaml:"max_cpu_percent"`
	MaxGoroutines  int           `yaml:"max_goroutines"`
	ProcessTimeout time.Duration `yaml:"process_timeout"`
	CollectTimeout time.Duration `yaml:"collect_timeout"`
}

// PipelineMetrics - 流水线监控指标
type PipelineMetrics struct {
	// 基础计数器
	CollectedCount int64 `json:"collected_count"`
	ProcessedCount int64 `json:"processed_count"`
	ErrorCount     int64 `json:"error_count"`
	DroppedCount   int64 `json:"dropped_count"`

	// 性能指标
	AvgLatency time.Duration `json:"avg_latency"`
	P95Latency time.Duration `json:"p95_latency"`
	P99Latency time.Duration `json:"p99_latency"`
	Throughput float64       `json:"throughput"` // 每秒处理量

	// 资源使用
	MemoryUsage    int64   `json:"memory_usage"`
	CPUUsage       float64 `json:"cpu_usage"`
	GoroutineCount int     `json:"goroutine_count"`
	ChannelSize    int     `json:"channel_size"`

	// 插件指标
	PluginMetrics map[string]*PluginMetrics `json:"plugin_metrics"`

	// 时间戳
	LastUpdate time.Time `json:"last_update"`
	StartTime  time.Time `json:"start_time"`

	// 状态统计
	StateHistory []StateChange `json:"state_history"`

	// 错误统计
	ErrorStats map[string]int64 `json:"error_stats"`

	// 性能历史
	LatencyHistory []LatencyPoint `json:"latency_history"`

	// 互斥锁
	mutex sync.RWMutex `json:"-"`
}

// StateChange 状态变更记录
type StateChange struct {
	From      PipelineState `json:"from"`
	To        PipelineState `json:"to"`
	Timestamp time.Time     `json:"timestamp"`
	Reason    string        `json:"reason"`
}

// LatencyPoint 延迟数据点
type LatencyPoint struct {
	Timestamp time.Time     `json:"timestamp"`
	Latency   time.Duration `json:"latency"`
	Stage     string        `json:"stage"`
}

// NewPipelineMetrics 创建新的流水线指标
func NewPipelineMetrics() *PipelineMetrics {
	return &PipelineMetrics{
		PluginMetrics:  make(map[string]*PluginMetrics),
		ErrorStats:     make(map[string]int64),
		StateHistory:   make([]StateChange, 0),
		LatencyHistory: make([]LatencyPoint, 0, 1000), // 保留最近1000个数据点
		StartTime:      time.Now(),
		LastUpdate:     time.Now(),
	}
}

// IncrementCollected 增加采集计数
func (pm *PipelineMetrics) IncrementCollected() {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()
	pm.CollectedCount++
	pm.LastUpdate = time.Now()
}

// IncrementProcessed 增加处理计数
func (pm *PipelineMetrics) IncrementProcessed() {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()
	pm.ProcessedCount++
	pm.LastUpdate = time.Now()
}

// IncrementErrors 增加错误计数
func (pm *PipelineMetrics) IncrementErrors() {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()
	pm.ErrorCount++
	pm.LastUpdate = time.Now()
}

// IncrementDropped 增加丢弃计数
func (pm *PipelineMetrics) IncrementDropped() {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()
	pm.DroppedCount++
	pm.LastUpdate = time.Now()
}

// RecordLatency 记录延迟
func (pm *PipelineMetrics) RecordLatency(latency time.Duration, stage string) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	// 添加到历史记录
	point := LatencyPoint{
		Timestamp: time.Now(),
		Latency:   latency,
		Stage:     stage,
	}
	pm.LatencyHistory = append(pm.LatencyHistory, point)

	// 保持历史记录大小
	if len(pm.LatencyHistory) > 1000 {
		pm.LatencyHistory = pm.LatencyHistory[1:]
	}

	// 更新平均延迟（简化计算）
	pm.updateLatencyStats()
	pm.LastUpdate = time.Now()
}

// updateLatencyStats 更新延迟统计
func (pm *PipelineMetrics) updateLatencyStats() {
	if len(pm.LatencyHistory) == 0 {
		return
	}

	// 计算最近100个数据点的统计
	start := len(pm.LatencyHistory) - 100
	if start < 0 {
		start = 0
	}

	recent := pm.LatencyHistory[start:]
	if len(recent) == 0 {
		return
	}

	// 计算平均值
	var total time.Duration
	for _, point := range recent {
		total += point.Latency
	}
	pm.AvgLatency = total / time.Duration(len(recent))

	// 计算P95和P99（简化版本）
	if len(recent) >= 20 {
		p95Index := int(float64(len(recent)) * 0.95)
		p99Index := int(float64(len(recent)) * 0.99)
		pm.P95Latency = recent[p95Index].Latency
		pm.P99Latency = recent[p99Index].Latency
	}
}

// RecordStateChange 记录状态变更
func (pm *PipelineMetrics) RecordStateChange(from, to PipelineState, reason string) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	change := StateChange{
		From:      from,
		To:        to,
		Timestamp: time.Now(),
		Reason:    reason,
	}
	pm.StateHistory = append(pm.StateHistory, change)

	// 保持历史记录大小
	if len(pm.StateHistory) > 100 {
		pm.StateHistory = pm.StateHistory[1:]
	}

	pm.LastUpdate = time.Now()
}

// RecordError 记录错误
func (pm *PipelineMetrics) RecordError(errorType string) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()
	pm.ErrorStats[errorType]++
	pm.ErrorCount++
	pm.LastUpdate = time.Now()
}

// RecordProcessorLatency 记录处理器延迟
func (pm *PipelineMetrics) RecordProcessorLatency(processorName string, latency time.Duration) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	if pm.PluginMetrics[processorName] == nil {
		pm.PluginMetrics[processorName] = &PluginMetrics{
			Name: processorName,
		}
	}

	plugin := pm.PluginMetrics[processorName]
	plugin.ProcessedCount++
	plugin.LastProcessTime = time.Now()
	plugin.AvgLatency = (plugin.AvgLatency + latency) / 2 // 简化的平均值计算

	pm.LastUpdate = time.Now()
}

// GetSnapshot 获取指标快照
func (pm *PipelineMetrics) GetSnapshot() *PipelineMetrics {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	// 创建副本
	snapshot := &PipelineMetrics{
		CollectedCount: pm.CollectedCount,
		ProcessedCount: pm.ProcessedCount,
		ErrorCount:     pm.ErrorCount,
		DroppedCount:   pm.DroppedCount,
		AvgLatency:     pm.AvgLatency,
		P95Latency:     pm.P95Latency,
		P99Latency:     pm.P99Latency,
		Throughput:     pm.Throughput,
		MemoryUsage:    pm.MemoryUsage,
		CPUUsage:       pm.CPUUsage,
		GoroutineCount: pm.GoroutineCount,
		ChannelSize:    pm.ChannelSize,
		LastUpdate:     pm.LastUpdate,
		StartTime:      pm.StartTime,
		StateHistory:   make([]StateChange, len(pm.StateHistory)),
		ErrorStats:     make(map[string]int64),
		LatencyHistory: make([]LatencyPoint, len(pm.LatencyHistory)),
		PluginMetrics:  make(map[string]*PluginMetrics),
	}

	// 复制切片
	copy(snapshot.StateHistory, pm.StateHistory)
	copy(snapshot.LatencyHistory, pm.LatencyHistory)

	// 复制映射
	for k, v := range pm.ErrorStats {
		snapshot.ErrorStats[k] = v
	}
	for k, v := range pm.PluginMetrics {
		pluginCopy := *v
		snapshot.PluginMetrics[k] = &pluginCopy
	}

	return snapshot
}
