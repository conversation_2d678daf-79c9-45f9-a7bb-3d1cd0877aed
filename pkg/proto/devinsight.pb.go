// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.28.2
// source: pkg/proto/devinsight.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// RegisterAgentRequest Agent 注册请求
type RegisterAgentRequest struct {
	state                   protoimpl.MessageState `protogen:"open.v1"`
	AgentId                 string                 `protobuf:"bytes,1,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	AgentIp                 string                 `protobuf:"bytes,2,opt,name=agent_ip,json=agentIp,proto3" json:"agent_ip,omitempty"`
	SupportedCollectorTypes []string               `protobuf:"bytes,3,rep,name=supported_collector_types,json=supportedCollectorTypes,proto3" json:"supported_collector_types,omitempty"`
	DeviceConfig            *DeviceConfig          `protobuf:"bytes,4,opt,name=device_config,json=deviceConfig,proto3" json:"device_config,omitempty"` // Agent设备配置信息
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *RegisterAgentRequest) Reset() {
	*x = RegisterAgentRequest{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterAgentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterAgentRequest) ProtoMessage() {}

func (x *RegisterAgentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterAgentRequest.ProtoReflect.Descriptor instead.
func (*RegisterAgentRequest) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{0}
}

func (x *RegisterAgentRequest) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

func (x *RegisterAgentRequest) GetAgentIp() string {
	if x != nil {
		return x.AgentIp
	}
	return ""
}

func (x *RegisterAgentRequest) GetSupportedCollectorTypes() []string {
	if x != nil {
		return x.SupportedCollectorTypes
	}
	return nil
}

func (x *RegisterAgentRequest) GetDeviceConfig() *DeviceConfig {
	if x != nil {
		return x.DeviceConfig
	}
	return nil
}

// DeviceConfig Agent设备配置信息
type DeviceConfig struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	MaxMemoryMb        int64                  `protobuf:"varint,1,opt,name=max_memory_mb,json=maxMemoryMb,proto3" json:"max_memory_mb,omitempty"`
	MaxCpuPercent      int32                  `protobuf:"varint,2,opt,name=max_cpu_percent,json=maxCpuPercent,proto3" json:"max_cpu_percent,omitempty"`
	MaxDiskMb          int64                  `protobuf:"varint,3,opt,name=max_disk_mb,json=maxDiskMb,proto3" json:"max_disk_mb,omitempty"`
	MaxConcurrentTasks int32                  `protobuf:"varint,4,opt,name=max_concurrent_tasks,json=maxConcurrentTasks,proto3" json:"max_concurrent_tasks,omitempty"`
	Capabilities       map[string]string      `protobuf:"bytes,5,rep,name=capabilities,proto3" json:"capabilities,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *DeviceConfig) Reset() {
	*x = DeviceConfig{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceConfig) ProtoMessage() {}

func (x *DeviceConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceConfig.ProtoReflect.Descriptor instead.
func (*DeviceConfig) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{1}
}

func (x *DeviceConfig) GetMaxMemoryMb() int64 {
	if x != nil {
		return x.MaxMemoryMb
	}
	return 0
}

func (x *DeviceConfig) GetMaxCpuPercent() int32 {
	if x != nil {
		return x.MaxCpuPercent
	}
	return 0
}

func (x *DeviceConfig) GetMaxDiskMb() int64 {
	if x != nil {
		return x.MaxDiskMb
	}
	return 0
}

func (x *DeviceConfig) GetMaxConcurrentTasks() int32 {
	if x != nil {
		return x.MaxConcurrentTasks
	}
	return 0
}

func (x *DeviceConfig) GetCapabilities() map[string]string {
	if x != nil {
		return x.Capabilities
	}
	return nil
}

// RegisterAgentResponse Agent 注册响应
type RegisterAgentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterAgentResponse) Reset() {
	*x = RegisterAgentResponse{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterAgentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterAgentResponse) ProtoMessage() {}

func (x *RegisterAgentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterAgentResponse.ProtoReflect.Descriptor instead.
func (*RegisterAgentResponse) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{2}
}

func (x *RegisterAgentResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RegisterAgentResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// CollectorTaskConfig 采集任务配置
type CollectorTaskConfig struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	TaskId           string                 `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	DeviceId         string                 `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	DeviceName       string                 `protobuf:"bytes,3,opt,name=device_name,json=deviceName,proto3" json:"device_name,omitempty"`
	DeviceType       string                 `protobuf:"bytes,4,opt,name=device_type,json=deviceType,proto3" json:"device_type,omitempty"`
	Host             string                 `protobuf:"bytes,5,opt,name=host,proto3" json:"host,omitempty"`
	Port             int32                  `protobuf:"varint,6,opt,name=port,proto3" json:"port,omitempty"`
	Username         string                 `protobuf:"bytes,7,opt,name=username,proto3" json:"username,omitempty"`
	Password         string                 `protobuf:"bytes,8,opt,name=password,proto3" json:"password,omitempty"`
	ConnectParams    map[string]string      `protobuf:"bytes,9,rep,name=connect_params,json=connectParams,proto3" json:"connect_params,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	FrequencySeconds int64                  `protobuf:"varint,10,opt,name=frequency_seconds,json=frequencySeconds,proto3" json:"frequency_seconds,omitempty"`
	CollectItems     []string               `protobuf:"bytes,11,rep,name=collect_items,json=collectItems,proto3" json:"collect_items,omitempty"`
	IsEnabled        bool                   `protobuf:"varint,12,opt,name=is_enabled,json=isEnabled,proto3" json:"is_enabled,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *CollectorTaskConfig) Reset() {
	*x = CollectorTaskConfig{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CollectorTaskConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectorTaskConfig) ProtoMessage() {}

func (x *CollectorTaskConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectorTaskConfig.ProtoReflect.Descriptor instead.
func (*CollectorTaskConfig) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{3}
}

func (x *CollectorTaskConfig) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *CollectorTaskConfig) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *CollectorTaskConfig) GetDeviceName() string {
	if x != nil {
		return x.DeviceName
	}
	return ""
}

func (x *CollectorTaskConfig) GetDeviceType() string {
	if x != nil {
		return x.DeviceType
	}
	return ""
}

func (x *CollectorTaskConfig) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *CollectorTaskConfig) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *CollectorTaskConfig) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *CollectorTaskConfig) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *CollectorTaskConfig) GetConnectParams() map[string]string {
	if x != nil {
		return x.ConnectParams
	}
	return nil
}

func (x *CollectorTaskConfig) GetFrequencySeconds() int64 {
	if x != nil {
		return x.FrequencySeconds
	}
	return 0
}

func (x *CollectorTaskConfig) GetCollectItems() []string {
	if x != nil {
		return x.CollectItems
	}
	return nil
}

func (x *CollectorTaskConfig) GetIsEnabled() bool {
	if x != nil {
		return x.IsEnabled
	}
	return false
}

// TaskStatus 任务状态上报
type TaskStatus struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	TaskId               string                 `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	Status               string                 `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"` // running, failed, success
	ErrorMessage         string                 `protobuf:"bytes,3,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	LastCollectTimestamp int64                  `protobuf:"varint,4,opt,name=last_collect_timestamp,json=lastCollectTimestamp,proto3" json:"last_collect_timestamp,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *TaskStatus) Reset() {
	*x = TaskStatus{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskStatus) ProtoMessage() {}

func (x *TaskStatus) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskStatus.ProtoReflect.Descriptor instead.
func (*TaskStatus) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{4}
}

func (x *TaskStatus) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *TaskStatus) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *TaskStatus) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *TaskStatus) GetLastCollectTimestamp() int64 {
	if x != nil {
		return x.LastCollectTimestamp
	}
	return 0
}

// MetricData 指标数据点 - 支持灵活的多维数据
type MetricData struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	DeviceId  string                 `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	MetricKey string                 `protobuf:"bytes,2,opt,name=metric_key,json=metricKey,proto3" json:"metric_key,omitempty"`
	// Types that are valid to be assigned to ValueType:
	//
	//	*MetricData_NumericValue
	//	*MetricData_StringValue
	//	*MetricData_BooleanValue
	ValueType     isMetricData_ValueType `protobuf_oneof:"value_type"`
	Timestamp     int64                  `protobuf:"varint,6,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	JsonData      string                 `protobuf:"bytes,7,opt,name=json_data,json=jsonData,proto3" json:"json_data,omitempty"` // 灵活的JSON格式数据，支持复杂的多维指标
	Labels        map[string]string      `protobuf:"bytes,8,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MetricData) Reset() {
	*x = MetricData{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MetricData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MetricData) ProtoMessage() {}

func (x *MetricData) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MetricData.ProtoReflect.Descriptor instead.
func (*MetricData) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{5}
}

func (x *MetricData) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *MetricData) GetMetricKey() string {
	if x != nil {
		return x.MetricKey
	}
	return ""
}

func (x *MetricData) GetValueType() isMetricData_ValueType {
	if x != nil {
		return x.ValueType
	}
	return nil
}

func (x *MetricData) GetNumericValue() float64 {
	if x != nil {
		if x, ok := x.ValueType.(*MetricData_NumericValue); ok {
			return x.NumericValue
		}
	}
	return 0
}

func (x *MetricData) GetStringValue() string {
	if x != nil {
		if x, ok := x.ValueType.(*MetricData_StringValue); ok {
			return x.StringValue
		}
	}
	return ""
}

func (x *MetricData) GetBooleanValue() bool {
	if x != nil {
		if x, ok := x.ValueType.(*MetricData_BooleanValue); ok {
			return x.BooleanValue
		}
	}
	return false
}

func (x *MetricData) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *MetricData) GetJsonData() string {
	if x != nil {
		return x.JsonData
	}
	return ""
}

func (x *MetricData) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

type isMetricData_ValueType interface {
	isMetricData_ValueType()
}

type MetricData_NumericValue struct {
	NumericValue float64 `protobuf:"fixed64,3,opt,name=numeric_value,json=numericValue,proto3,oneof"`
}

type MetricData_StringValue struct {
	StringValue string `protobuf:"bytes,4,opt,name=string_value,json=stringValue,proto3,oneof"`
}

type MetricData_BooleanValue struct {
	BooleanValue bool `protobuf:"varint,5,opt,name=boolean_value,json=booleanValue,proto3,oneof"`
}

func (*MetricData_NumericValue) isMetricData_ValueType() {}

func (*MetricData_StringValue) isMetricData_ValueType() {}

func (*MetricData_BooleanValue) isMetricData_ValueType() {}

// SupportedMetric 支持的指标定义
type SupportedMetric struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MetricKey     string                 `protobuf:"bytes,1,opt,name=metric_key,json=metricKey,proto3" json:"metric_key,omitempty"`
	MetricName    string                 `protobuf:"bytes,2,opt,name=metric_name,json=metricName,proto3" json:"metric_name,omitempty"`
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	DataType      string                 `protobuf:"bytes,4,opt,name=data_type,json=dataType,proto3" json:"data_type,omitempty"` // numeric, string, boolean, json
	Unit          string                 `protobuf:"bytes,5,opt,name=unit,proto3" json:"unit,omitempty"`
	Metadata      map[string]string      `protobuf:"bytes,6,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	IsActive      bool                   `protobuf:"varint,7,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	CollectorType string                 `protobuf:"bytes,8,opt,name=collector_type,json=collectorType,proto3" json:"collector_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SupportedMetric) Reset() {
	*x = SupportedMetric{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SupportedMetric) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SupportedMetric) ProtoMessage() {}

func (x *SupportedMetric) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SupportedMetric.ProtoReflect.Descriptor instead.
func (*SupportedMetric) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{6}
}

func (x *SupportedMetric) GetMetricKey() string {
	if x != nil {
		return x.MetricKey
	}
	return ""
}

func (x *SupportedMetric) GetMetricName() string {
	if x != nil {
		return x.MetricName
	}
	return ""
}

func (x *SupportedMetric) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SupportedMetric) GetDataType() string {
	if x != nil {
		return x.DataType
	}
	return ""
}

func (x *SupportedMetric) GetUnit() string {
	if x != nil {
		return x.Unit
	}
	return ""
}

func (x *SupportedMetric) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *SupportedMetric) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *SupportedMetric) GetCollectorType() string {
	if x != nil {
		return x.CollectorType
	}
	return ""
}

// LogEntry 日志条目
type LogEntry struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	LogLevel      string                 `protobuf:"bytes,2,opt,name=log_level,json=logLevel,proto3" json:"log_level,omitempty"` // DEBUG, INFO, WARN, ERROR
	Message       string                 `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	Timestamp     int64                  `protobuf:"varint,4,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Source        string                 `protobuf:"bytes,5,opt,name=source,proto3" json:"source,omitempty"`                                                                           // 日志来源：agent, collector, etc.
	Fields        map[string]string      `protobuf:"bytes,6,rep,name=fields,proto3" json:"fields,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"` // 额外的结构化字段
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LogEntry) Reset() {
	*x = LogEntry{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogEntry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogEntry) ProtoMessage() {}

func (x *LogEntry) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogEntry.ProtoReflect.Descriptor instead.
func (*LogEntry) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{7}
}

func (x *LogEntry) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *LogEntry) GetLogLevel() string {
	if x != nil {
		return x.LogLevel
	}
	return ""
}

func (x *LogEntry) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *LogEntry) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *LogEntry) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *LogEntry) GetFields() map[string]string {
	if x != nil {
		return x.Fields
	}
	return nil
}

// StreamLogDataResponse 日志数据上报响应
type StreamLogDataResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	ReceivedCount int32                  `protobuf:"varint,3,opt,name=received_count,json=receivedCount,proto3" json:"received_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StreamLogDataResponse) Reset() {
	*x = StreamLogDataResponse{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StreamLogDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamLogDataResponse) ProtoMessage() {}

func (x *StreamLogDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamLogDataResponse.ProtoReflect.Descriptor instead.
func (*StreamLogDataResponse) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{8}
}

func (x *StreamLogDataResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *StreamLogDataResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *StreamLogDataResponse) GetReceivedCount() int32 {
	if x != nil {
		return x.ReceivedCount
	}
	return 0
}

// StreamMetricDataResponse 指标数据上报响应
type StreamMetricDataResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	ReceivedCount int32                  `protobuf:"varint,3,opt,name=received_count,json=receivedCount,proto3" json:"received_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StreamMetricDataResponse) Reset() {
	*x = StreamMetricDataResponse{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StreamMetricDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamMetricDataResponse) ProtoMessage() {}

func (x *StreamMetricDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamMetricDataResponse.ProtoReflect.Descriptor instead.
func (*StreamMetricDataResponse) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{9}
}

func (x *StreamMetricDataResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *StreamMetricDataResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *StreamMetricDataResponse) GetReceivedCount() int32 {
	if x != nil {
		return x.ReceivedCount
	}
	return 0
}

// PluginRequest Agent 请求插件
type PluginRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AgentId       string                 `protobuf:"bytes,1,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	PluginName    string                 `protobuf:"bytes,2,opt,name=plugin_name,json=pluginName,proto3" json:"plugin_name,omitempty"`
	PluginVersion string                 `protobuf:"bytes,3,opt,name=plugin_version,json=pluginVersion,proto3" json:"plugin_version,omitempty"` // 可选，如果为空则获取最新版本
	DeviceType    string                 `protobuf:"bytes,4,opt,name=device_type,json=deviceType,proto3" json:"device_type,omitempty"`          // 设备类型，用于确定插件兼容性
	Architecture  string                 `protobuf:"bytes,5,opt,name=architecture,proto3" json:"architecture,omitempty"`                        // 系统架构：amd64, arm64, etc.
	Os            string                 `protobuf:"bytes,6,opt,name=os,proto3" json:"os,omitempty"`                                            // 操作系统：linux, windows, darwin
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PluginRequest) Reset() {
	*x = PluginRequest{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PluginRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PluginRequest) ProtoMessage() {}

func (x *PluginRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PluginRequest.ProtoReflect.Descriptor instead.
func (*PluginRequest) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{10}
}

func (x *PluginRequest) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

func (x *PluginRequest) GetPluginName() string {
	if x != nil {
		return x.PluginName
	}
	return ""
}

func (x *PluginRequest) GetPluginVersion() string {
	if x != nil {
		return x.PluginVersion
	}
	return ""
}

func (x *PluginRequest) GetDeviceType() string {
	if x != nil {
		return x.DeviceType
	}
	return ""
}

func (x *PluginRequest) GetArchitecture() string {
	if x != nil {
		return x.Architecture
	}
	return ""
}

func (x *PluginRequest) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

// PluginResponse Control Plane 响应插件请求
type PluginResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Metadata      *PluginMetadata        `protobuf:"bytes,3,opt,name=metadata,proto3" json:"metadata,omitempty"`
	PluginPath    string                 `protobuf:"bytes,4,opt,name=plugin_path,json=pluginPath,proto3" json:"plugin_path,omitempty"`       // 插件在 plugins/build 目录下的相对路径
	Checksum      string                 `protobuf:"bytes,5,opt,name=checksum,proto3" json:"checksum,omitempty"`                             // 插件文件校验和
	AbsolutePath  string                 `protobuf:"bytes,6,opt,name=absolute_path,json=absolutePath,proto3" json:"absolute_path,omitempty"` // 插件的绝对路径（可选）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PluginResponse) Reset() {
	*x = PluginResponse{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PluginResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PluginResponse) ProtoMessage() {}

func (x *PluginResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PluginResponse.ProtoReflect.Descriptor instead.
func (*PluginResponse) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{11}
}

func (x *PluginResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *PluginResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *PluginResponse) GetMetadata() *PluginMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *PluginResponse) GetPluginPath() string {
	if x != nil {
		return x.PluginPath
	}
	return ""
}

func (x *PluginResponse) GetChecksum() string {
	if x != nil {
		return x.Checksum
	}
	return ""
}

func (x *PluginResponse) GetAbsolutePath() string {
	if x != nil {
		return x.AbsolutePath
	}
	return ""
}

// PluginMetadata 插件元数据
type PluginMetadata struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	Name                   string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Version                string                 `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	Description            string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	SupportedDeviceTypes   []string               `protobuf:"bytes,4,rep,name=supported_device_types,json=supportedDeviceTypes,proto3" json:"supported_device_types,omitempty"`
	SupportedArchitectures []string               `protobuf:"bytes,5,rep,name=supported_architectures,json=supportedArchitectures,proto3" json:"supported_architectures,omitempty"`
	SupportedOs            []string               `protobuf:"bytes,6,rep,name=supported_os,json=supportedOs,proto3" json:"supported_os,omitempty"`
	Configuration          map[string]string      `protobuf:"bytes,7,rep,name=configuration,proto3" json:"configuration,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Dependencies           []string               `protobuf:"bytes,8,rep,name=dependencies,proto3" json:"dependencies,omitempty"`
	SizeBytes              int64                  `protobuf:"varint,9,opt,name=size_bytes,json=sizeBytes,proto3" json:"size_bytes,omitempty"`
	Author                 string                 `protobuf:"bytes,10,opt,name=author,proto3" json:"author,omitempty"`
	CreatedAt              int64                  `protobuf:"varint,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt              int64                  `protobuf:"varint,12,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *PluginMetadata) Reset() {
	*x = PluginMetadata{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PluginMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PluginMetadata) ProtoMessage() {}

func (x *PluginMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PluginMetadata.ProtoReflect.Descriptor instead.
func (*PluginMetadata) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{12}
}

func (x *PluginMetadata) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PluginMetadata) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *PluginMetadata) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *PluginMetadata) GetSupportedDeviceTypes() []string {
	if x != nil {
		return x.SupportedDeviceTypes
	}
	return nil
}

func (x *PluginMetadata) GetSupportedArchitectures() []string {
	if x != nil {
		return x.SupportedArchitectures
	}
	return nil
}

func (x *PluginMetadata) GetSupportedOs() []string {
	if x != nil {
		return x.SupportedOs
	}
	return nil
}

func (x *PluginMetadata) GetConfiguration() map[string]string {
	if x != nil {
		return x.Configuration
	}
	return nil
}

func (x *PluginMetadata) GetDependencies() []string {
	if x != nil {
		return x.Dependencies
	}
	return nil
}

func (x *PluginMetadata) GetSizeBytes() int64 {
	if x != nil {
		return x.SizeBytes
	}
	return 0
}

func (x *PluginMetadata) GetAuthor() string {
	if x != nil {
		return x.Author
	}
	return ""
}

func (x *PluginMetadata) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *PluginMetadata) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

// PluginUpdateNotification 插件更新通知
type PluginUpdateNotification struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AgentId       string                 `protobuf:"bytes,1,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	PluginName    string                 `protobuf:"bytes,2,opt,name=plugin_name,json=pluginName,proto3" json:"plugin_name,omitempty"`
	NewVersion    string                 `protobuf:"bytes,3,opt,name=new_version,json=newVersion,proto3" json:"new_version,omitempty"`
	OldVersion    string                 `protobuf:"bytes,4,opt,name=old_version,json=oldVersion,proto3" json:"old_version,omitempty"`
	IsMandatory   bool                   `protobuf:"varint,5,opt,name=is_mandatory,json=isMandatory,proto3" json:"is_mandatory,omitempty"` // 是否强制更新
	Reason        string                 `protobuf:"bytes,6,opt,name=reason,proto3" json:"reason,omitempty"`                               // 更新原因
	Metadata      *PluginMetadata        `protobuf:"bytes,7,opt,name=metadata,proto3" json:"metadata,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PluginUpdateNotification) Reset() {
	*x = PluginUpdateNotification{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PluginUpdateNotification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PluginUpdateNotification) ProtoMessage() {}

func (x *PluginUpdateNotification) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PluginUpdateNotification.ProtoReflect.Descriptor instead.
func (*PluginUpdateNotification) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{13}
}

func (x *PluginUpdateNotification) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

func (x *PluginUpdateNotification) GetPluginName() string {
	if x != nil {
		return x.PluginName
	}
	return ""
}

func (x *PluginUpdateNotification) GetNewVersion() string {
	if x != nil {
		return x.NewVersion
	}
	return ""
}

func (x *PluginUpdateNotification) GetOldVersion() string {
	if x != nil {
		return x.OldVersion
	}
	return ""
}

func (x *PluginUpdateNotification) GetIsMandatory() bool {
	if x != nil {
		return x.IsMandatory
	}
	return false
}

func (x *PluginUpdateNotification) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *PluginUpdateNotification) GetMetadata() *PluginMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// PluginUpdateResponse 插件更新响应
type PluginUpdateResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Success           bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message           string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	NotificationCount int32                  `protobuf:"varint,3,opt,name=notification_count,json=notificationCount,proto3" json:"notification_count,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *PluginUpdateResponse) Reset() {
	*x = PluginUpdateResponse{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PluginUpdateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PluginUpdateResponse) ProtoMessage() {}

func (x *PluginUpdateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PluginUpdateResponse.ProtoReflect.Descriptor instead.
func (*PluginUpdateResponse) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{14}
}

func (x *PluginUpdateResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *PluginUpdateResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *PluginUpdateResponse) GetNotificationCount() int32 {
	if x != nil {
		return x.NotificationCount
	}
	return 0
}

// PluginStatusReport Agent 上报插件状态
type PluginStatusReport struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	AgentId        string                 `protobuf:"bytes,1,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	PluginStatuses []*PluginStatus        `protobuf:"bytes,2,rep,name=plugin_statuses,json=pluginStatuses,proto3" json:"plugin_statuses,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PluginStatusReport) Reset() {
	*x = PluginStatusReport{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PluginStatusReport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PluginStatusReport) ProtoMessage() {}

func (x *PluginStatusReport) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PluginStatusReport.ProtoReflect.Descriptor instead.
func (*PluginStatusReport) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{15}
}

func (x *PluginStatusReport) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

func (x *PluginStatusReport) GetPluginStatuses() []*PluginStatus {
	if x != nil {
		return x.PluginStatuses
	}
	return nil
}

// PluginStatus 单个插件状态
type PluginStatus struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	PluginName        string                 `protobuf:"bytes,1,opt,name=plugin_name,json=pluginName,proto3" json:"plugin_name,omitempty"`
	Version           string                 `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	Status            string                 `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"` // loaded, running, failed, unloaded
	ErrorMessage      string                 `protobuf:"bytes,4,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	LastUsedTimestamp int64                  `protobuf:"varint,5,opt,name=last_used_timestamp,json=lastUsedTimestamp,proto3" json:"last_used_timestamp,omitempty"`
	LoadTimestamp     int64                  `protobuf:"varint,6,opt,name=load_timestamp,json=loadTimestamp,proto3" json:"load_timestamp,omitempty"`
	Metrics           map[string]string      `protobuf:"bytes,7,rep,name=metrics,proto3" json:"metrics,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"` // 插件相关指标
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *PluginStatus) Reset() {
	*x = PluginStatus{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PluginStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PluginStatus) ProtoMessage() {}

func (x *PluginStatus) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PluginStatus.ProtoReflect.Descriptor instead.
func (*PluginStatus) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{16}
}

func (x *PluginStatus) GetPluginName() string {
	if x != nil {
		return x.PluginName
	}
	return ""
}

func (x *PluginStatus) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *PluginStatus) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *PluginStatus) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *PluginStatus) GetLastUsedTimestamp() int64 {
	if x != nil {
		return x.LastUsedTimestamp
	}
	return 0
}

func (x *PluginStatus) GetLoadTimestamp() int64 {
	if x != nil {
		return x.LoadTimestamp
	}
	return 0
}

func (x *PluginStatus) GetMetrics() map[string]string {
	if x != nil {
		return x.Metrics
	}
	return nil
}

// PluginStatusResponse 插件状态上报响应
type PluginStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Actions       []string               `protobuf:"bytes,3,rep,name=actions,proto3" json:"actions,omitempty"` // 建议的操作：reload, unload, update, etc.
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PluginStatusResponse) Reset() {
	*x = PluginStatusResponse{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PluginStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PluginStatusResponse) ProtoMessage() {}

func (x *PluginStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PluginStatusResponse.ProtoReflect.Descriptor instead.
func (*PluginStatusResponse) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{17}
}

func (x *PluginStatusResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *PluginStatusResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *PluginStatusResponse) GetActions() []string {
	if x != nil {
		return x.Actions
	}
	return nil
}

// PipelineConfig 流水线配置
type PipelineConfig struct {
	state       protoimpl.MessageState `protogen:"open.v1"`
	PipelineId  string                 `protobuf:"bytes,1,opt,name=pipeline_id,json=pipelineId,proto3" json:"pipeline_id,omitempty"`
	Name        string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Version     string                 `protobuf:"bytes,4,opt,name=version,proto3" json:"version,omitempty"`
	Enabled     bool                   `protobuf:"varint,5,opt,name=enabled,proto3" json:"enabled,omitempty"`
	// 基础配置
	BufferSize        int32 `protobuf:"varint,6,opt,name=buffer_size,json=bufferSize,proto3" json:"buffer_size,omitempty"`
	WorkerCount       int32 `protobuf:"varint,7,opt,name=worker_count,json=workerCount,proto3" json:"worker_count,omitempty"`
	TimeoutSeconds    int64 `protobuf:"varint,8,opt,name=timeout_seconds,json=timeoutSeconds,proto3" json:"timeout_seconds,omitempty"`
	RetryAttempts     int32 `protobuf:"varint,9,opt,name=retry_attempts,json=retryAttempts,proto3" json:"retry_attempts,omitempty"`
	RetryDelaySeconds int64 `protobuf:"varint,10,opt,name=retry_delay_seconds,json=retryDelaySeconds,proto3" json:"retry_delay_seconds,omitempty"`
	// 采集器配置
	Collector *CollectorPluginConfig `protobuf:"bytes,11,opt,name=collector,proto3" json:"collector,omitempty"`
	// 处理器配置
	Processors []*ProcessorPluginConfig `protobuf:"bytes,12,rep,name=processors,proto3" json:"processors,omitempty"`
	// 监控配置
	EnableMetrics          bool  `protobuf:"varint,13,opt,name=enable_metrics,json=enableMetrics,proto3" json:"enable_metrics,omitempty"`
	EnableTracing          bool  `protobuf:"varint,14,opt,name=enable_tracing,json=enableTracing,proto3" json:"enable_tracing,omitempty"`
	MetricsIntervalSeconds int64 `protobuf:"varint,15,opt,name=metrics_interval_seconds,json=metricsIntervalSeconds,proto3" json:"metrics_interval_seconds,omitempty"`
	// 错误处理配置
	ErrorHandling *ErrorHandlingConfig `protobuf:"bytes,16,opt,name=error_handling,json=errorHandling,proto3" json:"error_handling,omitempty"`
	// 资源限制
	ResourceLimits *ResourceLimitsConfig `protobuf:"bytes,17,opt,name=resource_limits,json=resourceLimits,proto3" json:"resource_limits,omitempty"`
	// 创建和更新时间
	CreatedAt     int64 `protobuf:"varint,18,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     int64 `protobuf:"varint,19,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PipelineConfig) Reset() {
	*x = PipelineConfig{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PipelineConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineConfig) ProtoMessage() {}

func (x *PipelineConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineConfig.ProtoReflect.Descriptor instead.
func (*PipelineConfig) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{18}
}

func (x *PipelineConfig) GetPipelineId() string {
	if x != nil {
		return x.PipelineId
	}
	return ""
}

func (x *PipelineConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PipelineConfig) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *PipelineConfig) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *PipelineConfig) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *PipelineConfig) GetBufferSize() int32 {
	if x != nil {
		return x.BufferSize
	}
	return 0
}

func (x *PipelineConfig) GetWorkerCount() int32 {
	if x != nil {
		return x.WorkerCount
	}
	return 0
}

func (x *PipelineConfig) GetTimeoutSeconds() int64 {
	if x != nil {
		return x.TimeoutSeconds
	}
	return 0
}

func (x *PipelineConfig) GetRetryAttempts() int32 {
	if x != nil {
		return x.RetryAttempts
	}
	return 0
}

func (x *PipelineConfig) GetRetryDelaySeconds() int64 {
	if x != nil {
		return x.RetryDelaySeconds
	}
	return 0
}

func (x *PipelineConfig) GetCollector() *CollectorPluginConfig {
	if x != nil {
		return x.Collector
	}
	return nil
}

func (x *PipelineConfig) GetProcessors() []*ProcessorPluginConfig {
	if x != nil {
		return x.Processors
	}
	return nil
}

func (x *PipelineConfig) GetEnableMetrics() bool {
	if x != nil {
		return x.EnableMetrics
	}
	return false
}

func (x *PipelineConfig) GetEnableTracing() bool {
	if x != nil {
		return x.EnableTracing
	}
	return false
}

func (x *PipelineConfig) GetMetricsIntervalSeconds() int64 {
	if x != nil {
		return x.MetricsIntervalSeconds
	}
	return 0
}

func (x *PipelineConfig) GetErrorHandling() *ErrorHandlingConfig {
	if x != nil {
		return x.ErrorHandling
	}
	return nil
}

func (x *PipelineConfig) GetResourceLimits() *ResourceLimitsConfig {
	if x != nil {
		return x.ResourceLimits
	}
	return nil
}

func (x *PipelineConfig) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *PipelineConfig) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

// CollectorPluginConfig 采集器插件配置
type CollectorPluginConfig struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Name            string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Type            string                 `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	IntervalSeconds int64                  `protobuf:"varint,3,opt,name=interval_seconds,json=intervalSeconds,proto3" json:"interval_seconds,omitempty"`
	Config          map[string]string      `protobuf:"bytes,4,rep,name=config,proto3" json:"config,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Enabled         bool                   `protobuf:"varint,5,opt,name=enabled,proto3" json:"enabled,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *CollectorPluginConfig) Reset() {
	*x = CollectorPluginConfig{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CollectorPluginConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectorPluginConfig) ProtoMessage() {}

func (x *CollectorPluginConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectorPluginConfig.ProtoReflect.Descriptor instead.
func (*CollectorPluginConfig) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{19}
}

func (x *CollectorPluginConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CollectorPluginConfig) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *CollectorPluginConfig) GetIntervalSeconds() int64 {
	if x != nil {
		return x.IntervalSeconds
	}
	return 0
}

func (x *CollectorPluginConfig) GetConfig() map[string]string {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *CollectorPluginConfig) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

// ProcessorPluginConfig 处理器插件配置
type ProcessorPluginConfig struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Name           string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Type           string                 `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	Enabled        bool                   `protobuf:"varint,3,opt,name=enabled,proto3" json:"enabled,omitempty"`
	Config         map[string]string      `protobuf:"bytes,4,rep,name=config,proto3" json:"config,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Concurrency    int32                  `protobuf:"varint,5,opt,name=concurrency,proto3" json:"concurrency,omitempty"`
	TimeoutSeconds int64                  `protobuf:"varint,6,opt,name=timeout_seconds,json=timeoutSeconds,proto3" json:"timeout_seconds,omitempty"`
	Order          int32                  `protobuf:"varint,7,opt,name=order,proto3" json:"order,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ProcessorPluginConfig) Reset() {
	*x = ProcessorPluginConfig{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcessorPluginConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessorPluginConfig) ProtoMessage() {}

func (x *ProcessorPluginConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessorPluginConfig.ProtoReflect.Descriptor instead.
func (*ProcessorPluginConfig) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{20}
}

func (x *ProcessorPluginConfig) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ProcessorPluginConfig) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *ProcessorPluginConfig) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *ProcessorPluginConfig) GetConfig() map[string]string {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *ProcessorPluginConfig) GetConcurrency() int32 {
	if x != nil {
		return x.Concurrency
	}
	return 0
}

func (x *ProcessorPluginConfig) GetTimeoutSeconds() int64 {
	if x != nil {
		return x.TimeoutSeconds
	}
	return 0
}

func (x *ProcessorPluginConfig) GetOrder() int32 {
	if x != nil {
		return x.Order
	}
	return 0
}

// ErrorHandlingConfig 错误处理配置
type ErrorHandlingConfig struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Strategy          string                 `protobuf:"bytes,1,opt,name=strategy,proto3" json:"strategy,omitempty"` // ignore, retry, circuit_breaker
	MaxRetries        int32                  `protobuf:"varint,2,opt,name=max_retries,json=maxRetries,proto3" json:"max_retries,omitempty"`
	RetryDelaySeconds int64                  `protobuf:"varint,3,opt,name=retry_delay_seconds,json=retryDelaySeconds,proto3" json:"retry_delay_seconds,omitempty"`
	CircuitBreaker    *CircuitBreakerConfig  `protobuf:"bytes,4,opt,name=circuit_breaker,json=circuitBreaker,proto3" json:"circuit_breaker,omitempty"`
	DeadLetter        *DeadLetterConfig      `protobuf:"bytes,5,opt,name=dead_letter,json=deadLetter,proto3" json:"dead_letter,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *ErrorHandlingConfig) Reset() {
	*x = ErrorHandlingConfig{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ErrorHandlingConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorHandlingConfig) ProtoMessage() {}

func (x *ErrorHandlingConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorHandlingConfig.ProtoReflect.Descriptor instead.
func (*ErrorHandlingConfig) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{21}
}

func (x *ErrorHandlingConfig) GetStrategy() string {
	if x != nil {
		return x.Strategy
	}
	return ""
}

func (x *ErrorHandlingConfig) GetMaxRetries() int32 {
	if x != nil {
		return x.MaxRetries
	}
	return 0
}

func (x *ErrorHandlingConfig) GetRetryDelaySeconds() int64 {
	if x != nil {
		return x.RetryDelaySeconds
	}
	return 0
}

func (x *ErrorHandlingConfig) GetCircuitBreaker() *CircuitBreakerConfig {
	if x != nil {
		return x.CircuitBreaker
	}
	return nil
}

func (x *ErrorHandlingConfig) GetDeadLetter() *DeadLetterConfig {
	if x != nil {
		return x.DeadLetter
	}
	return nil
}

// CircuitBreakerConfig 熔断器配置
type CircuitBreakerConfig struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	Enabled                bool                   `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	FailureThreshold       int32                  `protobuf:"varint,2,opt,name=failure_threshold,json=failureThreshold,proto3" json:"failure_threshold,omitempty"`
	RecoveryTimeoutSeconds int64                  `protobuf:"varint,3,opt,name=recovery_timeout_seconds,json=recoveryTimeoutSeconds,proto3" json:"recovery_timeout_seconds,omitempty"`
	HalfOpenRequests       int32                  `protobuf:"varint,4,opt,name=half_open_requests,json=halfOpenRequests,proto3" json:"half_open_requests,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *CircuitBreakerConfig) Reset() {
	*x = CircuitBreakerConfig{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CircuitBreakerConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CircuitBreakerConfig) ProtoMessage() {}

func (x *CircuitBreakerConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CircuitBreakerConfig.ProtoReflect.Descriptor instead.
func (*CircuitBreakerConfig) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{22}
}

func (x *CircuitBreakerConfig) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *CircuitBreakerConfig) GetFailureThreshold() int32 {
	if x != nil {
		return x.FailureThreshold
	}
	return 0
}

func (x *CircuitBreakerConfig) GetRecoveryTimeoutSeconds() int64 {
	if x != nil {
		return x.RecoveryTimeoutSeconds
	}
	return 0
}

func (x *CircuitBreakerConfig) GetHalfOpenRequests() int32 {
	if x != nil {
		return x.HalfOpenRequests
	}
	return 0
}

// DeadLetterConfig 死信队列配置
type DeadLetterConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Enabled       bool                   `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	QueueSize     int32                  `protobuf:"varint,2,opt,name=queue_size,json=queueSize,proto3" json:"queue_size,omitempty"`
	Persistent    bool                   `protobuf:"varint,3,opt,name=persistent,proto3" json:"persistent,omitempty"`
	Path          string                 `protobuf:"bytes,4,opt,name=path,proto3" json:"path,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeadLetterConfig) Reset() {
	*x = DeadLetterConfig{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeadLetterConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeadLetterConfig) ProtoMessage() {}

func (x *DeadLetterConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeadLetterConfig.ProtoReflect.Descriptor instead.
func (*DeadLetterConfig) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{23}
}

func (x *DeadLetterConfig) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *DeadLetterConfig) GetQueueSize() int32 {
	if x != nil {
		return x.QueueSize
	}
	return 0
}

func (x *DeadLetterConfig) GetPersistent() bool {
	if x != nil {
		return x.Persistent
	}
	return false
}

func (x *DeadLetterConfig) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

// ResourceLimitsConfig 资源限制配置
type ResourceLimitsConfig struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	MaxMemoryMb           int64                  `protobuf:"varint,1,opt,name=max_memory_mb,json=maxMemoryMb,proto3" json:"max_memory_mb,omitempty"`
	MaxCpuPercent         int32                  `protobuf:"varint,2,opt,name=max_cpu_percent,json=maxCpuPercent,proto3" json:"max_cpu_percent,omitempty"`
	MaxGoroutines         int32                  `protobuf:"varint,3,opt,name=max_goroutines,json=maxGoroutines,proto3" json:"max_goroutines,omitempty"`
	ProcessTimeoutSeconds int64                  `protobuf:"varint,4,opt,name=process_timeout_seconds,json=processTimeoutSeconds,proto3" json:"process_timeout_seconds,omitempty"`
	CollectTimeoutSeconds int64                  `protobuf:"varint,5,opt,name=collect_timeout_seconds,json=collectTimeoutSeconds,proto3" json:"collect_timeout_seconds,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *ResourceLimitsConfig) Reset() {
	*x = ResourceLimitsConfig{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResourceLimitsConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceLimitsConfig) ProtoMessage() {}

func (x *ResourceLimitsConfig) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceLimitsConfig.ProtoReflect.Descriptor instead.
func (*ResourceLimitsConfig) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{24}
}

func (x *ResourceLimitsConfig) GetMaxMemoryMb() int64 {
	if x != nil {
		return x.MaxMemoryMb
	}
	return 0
}

func (x *ResourceLimitsConfig) GetMaxCpuPercent() int32 {
	if x != nil {
		return x.MaxCpuPercent
	}
	return 0
}

func (x *ResourceLimitsConfig) GetMaxGoroutines() int32 {
	if x != nil {
		return x.MaxGoroutines
	}
	return 0
}

func (x *ResourceLimitsConfig) GetProcessTimeoutSeconds() int64 {
	if x != nil {
		return x.ProcessTimeoutSeconds
	}
	return 0
}

func (x *ResourceLimitsConfig) GetCollectTimeoutSeconds() int64 {
	if x != nil {
		return x.CollectTimeoutSeconds
	}
	return 0
}

// PipelineStatus 流水线状态
type PipelineStatus struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	PipelineId          string                 `protobuf:"bytes,1,opt,name=pipeline_id,json=pipelineId,proto3" json:"pipeline_id,omitempty"`
	AgentId             string                 `protobuf:"bytes,2,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	Status              string                 `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"` // idle, starting, running, stopping, stopped, error
	ErrorMessage        string                 `protobuf:"bytes,4,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	StartTimestamp      int64                  `protobuf:"varint,5,opt,name=start_timestamp,json=startTimestamp,proto3" json:"start_timestamp,omitempty"`
	LastUpdateTimestamp int64                  `protobuf:"varint,6,opt,name=last_update_timestamp,json=lastUpdateTimestamp,proto3" json:"last_update_timestamp,omitempty"`
	Metrics             *PipelineMetrics       `protobuf:"bytes,7,opt,name=metrics,proto3" json:"metrics,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *PipelineStatus) Reset() {
	*x = PipelineStatus{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PipelineStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineStatus) ProtoMessage() {}

func (x *PipelineStatus) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineStatus.ProtoReflect.Descriptor instead.
func (*PipelineStatus) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{25}
}

func (x *PipelineStatus) GetPipelineId() string {
	if x != nil {
		return x.PipelineId
	}
	return ""
}

func (x *PipelineStatus) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

func (x *PipelineStatus) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *PipelineStatus) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *PipelineStatus) GetStartTimestamp() int64 {
	if x != nil {
		return x.StartTimestamp
	}
	return 0
}

func (x *PipelineStatus) GetLastUpdateTimestamp() int64 {
	if x != nil {
		return x.LastUpdateTimestamp
	}
	return 0
}

func (x *PipelineStatus) GetMetrics() *PipelineMetrics {
	if x != nil {
		return x.Metrics
	}
	return nil
}

// PipelineMetrics 流水线指标
type PipelineMetrics struct {
	state               protoimpl.MessageState    `protogen:"open.v1"`
	CollectedCount      int64                     `protobuf:"varint,1,opt,name=collected_count,json=collectedCount,proto3" json:"collected_count,omitempty"`
	ProcessedCount      int64                     `protobuf:"varint,2,opt,name=processed_count,json=processedCount,proto3" json:"processed_count,omitempty"`
	ErrorCount          int64                     `protobuf:"varint,3,opt,name=error_count,json=errorCount,proto3" json:"error_count,omitempty"`
	DroppedCount        int64                     `protobuf:"varint,4,opt,name=dropped_count,json=droppedCount,proto3" json:"dropped_count,omitempty"`
	AvgLatencyMs        int64                     `protobuf:"varint,5,opt,name=avg_latency_ms,json=avgLatencyMs,proto3" json:"avg_latency_ms,omitempty"`
	P95LatencyMs        int64                     `protobuf:"varint,6,opt,name=p95_latency_ms,json=p95LatencyMs,proto3" json:"p95_latency_ms,omitempty"`
	P99LatencyMs        int64                     `protobuf:"varint,7,opt,name=p99_latency_ms,json=p99LatencyMs,proto3" json:"p99_latency_ms,omitempty"`
	Throughput          float64                   `protobuf:"fixed64,8,opt,name=throughput,proto3" json:"throughput,omitempty"`
	MemoryUsageMb       int64                     `protobuf:"varint,9,opt,name=memory_usage_mb,json=memoryUsageMb,proto3" json:"memory_usage_mb,omitempty"`
	CpuUsagePercent     float64                   `protobuf:"fixed64,10,opt,name=cpu_usage_percent,json=cpuUsagePercent,proto3" json:"cpu_usage_percent,omitempty"`
	GoroutineCount      int32                     `protobuf:"varint,11,opt,name=goroutine_count,json=goroutineCount,proto3" json:"goroutine_count,omitempty"`
	ChannelSize         int32                     `protobuf:"varint,12,opt,name=channel_size,json=channelSize,proto3" json:"channel_size,omitempty"`
	PluginMetrics       map[string]*PluginMetrics `protobuf:"bytes,13,rep,name=plugin_metrics,json=pluginMetrics,proto3" json:"plugin_metrics,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	LastUpdateTimestamp int64                     `protobuf:"varint,14,opt,name=last_update_timestamp,json=lastUpdateTimestamp,proto3" json:"last_update_timestamp,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *PipelineMetrics) Reset() {
	*x = PipelineMetrics{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PipelineMetrics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineMetrics) ProtoMessage() {}

func (x *PipelineMetrics) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineMetrics.ProtoReflect.Descriptor instead.
func (*PipelineMetrics) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{26}
}

func (x *PipelineMetrics) GetCollectedCount() int64 {
	if x != nil {
		return x.CollectedCount
	}
	return 0
}

func (x *PipelineMetrics) GetProcessedCount() int64 {
	if x != nil {
		return x.ProcessedCount
	}
	return 0
}

func (x *PipelineMetrics) GetErrorCount() int64 {
	if x != nil {
		return x.ErrorCount
	}
	return 0
}

func (x *PipelineMetrics) GetDroppedCount() int64 {
	if x != nil {
		return x.DroppedCount
	}
	return 0
}

func (x *PipelineMetrics) GetAvgLatencyMs() int64 {
	if x != nil {
		return x.AvgLatencyMs
	}
	return 0
}

func (x *PipelineMetrics) GetP95LatencyMs() int64 {
	if x != nil {
		return x.P95LatencyMs
	}
	return 0
}

func (x *PipelineMetrics) GetP99LatencyMs() int64 {
	if x != nil {
		return x.P99LatencyMs
	}
	return 0
}

func (x *PipelineMetrics) GetThroughput() float64 {
	if x != nil {
		return x.Throughput
	}
	return 0
}

func (x *PipelineMetrics) GetMemoryUsageMb() int64 {
	if x != nil {
		return x.MemoryUsageMb
	}
	return 0
}

func (x *PipelineMetrics) GetCpuUsagePercent() float64 {
	if x != nil {
		return x.CpuUsagePercent
	}
	return 0
}

func (x *PipelineMetrics) GetGoroutineCount() int32 {
	if x != nil {
		return x.GoroutineCount
	}
	return 0
}

func (x *PipelineMetrics) GetChannelSize() int32 {
	if x != nil {
		return x.ChannelSize
	}
	return 0
}

func (x *PipelineMetrics) GetPluginMetrics() map[string]*PluginMetrics {
	if x != nil {
		return x.PluginMetrics
	}
	return nil
}

func (x *PipelineMetrics) GetLastUpdateTimestamp() int64 {
	if x != nil {
		return x.LastUpdateTimestamp
	}
	return 0
}

// PluginMetrics 插件指标
type PluginMetrics struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	Name                 string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	ProcessedCount       int64                  `protobuf:"varint,2,opt,name=processed_count,json=processedCount,proto3" json:"processed_count,omitempty"`
	ErrorCount           int64                  `protobuf:"varint,3,opt,name=error_count,json=errorCount,proto3" json:"error_count,omitempty"`
	AvgLatencyMs         int64                  `protobuf:"varint,4,opt,name=avg_latency_ms,json=avgLatencyMs,proto3" json:"avg_latency_ms,omitempty"`
	LastProcessTimestamp int64                  `protobuf:"varint,5,opt,name=last_process_timestamp,json=lastProcessTimestamp,proto3" json:"last_process_timestamp,omitempty"`
	Status               string                 `protobuf:"bytes,6,opt,name=status,proto3" json:"status,omitempty"`
	CustomMetrics        map[string]string      `protobuf:"bytes,7,rep,name=custom_metrics,json=customMetrics,proto3" json:"custom_metrics,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *PluginMetrics) Reset() {
	*x = PluginMetrics{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PluginMetrics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PluginMetrics) ProtoMessage() {}

func (x *PluginMetrics) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PluginMetrics.ProtoReflect.Descriptor instead.
func (*PluginMetrics) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{27}
}

func (x *PluginMetrics) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PluginMetrics) GetProcessedCount() int64 {
	if x != nil {
		return x.ProcessedCount
	}
	return 0
}

func (x *PluginMetrics) GetErrorCount() int64 {
	if x != nil {
		return x.ErrorCount
	}
	return 0
}

func (x *PluginMetrics) GetAvgLatencyMs() int64 {
	if x != nil {
		return x.AvgLatencyMs
	}
	return 0
}

func (x *PluginMetrics) GetLastProcessTimestamp() int64 {
	if x != nil {
		return x.LastProcessTimestamp
	}
	return 0
}

func (x *PluginMetrics) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *PluginMetrics) GetCustomMetrics() map[string]string {
	if x != nil {
		return x.CustomMetrics
	}
	return nil
}

// PipelineTemplateRequest 流水线模板请求
type PipelineTemplateRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AgentId       string                 `protobuf:"bytes,1,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	TemplateType  string                 `protobuf:"bytes,2,opt,name=template_type,json=templateType,proto3" json:"template_type,omitempty"` // mysql, redis, system, etc.
	Category      string                 `protobuf:"bytes,3,opt,name=category,proto3" json:"category,omitempty"`                             // monitoring, alerting, analysis
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PipelineTemplateRequest) Reset() {
	*x = PipelineTemplateRequest{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PipelineTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineTemplateRequest) ProtoMessage() {}

func (x *PipelineTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineTemplateRequest.ProtoReflect.Descriptor instead.
func (*PipelineTemplateRequest) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{28}
}

func (x *PipelineTemplateRequest) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

func (x *PipelineTemplateRequest) GetTemplateType() string {
	if x != nil {
		return x.TemplateType
	}
	return ""
}

func (x *PipelineTemplateRequest) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

// PipelineTemplateResponse 流水线模板响应
type PipelineTemplateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Templates     []*PipelineTemplate    `protobuf:"bytes,3,rep,name=templates,proto3" json:"templates,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PipelineTemplateResponse) Reset() {
	*x = PipelineTemplateResponse{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PipelineTemplateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineTemplateResponse) ProtoMessage() {}

func (x *PipelineTemplateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineTemplateResponse.ProtoReflect.Descriptor instead.
func (*PipelineTemplateResponse) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{29}
}

func (x *PipelineTemplateResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *PipelineTemplateResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *PipelineTemplateResponse) GetTemplates() []*PipelineTemplate {
	if x != nil {
		return x.Templates
	}
	return nil
}

// PipelineTemplate 流水线模板
type PipelineTemplate struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name           string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description    string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Category       string                 `protobuf:"bytes,4,opt,name=category,proto3" json:"category,omitempty"`
	Tags           []string               `protobuf:"bytes,5,rep,name=tags,proto3" json:"tags,omitempty"`
	ConfigTemplate *PipelineConfig        `protobuf:"bytes,6,opt,name=config_template,json=configTemplate,proto3" json:"config_template,omitempty"`
	DefaultValues  map[string]string      `protobuf:"bytes,7,rep,name=default_values,json=defaultValues,proto3" json:"default_values,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Parameters     []*TemplateParameter   `protobuf:"bytes,8,rep,name=parameters,proto3" json:"parameters,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PipelineTemplate) Reset() {
	*x = PipelineTemplate{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PipelineTemplate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineTemplate) ProtoMessage() {}

func (x *PipelineTemplate) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineTemplate.ProtoReflect.Descriptor instead.
func (*PipelineTemplate) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{30}
}

func (x *PipelineTemplate) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PipelineTemplate) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PipelineTemplate) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *PipelineTemplate) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *PipelineTemplate) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *PipelineTemplate) GetConfigTemplate() *PipelineConfig {
	if x != nil {
		return x.ConfigTemplate
	}
	return nil
}

func (x *PipelineTemplate) GetDefaultValues() map[string]string {
	if x != nil {
		return x.DefaultValues
	}
	return nil
}

func (x *PipelineTemplate) GetParameters() []*TemplateParameter {
	if x != nil {
		return x.Parameters
	}
	return nil
}

// TemplateParameter 模板参数
type TemplateParameter struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Type          string                 `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"` // string, int, bool, float
	Description   string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	Required      bool                   `protobuf:"varint,4,opt,name=required,proto3" json:"required,omitempty"`
	DefaultValue  string                 `protobuf:"bytes,5,opt,name=default_value,json=defaultValue,proto3" json:"default_value,omitempty"`
	AllowedValues []string               `protobuf:"bytes,6,rep,name=allowed_values,json=allowedValues,proto3" json:"allowed_values,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TemplateParameter) Reset() {
	*x = TemplateParameter{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TemplateParameter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TemplateParameter) ProtoMessage() {}

func (x *TemplateParameter) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TemplateParameter.ProtoReflect.Descriptor instead.
func (*TemplateParameter) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{31}
}

func (x *TemplateParameter) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TemplateParameter) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *TemplateParameter) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *TemplateParameter) GetRequired() bool {
	if x != nil {
		return x.Required
	}
	return false
}

func (x *TemplateParameter) GetDefaultValue() string {
	if x != nil {
		return x.DefaultValue
	}
	return ""
}

func (x *TemplateParameter) GetAllowedValues() []string {
	if x != nil {
		return x.AllowedValues
	}
	return nil
}

// PipelineConfigValidationRequest 流水线配置验证请求
type PipelineConfigValidationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AgentId       string                 `protobuf:"bytes,1,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	Config        *PipelineConfig        `protobuf:"bytes,2,opt,name=config,proto3" json:"config,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PipelineConfigValidationRequest) Reset() {
	*x = PipelineConfigValidationRequest{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PipelineConfigValidationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineConfigValidationRequest) ProtoMessage() {}

func (x *PipelineConfigValidationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineConfigValidationRequest.ProtoReflect.Descriptor instead.
func (*PipelineConfigValidationRequest) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{32}
}

func (x *PipelineConfigValidationRequest) GetAgentId() string {
	if x != nil {
		return x.AgentId
	}
	return ""
}

func (x *PipelineConfigValidationRequest) GetConfig() *PipelineConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

// PipelineConfigValidationResponse 流水线配置验证响应
type PipelineConfigValidationResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Valid         bool                   `protobuf:"varint,1,opt,name=valid,proto3" json:"valid,omitempty"`
	Errors        []*ValidationError     `protobuf:"bytes,2,rep,name=errors,proto3" json:"errors,omitempty"`
	Warnings      []*ValidationWarning   `protobuf:"bytes,3,rep,name=warnings,proto3" json:"warnings,omitempty"`
	Message       string                 `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PipelineConfigValidationResponse) Reset() {
	*x = PipelineConfigValidationResponse{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PipelineConfigValidationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PipelineConfigValidationResponse) ProtoMessage() {}

func (x *PipelineConfigValidationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PipelineConfigValidationResponse.ProtoReflect.Descriptor instead.
func (*PipelineConfigValidationResponse) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{33}
}

func (x *PipelineConfigValidationResponse) GetValid() bool {
	if x != nil {
		return x.Valid
	}
	return false
}

func (x *PipelineConfigValidationResponse) GetErrors() []*ValidationError {
	if x != nil {
		return x.Errors
	}
	return nil
}

func (x *PipelineConfigValidationResponse) GetWarnings() []*ValidationWarning {
	if x != nil {
		return x.Warnings
	}
	return nil
}

func (x *PipelineConfigValidationResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// ValidationError 验证错误
type ValidationError struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Field         string                 `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Code          string                 `protobuf:"bytes,3,opt,name=code,proto3" json:"code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidationError) Reset() {
	*x = ValidationError{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidationError) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidationError) ProtoMessage() {}

func (x *ValidationError) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidationError.ProtoReflect.Descriptor instead.
func (*ValidationError) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{34}
}

func (x *ValidationError) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *ValidationError) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ValidationError) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

// ValidationWarning 验证警告
type ValidationWarning struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Field         string                 `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Code          string                 `protobuf:"bytes,3,opt,name=code,proto3" json:"code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidationWarning) Reset() {
	*x = ValidationWarning{}
	mi := &file_pkg_proto_devinsight_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidationWarning) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidationWarning) ProtoMessage() {}

func (x *ValidationWarning) ProtoReflect() protoreflect.Message {
	mi := &file_pkg_proto_devinsight_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidationWarning.ProtoReflect.Descriptor instead.
func (*ValidationWarning) Descriptor() ([]byte, []int) {
	return file_pkg_proto_devinsight_proto_rawDescGZIP(), []int{35}
}

func (x *ValidationWarning) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *ValidationWarning) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ValidationWarning) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

var File_pkg_proto_devinsight_proto protoreflect.FileDescriptor

const file_pkg_proto_devinsight_proto_rawDesc = "" +
	"\n" +
	"\x1apkg/proto/devinsight.proto\x12\n" +
	"devinsight\x1a\x1bgoogle/protobuf/empty.proto\"\xc7\x01\n" +
	"\x14RegisterAgentRequest\x12\x19\n" +
	"\bagent_id\x18\x01 \x01(\tR\aagentId\x12\x19\n" +
	"\bagent_ip\x18\x02 \x01(\tR\aagentIp\x12:\n" +
	"\x19supported_collector_types\x18\x03 \x03(\tR\x17supportedCollectorTypes\x12=\n" +
	"\rdevice_config\x18\x04 \x01(\v2\x18.devinsight.DeviceConfigR\fdeviceConfig\"\xbd\x02\n" +
	"\fDeviceConfig\x12\"\n" +
	"\rmax_memory_mb\x18\x01 \x01(\x03R\vmaxMemoryMb\x12&\n" +
	"\x0fmax_cpu_percent\x18\x02 \x01(\x05R\rmaxCpuPercent\x12\x1e\n" +
	"\vmax_disk_mb\x18\x03 \x01(\x03R\tmaxDiskMb\x120\n" +
	"\x14max_concurrent_tasks\x18\x04 \x01(\x05R\x12maxConcurrentTasks\x12N\n" +
	"\fcapabilities\x18\x05 \x03(\v2*.devinsight.DeviceConfig.CapabilitiesEntryR\fcapabilities\x1a?\n" +
	"\x11CapabilitiesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"K\n" +
	"\x15RegisterAgentResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\xfb\x03\n" +
	"\x13CollectorTaskConfig\x12\x17\n" +
	"\atask_id\x18\x01 \x01(\tR\x06taskId\x12\x1b\n" +
	"\tdevice_id\x18\x02 \x01(\tR\bdeviceId\x12\x1f\n" +
	"\vdevice_name\x18\x03 \x01(\tR\n" +
	"deviceName\x12\x1f\n" +
	"\vdevice_type\x18\x04 \x01(\tR\n" +
	"deviceType\x12\x12\n" +
	"\x04host\x18\x05 \x01(\tR\x04host\x12\x12\n" +
	"\x04port\x18\x06 \x01(\x05R\x04port\x12\x1a\n" +
	"\busername\x18\a \x01(\tR\busername\x12\x1a\n" +
	"\bpassword\x18\b \x01(\tR\bpassword\x12Y\n" +
	"\x0econnect_params\x18\t \x03(\v22.devinsight.CollectorTaskConfig.ConnectParamsEntryR\rconnectParams\x12+\n" +
	"\x11frequency_seconds\x18\n" +
	" \x01(\x03R\x10frequencySeconds\x12#\n" +
	"\rcollect_items\x18\v \x03(\tR\fcollectItems\x12\x1d\n" +
	"\n" +
	"is_enabled\x18\f \x01(\bR\tisEnabled\x1a@\n" +
	"\x12ConnectParamsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\x98\x01\n" +
	"\n" +
	"TaskStatus\x12\x17\n" +
	"\atask_id\x18\x01 \x01(\tR\x06taskId\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\x12#\n" +
	"\rerror_message\x18\x03 \x01(\tR\ferrorMessage\x124\n" +
	"\x16last_collect_timestamp\x18\x04 \x01(\x03R\x14lastCollectTimestamp\"\xfb\x02\n" +
	"\n" +
	"MetricData\x12\x1b\n" +
	"\tdevice_id\x18\x01 \x01(\tR\bdeviceId\x12\x1d\n" +
	"\n" +
	"metric_key\x18\x02 \x01(\tR\tmetricKey\x12%\n" +
	"\rnumeric_value\x18\x03 \x01(\x01H\x00R\fnumericValue\x12#\n" +
	"\fstring_value\x18\x04 \x01(\tH\x00R\vstringValue\x12%\n" +
	"\rboolean_value\x18\x05 \x01(\bH\x00R\fbooleanValue\x12\x1c\n" +
	"\ttimestamp\x18\x06 \x01(\x03R\ttimestamp\x12\x1b\n" +
	"\tjson_data\x18\a \x01(\tR\bjsonData\x12:\n" +
	"\x06labels\x18\b \x03(\v2\".devinsight.MetricData.LabelsEntryR\x06labels\x1a9\n" +
	"\vLabelsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01B\f\n" +
	"\n" +
	"value_type\"\xec\x02\n" +
	"\x0fSupportedMetric\x12\x1d\n" +
	"\n" +
	"metric_key\x18\x01 \x01(\tR\tmetricKey\x12\x1f\n" +
	"\vmetric_name\x18\x02 \x01(\tR\n" +
	"metricName\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x1b\n" +
	"\tdata_type\x18\x04 \x01(\tR\bdataType\x12\x12\n" +
	"\x04unit\x18\x05 \x01(\tR\x04unit\x12E\n" +
	"\bmetadata\x18\x06 \x03(\v2).devinsight.SupportedMetric.MetadataEntryR\bmetadata\x12\x1b\n" +
	"\tis_active\x18\a \x01(\bR\bisActive\x12%\n" +
	"\x0ecollector_type\x18\b \x01(\tR\rcollectorType\x1a;\n" +
	"\rMetadataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\x89\x02\n" +
	"\bLogEntry\x12\x1b\n" +
	"\tdevice_id\x18\x01 \x01(\tR\bdeviceId\x12\x1b\n" +
	"\tlog_level\x18\x02 \x01(\tR\blogLevel\x12\x18\n" +
	"\amessage\x18\x03 \x01(\tR\amessage\x12\x1c\n" +
	"\ttimestamp\x18\x04 \x01(\x03R\ttimestamp\x12\x16\n" +
	"\x06source\x18\x05 \x01(\tR\x06source\x128\n" +
	"\x06fields\x18\x06 \x03(\v2 .devinsight.LogEntry.FieldsEntryR\x06fields\x1a9\n" +
	"\vFieldsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"r\n" +
	"\x15StreamLogDataResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12%\n" +
	"\x0ereceived_count\x18\x03 \x01(\x05R\rreceivedCount\"u\n" +
	"\x18StreamMetricDataResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12%\n" +
	"\x0ereceived_count\x18\x03 \x01(\x05R\rreceivedCount\"\xc7\x01\n" +
	"\rPluginRequest\x12\x19\n" +
	"\bagent_id\x18\x01 \x01(\tR\aagentId\x12\x1f\n" +
	"\vplugin_name\x18\x02 \x01(\tR\n" +
	"pluginName\x12%\n" +
	"\x0eplugin_version\x18\x03 \x01(\tR\rpluginVersion\x12\x1f\n" +
	"\vdevice_type\x18\x04 \x01(\tR\n" +
	"deviceType\x12\"\n" +
	"\farchitecture\x18\x05 \x01(\tR\farchitecture\x12\x0e\n" +
	"\x02os\x18\x06 \x01(\tR\x02os\"\xde\x01\n" +
	"\x0ePluginResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x126\n" +
	"\bmetadata\x18\x03 \x01(\v2\x1a.devinsight.PluginMetadataR\bmetadata\x12\x1f\n" +
	"\vplugin_path\x18\x04 \x01(\tR\n" +
	"pluginPath\x12\x1a\n" +
	"\bchecksum\x18\x05 \x01(\tR\bchecksum\x12#\n" +
	"\rabsolute_path\x18\x06 \x01(\tR\fabsolutePath\"\xa2\x04\n" +
	"\x0ePluginMetadata\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x18\n" +
	"\aversion\x18\x02 \x01(\tR\aversion\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x124\n" +
	"\x16supported_device_types\x18\x04 \x03(\tR\x14supportedDeviceTypes\x127\n" +
	"\x17supported_architectures\x18\x05 \x03(\tR\x16supportedArchitectures\x12!\n" +
	"\fsupported_os\x18\x06 \x03(\tR\vsupportedOs\x12S\n" +
	"\rconfiguration\x18\a \x03(\v2-.devinsight.PluginMetadata.ConfigurationEntryR\rconfiguration\x12\"\n" +
	"\fdependencies\x18\b \x03(\tR\fdependencies\x12\x1d\n" +
	"\n" +
	"size_bytes\x18\t \x01(\x03R\tsizeBytes\x12\x16\n" +
	"\x06author\x18\n" +
	" \x01(\tR\x06author\x12\x1d\n" +
	"\n" +
	"created_at\x18\v \x01(\x03R\tcreatedAt\x12\x1d\n" +
	"\n" +
	"updated_at\x18\f \x01(\x03R\tupdatedAt\x1a@\n" +
	"\x12ConfigurationEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\x8b\x02\n" +
	"\x18PluginUpdateNotification\x12\x19\n" +
	"\bagent_id\x18\x01 \x01(\tR\aagentId\x12\x1f\n" +
	"\vplugin_name\x18\x02 \x01(\tR\n" +
	"pluginName\x12\x1f\n" +
	"\vnew_version\x18\x03 \x01(\tR\n" +
	"newVersion\x12\x1f\n" +
	"\vold_version\x18\x04 \x01(\tR\n" +
	"oldVersion\x12!\n" +
	"\fis_mandatory\x18\x05 \x01(\bR\visMandatory\x12\x16\n" +
	"\x06reason\x18\x06 \x01(\tR\x06reason\x126\n" +
	"\bmetadata\x18\a \x01(\v2\x1a.devinsight.PluginMetadataR\bmetadata\"y\n" +
	"\x14PluginUpdateResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12-\n" +
	"\x12notification_count\x18\x03 \x01(\x05R\x11notificationCount\"r\n" +
	"\x12PluginStatusReport\x12\x19\n" +
	"\bagent_id\x18\x01 \x01(\tR\aagentId\x12A\n" +
	"\x0fplugin_statuses\x18\x02 \x03(\v2\x18.devinsight.PluginStatusR\x0epluginStatuses\"\xda\x02\n" +
	"\fPluginStatus\x12\x1f\n" +
	"\vplugin_name\x18\x01 \x01(\tR\n" +
	"pluginName\x12\x18\n" +
	"\aversion\x18\x02 \x01(\tR\aversion\x12\x16\n" +
	"\x06status\x18\x03 \x01(\tR\x06status\x12#\n" +
	"\rerror_message\x18\x04 \x01(\tR\ferrorMessage\x12.\n" +
	"\x13last_used_timestamp\x18\x05 \x01(\x03R\x11lastUsedTimestamp\x12%\n" +
	"\x0eload_timestamp\x18\x06 \x01(\x03R\rloadTimestamp\x12?\n" +
	"\ametrics\x18\a \x03(\v2%.devinsight.PluginStatus.MetricsEntryR\ametrics\x1a:\n" +
	"\fMetricsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"d\n" +
	"\x14PluginStatusResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x18\n" +
	"\aactions\x18\x03 \x03(\tR\aactions\"\xbc\x06\n" +
	"\x0ePipelineConfig\x12\x1f\n" +
	"\vpipeline_id\x18\x01 \x01(\tR\n" +
	"pipelineId\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x18\n" +
	"\aversion\x18\x04 \x01(\tR\aversion\x12\x18\n" +
	"\aenabled\x18\x05 \x01(\bR\aenabled\x12\x1f\n" +
	"\vbuffer_size\x18\x06 \x01(\x05R\n" +
	"bufferSize\x12!\n" +
	"\fworker_count\x18\a \x01(\x05R\vworkerCount\x12'\n" +
	"\x0ftimeout_seconds\x18\b \x01(\x03R\x0etimeoutSeconds\x12%\n" +
	"\x0eretry_attempts\x18\t \x01(\x05R\rretryAttempts\x12.\n" +
	"\x13retry_delay_seconds\x18\n" +
	" \x01(\x03R\x11retryDelaySeconds\x12?\n" +
	"\tcollector\x18\v \x01(\v2!.devinsight.CollectorPluginConfigR\tcollector\x12A\n" +
	"\n" +
	"processors\x18\f \x03(\v2!.devinsight.ProcessorPluginConfigR\n" +
	"processors\x12%\n" +
	"\x0eenable_metrics\x18\r \x01(\bR\renableMetrics\x12%\n" +
	"\x0eenable_tracing\x18\x0e \x01(\bR\renableTracing\x128\n" +
	"\x18metrics_interval_seconds\x18\x0f \x01(\x03R\x16metricsIntervalSeconds\x12F\n" +
	"\x0eerror_handling\x18\x10 \x01(\v2\x1f.devinsight.ErrorHandlingConfigR\rerrorHandling\x12I\n" +
	"\x0fresource_limits\x18\x11 \x01(\v2 .devinsight.ResourceLimitsConfigR\x0eresourceLimits\x12\x1d\n" +
	"\n" +
	"created_at\x18\x12 \x01(\x03R\tcreatedAt\x12\x1d\n" +
	"\n" +
	"updated_at\x18\x13 \x01(\x03R\tupdatedAt\"\x86\x02\n" +
	"\x15CollectorPluginConfig\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x12\n" +
	"\x04type\x18\x02 \x01(\tR\x04type\x12)\n" +
	"\x10interval_seconds\x18\x03 \x01(\x03R\x0fintervalSeconds\x12E\n" +
	"\x06config\x18\x04 \x03(\v2-.devinsight.CollectorPluginConfig.ConfigEntryR\x06config\x12\x18\n" +
	"\aenabled\x18\x05 \x01(\bR\aenabled\x1a9\n" +
	"\vConfigEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xbc\x02\n" +
	"\x15ProcessorPluginConfig\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x12\n" +
	"\x04type\x18\x02 \x01(\tR\x04type\x12\x18\n" +
	"\aenabled\x18\x03 \x01(\bR\aenabled\x12E\n" +
	"\x06config\x18\x04 \x03(\v2-.devinsight.ProcessorPluginConfig.ConfigEntryR\x06config\x12 \n" +
	"\vconcurrency\x18\x05 \x01(\x05R\vconcurrency\x12'\n" +
	"\x0ftimeout_seconds\x18\x06 \x01(\x03R\x0etimeoutSeconds\x12\x14\n" +
	"\x05order\x18\a \x01(\x05R\x05order\x1a9\n" +
	"\vConfigEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\x8c\x02\n" +
	"\x13ErrorHandlingConfig\x12\x1a\n" +
	"\bstrategy\x18\x01 \x01(\tR\bstrategy\x12\x1f\n" +
	"\vmax_retries\x18\x02 \x01(\x05R\n" +
	"maxRetries\x12.\n" +
	"\x13retry_delay_seconds\x18\x03 \x01(\x03R\x11retryDelaySeconds\x12I\n" +
	"\x0fcircuit_breaker\x18\x04 \x01(\v2 .devinsight.CircuitBreakerConfigR\x0ecircuitBreaker\x12=\n" +
	"\vdead_letter\x18\x05 \x01(\v2\x1c.devinsight.DeadLetterConfigR\n" +
	"deadLetter\"\xc5\x01\n" +
	"\x14CircuitBreakerConfig\x12\x18\n" +
	"\aenabled\x18\x01 \x01(\bR\aenabled\x12+\n" +
	"\x11failure_threshold\x18\x02 \x01(\x05R\x10failureThreshold\x128\n" +
	"\x18recovery_timeout_seconds\x18\x03 \x01(\x03R\x16recoveryTimeoutSeconds\x12,\n" +
	"\x12half_open_requests\x18\x04 \x01(\x05R\x10halfOpenRequests\"\x7f\n" +
	"\x10DeadLetterConfig\x12\x18\n" +
	"\aenabled\x18\x01 \x01(\bR\aenabled\x12\x1d\n" +
	"\n" +
	"queue_size\x18\x02 \x01(\x05R\tqueueSize\x12\x1e\n" +
	"\n" +
	"persistent\x18\x03 \x01(\bR\n" +
	"persistent\x12\x12\n" +
	"\x04path\x18\x04 \x01(\tR\x04path\"\xf9\x01\n" +
	"\x14ResourceLimitsConfig\x12\"\n" +
	"\rmax_memory_mb\x18\x01 \x01(\x03R\vmaxMemoryMb\x12&\n" +
	"\x0fmax_cpu_percent\x18\x02 \x01(\x05R\rmaxCpuPercent\x12%\n" +
	"\x0emax_goroutines\x18\x03 \x01(\x05R\rmaxGoroutines\x126\n" +
	"\x17process_timeout_seconds\x18\x04 \x01(\x03R\x15processTimeoutSeconds\x126\n" +
	"\x17collect_timeout_seconds\x18\x05 \x01(\x03R\x15collectTimeoutSeconds\"\x9d\x02\n" +
	"\x0ePipelineStatus\x12\x1f\n" +
	"\vpipeline_id\x18\x01 \x01(\tR\n" +
	"pipelineId\x12\x19\n" +
	"\bagent_id\x18\x02 \x01(\tR\aagentId\x12\x16\n" +
	"\x06status\x18\x03 \x01(\tR\x06status\x12#\n" +
	"\rerror_message\x18\x04 \x01(\tR\ferrorMessage\x12'\n" +
	"\x0fstart_timestamp\x18\x05 \x01(\x03R\x0estartTimestamp\x122\n" +
	"\x15last_update_timestamp\x18\x06 \x01(\x03R\x13lastUpdateTimestamp\x125\n" +
	"\ametrics\x18\a \x01(\v2\x1b.devinsight.PipelineMetricsR\ametrics\"\xc3\x05\n" +
	"\x0fPipelineMetrics\x12'\n" +
	"\x0fcollected_count\x18\x01 \x01(\x03R\x0ecollectedCount\x12'\n" +
	"\x0fprocessed_count\x18\x02 \x01(\x03R\x0eprocessedCount\x12\x1f\n" +
	"\verror_count\x18\x03 \x01(\x03R\n" +
	"errorCount\x12#\n" +
	"\rdropped_count\x18\x04 \x01(\x03R\fdroppedCount\x12$\n" +
	"\x0eavg_latency_ms\x18\x05 \x01(\x03R\favgLatencyMs\x12$\n" +
	"\x0ep95_latency_ms\x18\x06 \x01(\x03R\fp95LatencyMs\x12$\n" +
	"\x0ep99_latency_ms\x18\a \x01(\x03R\fp99LatencyMs\x12\x1e\n" +
	"\n" +
	"throughput\x18\b \x01(\x01R\n" +
	"throughput\x12&\n" +
	"\x0fmemory_usage_mb\x18\t \x01(\x03R\rmemoryUsageMb\x12*\n" +
	"\x11cpu_usage_percent\x18\n" +
	" \x01(\x01R\x0fcpuUsagePercent\x12'\n" +
	"\x0fgoroutine_count\x18\v \x01(\x05R\x0egoroutineCount\x12!\n" +
	"\fchannel_size\x18\f \x01(\x05R\vchannelSize\x12U\n" +
	"\x0eplugin_metrics\x18\r \x03(\v2..devinsight.PipelineMetrics.PluginMetricsEntryR\rpluginMetrics\x122\n" +
	"\x15last_update_timestamp\x18\x0e \x01(\x03R\x13lastUpdateTimestamp\x1a[\n" +
	"\x12PluginMetricsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12/\n" +
	"\x05value\x18\x02 \x01(\v2\x19.devinsight.PluginMetricsR\x05value:\x028\x01\"\xf8\x02\n" +
	"\rPluginMetrics\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12'\n" +
	"\x0fprocessed_count\x18\x02 \x01(\x03R\x0eprocessedCount\x12\x1f\n" +
	"\verror_count\x18\x03 \x01(\x03R\n" +
	"errorCount\x12$\n" +
	"\x0eavg_latency_ms\x18\x04 \x01(\x03R\favgLatencyMs\x124\n" +
	"\x16last_process_timestamp\x18\x05 \x01(\x03R\x14lastProcessTimestamp\x12\x16\n" +
	"\x06status\x18\x06 \x01(\tR\x06status\x12S\n" +
	"\x0ecustom_metrics\x18\a \x03(\v2,.devinsight.PluginMetrics.CustomMetricsEntryR\rcustomMetrics\x1a@\n" +
	"\x12CustomMetricsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"u\n" +
	"\x17PipelineTemplateRequest\x12\x19\n" +
	"\bagent_id\x18\x01 \x01(\tR\aagentId\x12#\n" +
	"\rtemplate_type\x18\x02 \x01(\tR\ftemplateType\x12\x1a\n" +
	"\bcategory\x18\x03 \x01(\tR\bcategory\"\x8a\x01\n" +
	"\x18PipelineTemplateResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12:\n" +
	"\ttemplates\x18\x03 \x03(\v2\x1c.devinsight.PipelineTemplateR\ttemplates\"\xa6\x03\n" +
	"\x10PipelineTemplate\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x1a\n" +
	"\bcategory\x18\x04 \x01(\tR\bcategory\x12\x12\n" +
	"\x04tags\x18\x05 \x03(\tR\x04tags\x12C\n" +
	"\x0fconfig_template\x18\x06 \x01(\v2\x1a.devinsight.PipelineConfigR\x0econfigTemplate\x12V\n" +
	"\x0edefault_values\x18\a \x03(\v2/.devinsight.PipelineTemplate.DefaultValuesEntryR\rdefaultValues\x12=\n" +
	"\n" +
	"parameters\x18\b \x03(\v2\x1d.devinsight.TemplateParameterR\n" +
	"parameters\x1a@\n" +
	"\x12DefaultValuesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xc5\x01\n" +
	"\x11TemplateParameter\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x12\n" +
	"\x04type\x18\x02 \x01(\tR\x04type\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x1a\n" +
	"\brequired\x18\x04 \x01(\bR\brequired\x12#\n" +
	"\rdefault_value\x18\x05 \x01(\tR\fdefaultValue\x12%\n" +
	"\x0eallowed_values\x18\x06 \x03(\tR\rallowedValues\"p\n" +
	"\x1fPipelineConfigValidationRequest\x12\x19\n" +
	"\bagent_id\x18\x01 \x01(\tR\aagentId\x122\n" +
	"\x06config\x18\x02 \x01(\v2\x1a.devinsight.PipelineConfigR\x06config\"\xc2\x01\n" +
	" PipelineConfigValidationResponse\x12\x14\n" +
	"\x05valid\x18\x01 \x01(\bR\x05valid\x123\n" +
	"\x06errors\x18\x02 \x03(\v2\x1b.devinsight.ValidationErrorR\x06errors\x129\n" +
	"\bwarnings\x18\x03 \x03(\v2\x1d.devinsight.ValidationWarningR\bwarnings\x12\x18\n" +
	"\amessage\x18\x04 \x01(\tR\amessage\"U\n" +
	"\x0fValidationError\x12\x14\n" +
	"\x05field\x18\x01 \x01(\tR\x05field\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x12\n" +
	"\x04code\x18\x03 \x01(\tR\x04code\"W\n" +
	"\x11ValidationWarning\x12\x14\n" +
	"\x05field\x18\x01 \x01(\tR\x05field\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x12\n" +
	"\x04code\x18\x03 \x01(\tR\x04code2\x8b\a\n" +
	"\fAgentService\x12V\n" +
	"\rRegisterAgent\x12 .devinsight.RegisterAgentRequest\x1a!.devinsight.RegisterAgentResponse\"\x00\x12U\n" +
	"\x14StreamCollectorTasks\x12\x16.devinsight.TaskStatus\x1a\x1f.devinsight.CollectorTaskConfig\"\x00(\x010\x01\x12O\n" +
	"\x0fStreamPipelines\x12\x1a.devinsight.PipelineStatus\x1a\x1a.devinsight.PipelineConfig\"\x00(\x010\x01\x12T\n" +
	"\x10StreamMetricData\x12\x16.devinsight.MetricData\x1a$.devinsight.StreamMetricDataResponse\"\x00(\x01\x12L\n" +
	"\rStreamLogData\x12\x14.devinsight.LogEntry\x1a!.devinsight.StreamLogDataResponse\"\x00(\x01\x12H\n" +
	"\rRequestPlugin\x12\x19.devinsight.PluginRequest\x1a\x1a.devinsight.PluginResponse\"\x00\x12W\n" +
	"\x13StreamPluginUpdates\x12\x16.google.protobuf.Empty\x1a$.devinsight.PluginUpdateNotification\"\x000\x01\x12X\n" +
	"\x12ReportPluginStatus\x12\x1e.devinsight.PluginStatusReport\x1a .devinsight.PluginStatusResponse\"\x00\x12c\n" +
	"\x14GetPipelineTemplates\x12#.devinsight.PipelineTemplateRequest\x1a$.devinsight.PipelineTemplateResponse\"\x00\x12u\n" +
	"\x16ValidatePipelineConfig\x12+.devinsight.PipelineConfigValidationRequest\x1a,.devinsight.PipelineConfigValidationResponse\"\x00B\x11Z\x0faiops/pkg/protob\x06proto3"

var (
	file_pkg_proto_devinsight_proto_rawDescOnce sync.Once
	file_pkg_proto_devinsight_proto_rawDescData []byte
)

func file_pkg_proto_devinsight_proto_rawDescGZIP() []byte {
	file_pkg_proto_devinsight_proto_rawDescOnce.Do(func() {
		file_pkg_proto_devinsight_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pkg_proto_devinsight_proto_rawDesc), len(file_pkg_proto_devinsight_proto_rawDesc)))
	})
	return file_pkg_proto_devinsight_proto_rawDescData
}

var file_pkg_proto_devinsight_proto_msgTypes = make([]protoimpl.MessageInfo, 48)
var file_pkg_proto_devinsight_proto_goTypes = []any{
	(*RegisterAgentRequest)(nil),             // 0: devinsight.RegisterAgentRequest
	(*DeviceConfig)(nil),                     // 1: devinsight.DeviceConfig
	(*RegisterAgentResponse)(nil),            // 2: devinsight.RegisterAgentResponse
	(*CollectorTaskConfig)(nil),              // 3: devinsight.CollectorTaskConfig
	(*TaskStatus)(nil),                       // 4: devinsight.TaskStatus
	(*MetricData)(nil),                       // 5: devinsight.MetricData
	(*SupportedMetric)(nil),                  // 6: devinsight.SupportedMetric
	(*LogEntry)(nil),                         // 7: devinsight.LogEntry
	(*StreamLogDataResponse)(nil),            // 8: devinsight.StreamLogDataResponse
	(*StreamMetricDataResponse)(nil),         // 9: devinsight.StreamMetricDataResponse
	(*PluginRequest)(nil),                    // 10: devinsight.PluginRequest
	(*PluginResponse)(nil),                   // 11: devinsight.PluginResponse
	(*PluginMetadata)(nil),                   // 12: devinsight.PluginMetadata
	(*PluginUpdateNotification)(nil),         // 13: devinsight.PluginUpdateNotification
	(*PluginUpdateResponse)(nil),             // 14: devinsight.PluginUpdateResponse
	(*PluginStatusReport)(nil),               // 15: devinsight.PluginStatusReport
	(*PluginStatus)(nil),                     // 16: devinsight.PluginStatus
	(*PluginStatusResponse)(nil),             // 17: devinsight.PluginStatusResponse
	(*PipelineConfig)(nil),                   // 18: devinsight.PipelineConfig
	(*CollectorPluginConfig)(nil),            // 19: devinsight.CollectorPluginConfig
	(*ProcessorPluginConfig)(nil),            // 20: devinsight.ProcessorPluginConfig
	(*ErrorHandlingConfig)(nil),              // 21: devinsight.ErrorHandlingConfig
	(*CircuitBreakerConfig)(nil),             // 22: devinsight.CircuitBreakerConfig
	(*DeadLetterConfig)(nil),                 // 23: devinsight.DeadLetterConfig
	(*ResourceLimitsConfig)(nil),             // 24: devinsight.ResourceLimitsConfig
	(*PipelineStatus)(nil),                   // 25: devinsight.PipelineStatus
	(*PipelineMetrics)(nil),                  // 26: devinsight.PipelineMetrics
	(*PluginMetrics)(nil),                    // 27: devinsight.PluginMetrics
	(*PipelineTemplateRequest)(nil),          // 28: devinsight.PipelineTemplateRequest
	(*PipelineTemplateResponse)(nil),         // 29: devinsight.PipelineTemplateResponse
	(*PipelineTemplate)(nil),                 // 30: devinsight.PipelineTemplate
	(*TemplateParameter)(nil),                // 31: devinsight.TemplateParameter
	(*PipelineConfigValidationRequest)(nil),  // 32: devinsight.PipelineConfigValidationRequest
	(*PipelineConfigValidationResponse)(nil), // 33: devinsight.PipelineConfigValidationResponse
	(*ValidationError)(nil),                  // 34: devinsight.ValidationError
	(*ValidationWarning)(nil),                // 35: devinsight.ValidationWarning
	nil,                                      // 36: devinsight.DeviceConfig.CapabilitiesEntry
	nil,                                      // 37: devinsight.CollectorTaskConfig.ConnectParamsEntry
	nil,                                      // 38: devinsight.MetricData.LabelsEntry
	nil,                                      // 39: devinsight.SupportedMetric.MetadataEntry
	nil,                                      // 40: devinsight.LogEntry.FieldsEntry
	nil,                                      // 41: devinsight.PluginMetadata.ConfigurationEntry
	nil,                                      // 42: devinsight.PluginStatus.MetricsEntry
	nil,                                      // 43: devinsight.CollectorPluginConfig.ConfigEntry
	nil,                                      // 44: devinsight.ProcessorPluginConfig.ConfigEntry
	nil,                                      // 45: devinsight.PipelineMetrics.PluginMetricsEntry
	nil,                                      // 46: devinsight.PluginMetrics.CustomMetricsEntry
	nil,                                      // 47: devinsight.PipelineTemplate.DefaultValuesEntry
	(*emptypb.Empty)(nil),                    // 48: google.protobuf.Empty
}
var file_pkg_proto_devinsight_proto_depIdxs = []int32{
	1,  // 0: devinsight.RegisterAgentRequest.device_config:type_name -> devinsight.DeviceConfig
	36, // 1: devinsight.DeviceConfig.capabilities:type_name -> devinsight.DeviceConfig.CapabilitiesEntry
	37, // 2: devinsight.CollectorTaskConfig.connect_params:type_name -> devinsight.CollectorTaskConfig.ConnectParamsEntry
	38, // 3: devinsight.MetricData.labels:type_name -> devinsight.MetricData.LabelsEntry
	39, // 4: devinsight.SupportedMetric.metadata:type_name -> devinsight.SupportedMetric.MetadataEntry
	40, // 5: devinsight.LogEntry.fields:type_name -> devinsight.LogEntry.FieldsEntry
	12, // 6: devinsight.PluginResponse.metadata:type_name -> devinsight.PluginMetadata
	41, // 7: devinsight.PluginMetadata.configuration:type_name -> devinsight.PluginMetadata.ConfigurationEntry
	12, // 8: devinsight.PluginUpdateNotification.metadata:type_name -> devinsight.PluginMetadata
	16, // 9: devinsight.PluginStatusReport.plugin_statuses:type_name -> devinsight.PluginStatus
	42, // 10: devinsight.PluginStatus.metrics:type_name -> devinsight.PluginStatus.MetricsEntry
	19, // 11: devinsight.PipelineConfig.collector:type_name -> devinsight.CollectorPluginConfig
	20, // 12: devinsight.PipelineConfig.processors:type_name -> devinsight.ProcessorPluginConfig
	21, // 13: devinsight.PipelineConfig.error_handling:type_name -> devinsight.ErrorHandlingConfig
	24, // 14: devinsight.PipelineConfig.resource_limits:type_name -> devinsight.ResourceLimitsConfig
	43, // 15: devinsight.CollectorPluginConfig.config:type_name -> devinsight.CollectorPluginConfig.ConfigEntry
	44, // 16: devinsight.ProcessorPluginConfig.config:type_name -> devinsight.ProcessorPluginConfig.ConfigEntry
	22, // 17: devinsight.ErrorHandlingConfig.circuit_breaker:type_name -> devinsight.CircuitBreakerConfig
	23, // 18: devinsight.ErrorHandlingConfig.dead_letter:type_name -> devinsight.DeadLetterConfig
	26, // 19: devinsight.PipelineStatus.metrics:type_name -> devinsight.PipelineMetrics
	45, // 20: devinsight.PipelineMetrics.plugin_metrics:type_name -> devinsight.PipelineMetrics.PluginMetricsEntry
	46, // 21: devinsight.PluginMetrics.custom_metrics:type_name -> devinsight.PluginMetrics.CustomMetricsEntry
	30, // 22: devinsight.PipelineTemplateResponse.templates:type_name -> devinsight.PipelineTemplate
	18, // 23: devinsight.PipelineTemplate.config_template:type_name -> devinsight.PipelineConfig
	47, // 24: devinsight.PipelineTemplate.default_values:type_name -> devinsight.PipelineTemplate.DefaultValuesEntry
	31, // 25: devinsight.PipelineTemplate.parameters:type_name -> devinsight.TemplateParameter
	18, // 26: devinsight.PipelineConfigValidationRequest.config:type_name -> devinsight.PipelineConfig
	34, // 27: devinsight.PipelineConfigValidationResponse.errors:type_name -> devinsight.ValidationError
	35, // 28: devinsight.PipelineConfigValidationResponse.warnings:type_name -> devinsight.ValidationWarning
	27, // 29: devinsight.PipelineMetrics.PluginMetricsEntry.value:type_name -> devinsight.PluginMetrics
	0,  // 30: devinsight.AgentService.RegisterAgent:input_type -> devinsight.RegisterAgentRequest
	4,  // 31: devinsight.AgentService.StreamCollectorTasks:input_type -> devinsight.TaskStatus
	25, // 32: devinsight.AgentService.StreamPipelines:input_type -> devinsight.PipelineStatus
	5,  // 33: devinsight.AgentService.StreamMetricData:input_type -> devinsight.MetricData
	7,  // 34: devinsight.AgentService.StreamLogData:input_type -> devinsight.LogEntry
	10, // 35: devinsight.AgentService.RequestPlugin:input_type -> devinsight.PluginRequest
	48, // 36: devinsight.AgentService.StreamPluginUpdates:input_type -> google.protobuf.Empty
	15, // 37: devinsight.AgentService.ReportPluginStatus:input_type -> devinsight.PluginStatusReport
	28, // 38: devinsight.AgentService.GetPipelineTemplates:input_type -> devinsight.PipelineTemplateRequest
	32, // 39: devinsight.AgentService.ValidatePipelineConfig:input_type -> devinsight.PipelineConfigValidationRequest
	2,  // 40: devinsight.AgentService.RegisterAgent:output_type -> devinsight.RegisterAgentResponse
	3,  // 41: devinsight.AgentService.StreamCollectorTasks:output_type -> devinsight.CollectorTaskConfig
	18, // 42: devinsight.AgentService.StreamPipelines:output_type -> devinsight.PipelineConfig
	9,  // 43: devinsight.AgentService.StreamMetricData:output_type -> devinsight.StreamMetricDataResponse
	8,  // 44: devinsight.AgentService.StreamLogData:output_type -> devinsight.StreamLogDataResponse
	11, // 45: devinsight.AgentService.RequestPlugin:output_type -> devinsight.PluginResponse
	13, // 46: devinsight.AgentService.StreamPluginUpdates:output_type -> devinsight.PluginUpdateNotification
	17, // 47: devinsight.AgentService.ReportPluginStatus:output_type -> devinsight.PluginStatusResponse
	29, // 48: devinsight.AgentService.GetPipelineTemplates:output_type -> devinsight.PipelineTemplateResponse
	33, // 49: devinsight.AgentService.ValidatePipelineConfig:output_type -> devinsight.PipelineConfigValidationResponse
	40, // [40:50] is the sub-list for method output_type
	30, // [30:40] is the sub-list for method input_type
	30, // [30:30] is the sub-list for extension type_name
	30, // [30:30] is the sub-list for extension extendee
	0,  // [0:30] is the sub-list for field type_name
}

func init() { file_pkg_proto_devinsight_proto_init() }
func file_pkg_proto_devinsight_proto_init() {
	if File_pkg_proto_devinsight_proto != nil {
		return
	}
	file_pkg_proto_devinsight_proto_msgTypes[5].OneofWrappers = []any{
		(*MetricData_NumericValue)(nil),
		(*MetricData_StringValue)(nil),
		(*MetricData_BooleanValue)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pkg_proto_devinsight_proto_rawDesc), len(file_pkg_proto_devinsight_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   48,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pkg_proto_devinsight_proto_goTypes,
		DependencyIndexes: file_pkg_proto_devinsight_proto_depIdxs,
		MessageInfos:      file_pkg_proto_devinsight_proto_msgTypes,
	}.Build()
	File_pkg_proto_devinsight_proto = out.File
	file_pkg_proto_devinsight_proto_goTypes = nil
	file_pkg_proto_devinsight_proto_depIdxs = nil
}
