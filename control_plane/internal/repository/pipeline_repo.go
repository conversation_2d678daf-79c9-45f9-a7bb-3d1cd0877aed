package repository

import (
	"aiops/control_plane/internal/model"
	"time"

	"gorm.io/gorm"
)

// PipelineRepository 流水线配置仓库接口
type PipelineRepository interface {
	// 基础CRUD操作
	Create(config *model.PipelineConfig) error
	GetByID(pipelineID string) (*model.PipelineConfig, error)
	GetByAgentID(agentID string) ([]*model.PipelineConfig, error)
	Update(config *model.PipelineConfig) error
	Delete(pipelineID string) error
	List(offset, limit int) ([]*model.PipelineConfig, error)

	// 状态管理
	UpdateStatus(pipelineID, status string) error
	GetActiveConfigs() ([]*model.PipelineConfig, error)

	// 查询操作
	FindByName(name string) (*model.PipelineConfig, error)
	FindByStatus(status string) ([]*model.PipelineConfig, error)
	Count() (int64, error)
}

// PipelineStatusRepository 流水线状态仓库接口
type PipelineStatusRepository interface {
	Create(status *model.PipelineStatus) error
	Update(status *model.PipelineStatus) error
	GetByPipelineID(pipelineID string) (*model.PipelineStatus, error)
	GetByAgentID(agentID string) ([]*model.PipelineStatus, error)
	Delete(pipelineID string) error
	List(offset, limit int) ([]*model.PipelineStatus, error)
}

// PipelineTemplateRepository 流水线模板仓库接口
type PipelineTemplateRepository interface {
	Create(template *model.PipelineTemplate) error
	GetByID(templateID string) (*model.PipelineTemplate, error)
	Update(template *model.PipelineTemplate) error
	Delete(templateID string) error
	List(offset, limit int) ([]*model.PipelineTemplate, error)
	FindByCategory(category string) ([]*model.PipelineTemplate, error)
	GetActive() ([]*model.PipelineTemplate, error)
}

// PipelineMetricsRepository 流水线指标仓库接口
type PipelineMetricsRepository interface {
	Create(metrics *model.PipelineMetrics) error
	GetLatestByPipelineID(pipelineID string) (*model.PipelineMetrics, error)
	GetByPipelineIDAndTimeRange(pipelineID string, startTime, endTime int64) ([]*model.PipelineMetrics, error)
	DeleteOldMetrics(beforeTimestamp int64) error
	GetAggregatedMetrics(pipelineID string, interval string, startTime, endTime int64) ([]*model.PipelineMetrics, error)
}

// PipelineEventRepository 流水线事件仓库接口
type PipelineEventRepository interface {
	Create(event *model.PipelineEvent) error
	GetByPipelineID(pipelineID string, offset, limit int) ([]*model.PipelineEvent, error)
	GetByEventType(eventType string, offset, limit int) ([]*model.PipelineEvent, error)
	GetByTimeRange(startTime, endTime int64, offset, limit int) ([]*model.PipelineEvent, error)
	DeleteOldEvents(beforeTimestamp int64) error
}

// ==================== 实现 ====================

// pipelineRepository 流水线配置仓库实现
type pipelineRepository struct {
	db *gorm.DB
}

// NewPipelineRepository 创建流水线配置仓库
func NewPipelineRepository(db *gorm.DB) PipelineRepository {
	return &pipelineRepository{db: db}
}

func (r *pipelineRepository) Create(config *model.PipelineConfig) error {
	return r.db.Create(config).Error
}

func (r *pipelineRepository) GetByID(pipelineID string) (*model.PipelineConfig, error) {
	var config model.PipelineConfig
	err := r.db.Where("pipeline_id = ?", pipelineID).First(&config).Error
	if err != nil {
		return nil, err
	}
	return &config, nil
}

func (r *pipelineRepository) GetByAgentID(agentID string) ([]*model.PipelineConfig, error) {
	var configs []*model.PipelineConfig
	err := r.db.Where("agent_id = ?", agentID).Find(&configs).Error
	return configs, err
}

func (r *pipelineRepository) Update(config *model.PipelineConfig) error {
	return r.db.Save(config).Error
}

func (r *pipelineRepository) Delete(pipelineID string) error {
	return r.db.Where("pipeline_id = ?", pipelineID).Delete(&model.PipelineConfig{}).Error
}

func (r *pipelineRepository) List(offset, limit int) ([]*model.PipelineConfig, error) {
	var configs []*model.PipelineConfig
	err := r.db.Offset(offset).Limit(limit).Find(&configs).Error
	return configs, err
}

func (r *pipelineRepository) UpdateStatus(pipelineID, status string) error {
	updates := map[string]interface{}{
		"status": status,
	}

	if status == "running" {
		now := time.Now()
		updates["last_start_time"] = &now
	} else if status == "stopped" {
		now := time.Now()
		updates["last_stop_time"] = &now
	}

	return r.db.Model(&model.PipelineConfig{}).
		Where("pipeline_id = ?", pipelineID).
		Updates(updates).Error
}

func (r *pipelineRepository) GetActiveConfigs() ([]*model.PipelineConfig, error) {
	var configs []*model.PipelineConfig
	err := r.db.Where("enabled = ?", true).Find(&configs).Error
	return configs, err
}

func (r *pipelineRepository) FindByName(name string) (*model.PipelineConfig, error) {
	var config model.PipelineConfig
	err := r.db.Where("name = ?", name).First(&config).Error
	if err != nil {
		return nil, err
	}
	return &config, nil
}

func (r *pipelineRepository) FindByStatus(status string) ([]*model.PipelineConfig, error) {
	var configs []*model.PipelineConfig
	err := r.db.Where("status = ?", status).Find(&configs).Error
	return configs, err
}

func (r *pipelineRepository) Count() (int64, error) {
	var count int64
	err := r.db.Model(&model.PipelineConfig{}).Count(&count).Error
	return count, err
}

// pipelineStatusRepository 流水线状态仓库实现
type pipelineStatusRepository struct {
	db *gorm.DB
}

// NewPipelineStatusRepository 创建流水线状态仓库
func NewPipelineStatusRepository(db *gorm.DB) PipelineStatusRepository {
	return &pipelineStatusRepository{db: db}
}

func (r *pipelineStatusRepository) Create(status *model.PipelineStatus) error {
	return r.db.Create(status).Error
}

func (r *pipelineStatusRepository) Update(status *model.PipelineStatus) error {
	return r.db.Save(status).Error
}

func (r *pipelineStatusRepository) GetByPipelineID(pipelineID string) (*model.PipelineStatus, error) {
	var status model.PipelineStatus
	err := r.db.Where("pipeline_id = ?", pipelineID).First(&status).Error
	if err != nil {
		return nil, err
	}
	return &status, nil
}

func (r *pipelineStatusRepository) GetByAgentID(agentID string) ([]*model.PipelineStatus, error) {
	var statuses []*model.PipelineStatus
	err := r.db.Where("agent_id = ?", agentID).Find(&statuses).Error
	return statuses, err
}

func (r *pipelineStatusRepository) Delete(pipelineID string) error {
	return r.db.Where("pipeline_id = ?", pipelineID).Delete(&model.PipelineStatus{}).Error
}

func (r *pipelineStatusRepository) List(offset, limit int) ([]*model.PipelineStatus, error) {
	var statuses []*model.PipelineStatus
	err := r.db.Offset(offset).Limit(limit).Find(&statuses).Error
	return statuses, err
}

// pipelineTemplateRepository 流水线模板仓库实现
type pipelineTemplateRepository struct {
	db *gorm.DB
}

// NewPipelineTemplateRepository 创建流水线模板仓库
func NewPipelineTemplateRepository(db *gorm.DB) PipelineTemplateRepository {
	return &pipelineTemplateRepository{db: db}
}

func (r *pipelineTemplateRepository) Create(template *model.PipelineTemplate) error {
	return r.db.Create(template).Error
}

func (r *pipelineTemplateRepository) GetByID(templateID string) (*model.PipelineTemplate, error) {
	var template model.PipelineTemplate
	err := r.db.Where("template_id = ?", templateID).First(&template).Error
	if err != nil {
		return nil, err
	}
	return &template, nil
}

func (r *pipelineTemplateRepository) Update(template *model.PipelineTemplate) error {
	return r.db.Save(template).Error
}

func (r *pipelineTemplateRepository) Delete(templateID string) error {
	return r.db.Where("template_id = ?", templateID).Delete(&model.PipelineTemplate{}).Error
}

func (r *pipelineTemplateRepository) List(offset, limit int) ([]*model.PipelineTemplate, error) {
	var templates []*model.PipelineTemplate
	err := r.db.Offset(offset).Limit(limit).Find(&templates).Error
	return templates, err
}

func (r *pipelineTemplateRepository) FindByCategory(category string) ([]*model.PipelineTemplate, error) {
	var templates []*model.PipelineTemplate
	err := r.db.Where("category = ? AND is_active = ?", category, true).Find(&templates).Error
	return templates, err
}

func (r *pipelineTemplateRepository) GetActive() ([]*model.PipelineTemplate, error) {
	var templates []*model.PipelineTemplate
	err := r.db.Where("is_active = ?", true).Find(&templates).Error
	return templates, err
}

// pipelineMetricsRepository 流水线指标仓库实现
type pipelineMetricsRepository struct {
	db *gorm.DB
}

// NewPipelineMetricsRepository 创建流水线指标仓库
func NewPipelineMetricsRepository(db *gorm.DB) PipelineMetricsRepository {
	return &pipelineMetricsRepository{db: db}
}

func (r *pipelineMetricsRepository) Create(metrics *model.PipelineMetrics) error {
	return r.db.Create(metrics).Error
}

func (r *pipelineMetricsRepository) GetLatestByPipelineID(pipelineID string) (*model.PipelineMetrics, error) {
	var metrics model.PipelineMetrics
	err := r.db.Where("pipeline_id = ?", pipelineID).
		Order("timestamp DESC").
		First(&metrics).Error
	if err != nil {
		return nil, err
	}
	return &metrics, nil
}

func (r *pipelineMetricsRepository) GetByPipelineIDAndTimeRange(pipelineID string, startTime, endTime int64) ([]*model.PipelineMetrics, error) {
	var metrics []*model.PipelineMetrics
	err := r.db.Where("pipeline_id = ? AND timestamp >= ? AND timestamp <= ?", pipelineID, startTime, endTime).
		Order("timestamp ASC").
		Find(&metrics).Error
	return metrics, err
}

func (r *pipelineMetricsRepository) DeleteOldMetrics(beforeTimestamp int64) error {
	return r.db.Where("timestamp < ?", beforeTimestamp).Delete(&model.PipelineMetrics{}).Error
}

func (r *pipelineMetricsRepository) GetAggregatedMetrics(pipelineID string, interval string, startTime, endTime int64) ([]*model.PipelineMetrics, error) {
	// 简化实现，实际应该根据interval进行聚合
	return r.GetByPipelineIDAndTimeRange(pipelineID, startTime, endTime)
}

// pipelineEventRepository 流水线事件仓库实现
type pipelineEventRepository struct {
	db *gorm.DB
}

// NewPipelineEventRepository 创建流水线事件仓库
func NewPipelineEventRepository(db *gorm.DB) PipelineEventRepository {
	return &pipelineEventRepository{db: db}
}

func (r *pipelineEventRepository) Create(event *model.PipelineEvent) error {
	return r.db.Create(event).Error
}

func (r *pipelineEventRepository) GetByPipelineID(pipelineID string, offset, limit int) ([]*model.PipelineEvent, error) {
	var events []*model.PipelineEvent
	err := r.db.Where("pipeline_id = ?", pipelineID).
		Order("timestamp DESC").
		Offset(offset).Limit(limit).
		Find(&events).Error
	return events, err
}

func (r *pipelineEventRepository) GetByEventType(eventType string, offset, limit int) ([]*model.PipelineEvent, error) {
	var events []*model.PipelineEvent
	err := r.db.Where("event_type = ?", eventType).
		Order("timestamp DESC").
		Offset(offset).Limit(limit).
		Find(&events).Error
	return events, err
}

func (r *pipelineEventRepository) GetByTimeRange(startTime, endTime int64, offset, limit int) ([]*model.PipelineEvent, error) {
	var events []*model.PipelineEvent
	err := r.db.Where("timestamp >= ? AND timestamp <= ?", startTime, endTime).
		Order("timestamp DESC").
		Offset(offset).Limit(limit).
		Find(&events).Error
	return events, err
}

func (r *pipelineEventRepository) DeleteOldEvents(beforeTimestamp int64) error {
	return r.db.Where("timestamp < ?", beforeTimestamp).Delete(&model.PipelineEvent{}).Error
}
