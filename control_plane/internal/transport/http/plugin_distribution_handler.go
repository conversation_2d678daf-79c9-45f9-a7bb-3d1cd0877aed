package http

import (
	"net/http"

	"aiops/control_plane/internal/service"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// PluginDistributionHandler 插件分发相关的 HTTP 处理器
// 注意：这个处理器只负责插件分发管理，不提供本地插件执行功能
type PluginDistributionHandler struct {
	pluginRegistryService *service.PluginRegistryService
	logger                *zap.Logger
}

// NewPluginDistributionHandler 创建新的插件分发处理器
func NewPluginDistributionHandler(pluginRegistryService *service.PluginRegistryService, logger *zap.Logger) *PluginDistributionHandler {
	return &PluginDistributionHandler{
		pluginRegistryService: pluginRegistryService,
		logger:                logger,
	}
}

// RegisterRoutes 注册插件分发相关路由
func (h *PluginDistributionHandler) RegisterRoutes(router *gin.RouterGroup) {
	plugins := router.Group("/plugins")
	{
		// 插件注册表管理
		plugins.GET("/registry", h.ListRegisteredPlugins)
		plugins.POST("/registry", h.RegisterPlugin)
		plugins.GET("/registry/:id", h.GetRegisteredPlugin)
		plugins.DELETE("/registry/:id", h.UnregisterPlugin)

		// 插件分发状态
		plugins.GET("/distribution/status", h.GetDistributionStatus)
		plugins.GET("/distribution/agents/:agentId", h.GetAgentPlugins)

		// 简化的健康检查（仅检查分发服务状态）
		plugins.GET("/distribution/health", h.CheckDistributionHealth)
	}
}

// ListRegisteredPlugins 列出已注册的插件
// @Summary 列出已注册的插件
// @Tags plugin-distribution
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/plugins/registry [get]
func (h *PluginDistributionHandler) ListRegisteredPlugins(c *gin.Context) {
	// TODO: 实现从插件注册服务获取插件列表
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "插件分发模式：control_plane 不再本地加载插件",
		"data": gin.H{
			"plugins": []interface{}{},
			"count":   0,
			"note":    "插件现在由 agent 端加载，control_plane 仅负责分发",
		},
	})
}

// RegisterPlugin 注册新插件到分发系统
// @Summary 注册插件
// @Tags plugin-distribution
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/plugins/registry [post]
func (h *PluginDistributionHandler) RegisterPlugin(c *gin.Context) {
	// TODO: 实现插件注册逻辑
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "插件注册功能待实现",
	})
}

// GetRegisteredPlugin 获取指定插件信息
// @Summary 获取插件信息
// @Tags plugin-distribution
// @Param id path string true "插件ID"
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/plugins/registry/{id} [get]
func (h *PluginDistributionHandler) GetRegisteredPlugin(c *gin.Context) {
	pluginID := c.Param("id")
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"plugin_id": pluginID,
			"message":   "插件信息查询功能待实现",
		},
	})
}

// UnregisterPlugin 注销插件
// @Summary 注销插件
// @Tags plugin-distribution
// @Param id path string true "插件ID"
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/plugins/registry/{id} [delete]
func (h *PluginDistributionHandler) UnregisterPlugin(c *gin.Context) {
	pluginID := c.Param("id")
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "插件注销功能待实现",
		"data": gin.H{
			"plugin_id": pluginID,
		},
	})
}

// GetDistributionStatus 获取插件分发状态
// @Summary 获取分发状态
// @Tags plugin-distribution
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/plugins/distribution/status [get]
func (h *PluginDistributionHandler) GetDistributionStatus(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"distribution_service": "active",
			"total_agents":         0,
			"total_distributions":  0,
			"message":              "插件分发服务运行正常",
		},
	})
}

// GetAgentPlugins 获取指定 Agent 的插件状态
// @Summary 获取 Agent 插件状态
// @Tags plugin-distribution
// @Param agentId path string true "Agent ID"
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/plugins/distribution/agents/{agentId} [get]
func (h *PluginDistributionHandler) GetAgentPlugins(c *gin.Context) {
	agentID := c.Param("agentId")
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"agent_id": agentID,
			"plugins":  []interface{}{},
			"message":  "Agent 插件状态查询功能待实现",
		},
	})
}

// CheckDistributionHealth 检查插件分发服务健康状态
// @Summary 检查分发服务健康状态
// @Tags plugin-distribution
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/plugins/distribution/health [get]
func (h *PluginDistributionHandler) CheckDistributionHealth(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"healthy": true,
		"data": gin.H{
			"service":           "plugin-distribution",
			"status":            "healthy",
			"local_plugins":     "disabled",
			"distribution_mode": "enabled",
			"message":           "插件分发服务运行正常，本地插件加载已禁用",
		},
	})
}
