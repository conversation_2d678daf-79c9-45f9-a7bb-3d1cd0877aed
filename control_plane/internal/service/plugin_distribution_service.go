package service

import (
	"context"
	"crypto/md5"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"aiops/pkg/proto"

	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/types/known/emptypb"
)

// PluginDistributionService 处理插件分发相关的 gRPC 请求
type PluginDistributionService struct {
	logger          *zap.Logger
	registryService *PluginRegistryService
	pluginStorePath string
	maxPluginSizeMB int64
	enableStreaming bool
	proto.UnimplementedAgentServiceServer
}

// NewPluginDistributionService 创建新的插件分发服务
func NewPluginDistributionService(
	logger *zap.Logger,
	registryService *PluginRegistryService,
	pluginStorePath string,
	maxPluginSizeMB int64,
) *PluginDistributionService {
	return &PluginDistributionService{
		logger:          logger,
		registryService: registryService,
		pluginStorePath: pluginStorePath,
		maxPluginSizeMB: maxPluginSizeMB,
		enableStreaming: true,
	}
}

// RequestPlugin 处理 Agent 的插件请求
func (s *PluginDistributionService) RequestPlugin(
	ctx context.Context,
	req *proto.PluginRequest,
) (*proto.PluginResponse, error) {
	s.logger.Info("收到插件请求",
		zap.String("agent_id", req.AgentId),
		zap.String("plugin_name", req.PluginName),
		zap.String("plugin_version", req.PluginVersion),
		zap.String("device_type", req.DeviceType),
		zap.String("architecture", req.Architecture),
		zap.String("os", req.Os))

	// 验证请求参数
	if req.AgentId == "" || req.PluginName == "" {
		return &proto.PluginResponse{
			Success: false,
			Message: "agent_id 和 plugin_name 不能为空",
		}, nil
	}

	// 检查插件是否存在和兼容性
	pluginInfo, err := s.registryService.GetPluginForAgent(req.PluginName, req.PluginVersion, req.DeviceType, req.Architecture, req.Os)
	if err != nil {
		s.logger.Error("获取插件信息失败",
			zap.Error(err),
			zap.String("plugin_name", req.PluginName))
		return &proto.PluginResponse{
			Success: false,
			Message: fmt.Sprintf("插件不可用: %v", err),
		}, nil
	}

	// 验证插件文件存在并计算校验和
	checksum, err := s.calculatePluginChecksum(pluginInfo.BinaryPath)
	if err != nil {
		s.logger.Error("计算插件校验和失败",
			zap.Error(err),
			zap.String("binary_path", pluginInfo.BinaryPath))
		return &proto.PluginResponse{
			Success: false,
			Message: fmt.Sprintf("插件文件验证失败: %v", err),
		}, nil
	}

	// 获取插件文件大小
	fileInfo, err := os.Stat(pluginInfo.BinaryPath)
	if err != nil {
		s.logger.Error("获取插件文件信息失败",
			zap.Error(err),
			zap.String("binary_path", pluginInfo.BinaryPath))
		return &proto.PluginResponse{
			Success: false,
			Message: fmt.Sprintf("获取插件文件信息失败: %v", err),
		}, nil
	}

	// 构建响应 - 只返回插件路径，不传输二进制数据
	response := &proto.PluginResponse{
		Success:      true,
		Message:      "插件路径分发成功",
		PluginPath:   pluginInfo.BinaryPath, // 插件在 plugins/build 目录下的相对路径
		Checksum:     checksum,
		AbsolutePath: s.getAbsolutePluginPath(pluginInfo.BinaryPath), // 插件的绝对路径
		Metadata: &proto.PluginMetadata{
			Name:                   pluginInfo.Name,
			Version:                pluginInfo.Version,
			Description:            pluginInfo.Description,
			SupportedDeviceTypes:   pluginInfo.SupportedDeviceTypes,
			SupportedArchitectures: pluginInfo.SupportedArchitectures,
			SupportedOs:            pluginInfo.SupportedOS,
			Configuration:          pluginInfo.Configuration,
			Dependencies:           pluginInfo.Dependencies,
			SizeBytes:              fileInfo.Size(),
			Author:                 pluginInfo.Author,
			CreatedAt:              pluginInfo.CreatedAt.Unix(),
			UpdatedAt:              pluginInfo.UpdatedAt.Unix(),
		},
	}

	// 记录分发事件
	err = s.registryService.RecordDistribution(req.AgentId, pluginInfo.Name, pluginInfo.Version, "success", "")
	if err != nil {
		s.logger.Warn("记录分发事件失败", zap.Error(err))
	}

	return response, nil
}

// StreamPluginUpdates 处理插件更新推送流
func (s *PluginDistributionService) StreamPluginUpdates(
	req *emptypb.Empty,
	stream grpc.ServerStreamingServer[proto.PluginUpdateNotification],
) error {
	s.logger.Info("建立插件更新推送流连接")

	// 这里可以实现推送逻辑
	// 例如：当有新的插件版本时，主动推送给相关的 Agent
	for {
		select {
		case <-stream.Context().Done():
			s.logger.Info("插件更新推送流连接断开")
			return nil
		case <-time.After(30 * time.Second):
			// 定期检查是否有需要推送的更新
			// TODO: 实现实际的更新检查和推送逻辑
		}
	}
}

// ReportPluginStatus 处理 Agent 的插件状态上报
func (s *PluginDistributionService) ReportPluginStatus(
	ctx context.Context,
	req *proto.PluginStatusReport,
) (*proto.PluginStatusResponse, error) {
	s.logger.Info("收到插件状态报告",
		zap.String("agent_id", req.AgentId),
		zap.Int("plugin_count", len(req.PluginStatuses)))

	if req.AgentId == "" {
		return &proto.PluginStatusResponse{
			Success: false,
			Message: "agent_id 不能为空",
		}, nil
	}

	var actions []string

	// 处理每个插件的状态
	for _, pluginStatus := range req.PluginStatuses {
		err := s.registryService.UpdateAgentPluginStatus(
			req.AgentId,
			pluginStatus.PluginName,
			pluginStatus.Version,
			pluginStatus.Status,
			pluginStatus.ErrorMessage,
		)
		if err != nil {
			s.logger.Error("更新插件状态失败",
				zap.Error(err),
				zap.String("agent_id", req.AgentId),
				zap.String("plugin_name", pluginStatus.PluginName))
			continue
		}

		// 根据插件状态决定建议的操作
		switch pluginStatus.Status {
		case "failed":
			actions = append(actions, fmt.Sprintf("reload:%s", pluginStatus.PluginName))
		case "outdated":
			actions = append(actions, fmt.Sprintf("update:%s", pluginStatus.PluginName))
		}
	}

	return &proto.PluginStatusResponse{
		Success: true,
		Message: "状态更新成功",
		Actions: actions,
	}, nil
}

// getAbsolutePluginPath 获取插件的绝对路径
func (s *PluginDistributionService) getAbsolutePluginPath(relativePath string) string {
	// 如果已经是绝对路径，直接返回
	if filepath.IsAbs(relativePath) {
		return relativePath
	}

	// 检查路径是否已经包含完整的 plugins/build 前缀
	if strings.HasPrefix(relativePath, "plugins/build/") {
		// 如果 pluginStorePath 是 "./plugins/build"，需要替换为实际路径
		if strings.HasSuffix(s.pluginStorePath, "plugins/build") {
			// 去掉 relativePath 中的 "plugins/build/" 前缀，只保留文件名
			fileName := strings.TrimPrefix(relativePath, "plugins/build/")
			return filepath.Join(s.pluginStorePath, fileName)
		}
		// 如果 pluginStorePath 是项目根目录，直接拼接
		return filepath.Join(s.pluginStorePath, relativePath)
	}

	// 如果路径包含 plugins/ 但不是完整前缀，可能是其他情况
	if strings.Contains(relativePath, "plugins/") {
		// 直接使用相对路径，不做额外处理
		return relativePath
	}

	// 否则拼接存储目录
	return filepath.Join(s.pluginStorePath, relativePath)
}

// calculatePluginChecksum 计算插件文件的校验和
func (s *PluginDistributionService) calculatePluginChecksum(binaryPath string) (string, error) {
	// 获取完整路径
	fullPath := s.getAbsolutePluginPath(binaryPath)

	// 检查文件是否存在
	if _, err := os.Stat(fullPath); os.IsNotExist(err) {
		return "", fmt.Errorf("插件文件不存在: %s", fullPath)
	}

	// 读取文件
	data, err := os.ReadFile(fullPath)
	if err != nil {
		return "", fmt.Errorf("读取插件文件失败: %v", err)
	}

	// 计算 MD5 校验和
	hash := md5.New()
	if _, err := hash.Write(data); err != nil {
		return "", fmt.Errorf("计算校验和失败: %v", err)
	}

	return fmt.Sprintf("%x", hash.Sum(nil)), nil
}
