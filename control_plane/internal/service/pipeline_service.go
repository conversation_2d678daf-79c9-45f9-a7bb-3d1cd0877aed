package service

import (
	"aiops/control_plane/internal/model"
	"aiops/control_plane/internal/repository"
	pb "aiops/pkg/proto"
	"encoding/json"
	"fmt"
	"time"

	"go.uber.org/zap"
)

// PipelineService 流水线服务
type PipelineService struct {
	pipelineRepo         repository.PipelineRepository
	pipelineStatusRepo   repository.PipelineStatusRepository
	pipelineTemplateRepo repository.PipelineTemplateRepository
	pipelineMetricsRepo  repository.PipelineMetricsRepository
	pipelineEventRepo    repository.PipelineEventRepository
	agentService         *AgentService
	logger               *zap.Logger
}

// NewPipelineService 创建流水线服务
func NewPipelineService(
	pipelineRepo repository.PipelineRepository,
	pipelineStatusRepo repository.PipelineStatusRepository,
	pipelineTemplateRepo repository.PipelineTemplateRepository,
	pipelineMetricsRepo repository.PipelineMetricsRepository,
	pipelineEventRepo repository.PipelineEventRepository,
	logger *zap.Logger,
) *PipelineService {
	return &PipelineService{
		pipelineRepo:         pipelineRepo,
		pipelineStatusRepo:   pipelineStatusRepo,
		pipelineTemplateRepo: pipelineTemplateRepo,
		pipelineMetricsRepo:  pipelineMetricsRepo,
		pipelineEventRepo:    pipelineEventRepo,
		logger:               logger,
	}
}

// SetAgentService 设置Agent服务依赖
func (ps *PipelineService) SetAgentService(agentService *AgentService) {
	ps.agentService = agentService
}

// CreatePipeline 创建流水线
func (ps *PipelineService) CreatePipeline(config *pb.PipelineConfig) (*model.PipelineConfig, error) {
	// 转换protobuf配置到数据库模型
	dbConfig, err := ps.convertPbConfigToModel(config)
	if err != nil {
		return nil, fmt.Errorf("failed to convert config: %w", err)
	}

	// 验证配置
	if err := ps.validatePipelineConfig(dbConfig); err != nil {
		return nil, fmt.Errorf("config validation failed: %w", err)
	}

	// 保存到数据库
	if err := ps.pipelineRepo.Create(dbConfig); err != nil {
		return nil, fmt.Errorf("failed to create pipeline: %w", err)
	}

	// 记录事件
	ps.recordEvent(dbConfig.PipelineID, dbConfig.AgentID, "created", "Pipeline created successfully", "info")

	ps.logger.Info("Pipeline created",
		zap.String("pipeline_id", dbConfig.PipelineID),
		zap.String("name", dbConfig.Name),
		zap.String("agent_id", dbConfig.AgentID))

	return dbConfig, nil
}

// GetPipeline 获取流水线
func (ps *PipelineService) GetPipeline(pipelineID string) (*model.PipelineConfig, error) {
	return ps.pipelineRepo.GetByID(pipelineID)
}

// UpdatePipeline 更新流水线
func (ps *PipelineService) UpdatePipeline(pipelineID string, config *pb.PipelineConfig) (*model.PipelineConfig, error) {
	// 获取现有配置
	existing, err := ps.pipelineRepo.GetByID(pipelineID)
	if err != nil {
		return nil, fmt.Errorf("pipeline not found: %w", err)
	}

	// 转换新配置
	updated, err := ps.convertPbConfigToModel(config)
	if err != nil {
		return nil, fmt.Errorf("failed to convert config: %w", err)
	}

	// 保持ID和创建时间
	updated.ID = existing.ID
	updated.CreatedAt = existing.CreatedAt
	updated.PipelineID = existing.PipelineID

	// 验证配置
	if err := ps.validatePipelineConfig(updated); err != nil {
		return nil, fmt.Errorf("config validation failed: %w", err)
	}

	// 更新数据库
	if err := ps.pipelineRepo.Update(updated); err != nil {
		return nil, fmt.Errorf("failed to update pipeline: %w", err)
	}

	// 记录事件
	ps.recordEvent(updated.PipelineID, updated.AgentID, "config_updated", "Pipeline configuration updated", "info")

	ps.logger.Info("Pipeline updated",
		zap.String("pipeline_id", updated.PipelineID),
		zap.String("name", updated.Name))

	return updated, nil
}

// DeletePipeline 删除流水线
func (ps *PipelineService) DeletePipeline(pipelineID string) error {
	// 获取流水线信息
	config, err := ps.pipelineRepo.GetByID(pipelineID)
	if err != nil {
		return fmt.Errorf("pipeline not found: %w", err)
	}

	// 如果流水线正在运行，先停止它
	if config.Status == "running" {
		if err := ps.StopPipeline(pipelineID); err != nil {
			ps.logger.Warn("Failed to stop pipeline before deletion",
				zap.String("pipeline_id", pipelineID),
				zap.Error(err))
		}
	}

	// 删除相关数据
	ps.pipelineStatusRepo.Delete(pipelineID)

	// 删除流水线配置
	if err := ps.pipelineRepo.Delete(pipelineID); err != nil {
		return fmt.Errorf("failed to delete pipeline: %w", err)
	}

	// 记录事件
	ps.recordEvent(pipelineID, config.AgentID, "deleted", "Pipeline deleted", "info")

	ps.logger.Info("Pipeline deleted",
		zap.String("pipeline_id", pipelineID),
		zap.String("name", config.Name))

	return nil
}

// ListPipelines 列出流水线
func (ps *PipelineService) ListPipelines(offset, limit int) ([]*model.PipelineConfig, error) {
	return ps.pipelineRepo.List(offset, limit)
}

// GetPipelinesByAgent 获取Agent的流水线
func (ps *PipelineService) GetPipelinesByAgent(agentID string) ([]*model.PipelineConfig, error) {
	return ps.pipelineRepo.GetByAgentID(agentID)
}

// StartPipeline 启动流水线
func (ps *PipelineService) StartPipeline(pipelineID string) error {
	config, err := ps.pipelineRepo.GetByID(pipelineID)
	if err != nil {
		return fmt.Errorf("pipeline not found: %w", err)
	}

	if config.Status == "running" {
		return fmt.Errorf("pipeline already running")
	}

	// 更新状态
	if err := ps.pipelineRepo.UpdateStatus(pipelineID, "running"); err != nil {
		return fmt.Errorf("failed to update status: %w", err)
	}

	// 记录事件
	ps.recordEvent(pipelineID, config.AgentID, "started", "Pipeline started", "info")

	ps.logger.Info("Pipeline started",
		zap.String("pipeline_id", pipelineID),
		zap.String("name", config.Name))

	return nil
}

// StopPipeline 停止流水线
func (ps *PipelineService) StopPipeline(pipelineID string) error {
	config, err := ps.pipelineRepo.GetByID(pipelineID)
	if err != nil {
		return fmt.Errorf("pipeline not found: %w", err)
	}

	if config.Status != "running" {
		return fmt.Errorf("pipeline not running")
	}

	// 更新状态
	if err := ps.pipelineRepo.UpdateStatus(pipelineID, "stopped"); err != nil {
		return fmt.Errorf("failed to update status: %w", err)
	}

	// 记录事件
	ps.recordEvent(pipelineID, config.AgentID, "stopped", "Pipeline stopped", "info")

	ps.logger.Info("Pipeline stopped",
		zap.String("pipeline_id", pipelineID),
		zap.String("name", config.Name))

	return nil
}

// UpdatePipelineStatus 更新流水线状态
func (ps *PipelineService) UpdatePipelineStatus(status *pb.PipelineStatus) error {
	// 转换protobuf状态到数据库模型
	dbStatus := &model.PipelineStatus{
		PipelineID:          status.PipelineId,
		AgentID:             status.AgentId,
		Status:              status.Status,
		ErrorMessage:        status.ErrorMessage,
		StartTimestamp:      status.StartTimestamp,
		LastUpdateTimestamp: status.LastUpdateTimestamp,
	}

	// 序列化指标数据
	if status.Metrics != nil {
		metricsData, err := json.Marshal(status.Metrics)
		if err != nil {
			ps.logger.Error("Failed to marshal metrics data", zap.Error(err))
		} else {
			dbStatus.MetricsData = string(metricsData)
		}
	}

	// 检查是否已存在
	existing, err := ps.pipelineStatusRepo.GetByPipelineID(status.PipelineId)
	if err != nil {
		// 不存在，创建新的
		return ps.pipelineStatusRepo.Create(dbStatus)
	} else {
		// 存在，更新
		dbStatus.ID = existing.ID
		dbStatus.CreatedAt = existing.CreatedAt
		return ps.pipelineStatusRepo.Update(dbStatus)
	}
}

// GetPipelineStatus 获取流水线状态
func (ps *PipelineService) GetPipelineStatus(pipelineID string) (*model.PipelineStatus, error) {
	return ps.pipelineStatusRepo.GetByPipelineID(pipelineID)
}

// GetPipelineTemplates 获取流水线模板
func (ps *PipelineService) GetPipelineTemplates(category string) ([]*model.PipelineTemplate, error) {
	if category != "" {
		return ps.pipelineTemplateRepo.FindByCategory(category)
	}
	return ps.pipelineTemplateRepo.GetActive()
}

// ValidatePipelineConfig 验证流水线配置
func (ps *PipelineService) ValidatePipelineConfig(config *pb.PipelineConfig) (*pb.PipelineConfigValidationResponse, error) {
	response := &pb.PipelineConfigValidationResponse{
		Valid:    true,
		Errors:   make([]*pb.ValidationError, 0),
		Warnings: make([]*pb.ValidationWarning, 0),
		Message:  "Configuration is valid",
	}

	// 基础验证
	if config.Collector == nil {
		response.Valid = false
		response.Errors = append(response.Errors, &pb.ValidationError{
			Field:   "collector",
			Message: "Collector configuration is required",
			Code:    "REQUIRED_FIELD",
		})
	} else {
		if config.Collector.Name == "" {
			response.Valid = false
			response.Errors = append(response.Errors, &pb.ValidationError{
				Field:   "collector.name",
				Message: "Collector name is required",
				Code:    "REQUIRED_FIELD",
			})
		}
		if config.Collector.Type == "" {
			response.Valid = false
			response.Errors = append(response.Errors, &pb.ValidationError{
				Field:   "collector.type",
				Message: "Collector type is required",
				Code:    "REQUIRED_FIELD",
			})
		}
	}

	// 数值验证
	if config.BufferSize <= 0 {
		response.Warnings = append(response.Warnings, &pb.ValidationWarning{
			Field:   "buffer_size",
			Message: "Buffer size should be positive, will use default value",
			Code:    "DEFAULT_VALUE",
		})
	}

	if config.WorkerCount <= 0 {
		response.Warnings = append(response.Warnings, &pb.ValidationWarning{
			Field:   "worker_count",
			Message: "Worker count should be positive, will use default value",
			Code:    "DEFAULT_VALUE",
		})
	}

	if !response.Valid {
		response.Message = "Configuration validation failed"
	}

	return response, nil
}

// convertPbConfigToModel 转换protobuf配置到数据库模型
func (ps *PipelineService) convertPbConfigToModel(pbConfig *pb.PipelineConfig) (*model.PipelineConfig, error) {
	// 序列化各个配置部分
	collectorConfig, err := json.Marshal(pbConfig.Collector)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal collector config: %w", err)
	}

	processorsConfig, err := json.Marshal(pbConfig.Processors)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal processors config: %w", err)
	}

	errorHandlingConfig, err := json.Marshal(pbConfig.ErrorHandling)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal error handling config: %w", err)
	}

	resourceLimitsConfig, err := json.Marshal(pbConfig.ResourceLimits)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal resource limits config: %w", err)
	}

	return &model.PipelineConfig{
		PipelineID:             pbConfig.PipelineId,
		Name:                   pbConfig.Name,
		Description:            pbConfig.Description,
		Version:                pbConfig.Version,
		Enabled:                pbConfig.Enabled,
		BufferSize:             int(pbConfig.BufferSize),
		WorkerCount:            int(pbConfig.WorkerCount),
		TimeoutSeconds:         pbConfig.TimeoutSeconds,
		RetryAttempts:          int(pbConfig.RetryAttempts),
		RetryDelaySeconds:      pbConfig.RetryDelaySeconds,
		EnableMetrics:          pbConfig.EnableMetrics,
		EnableTracing:          pbConfig.EnableTracing,
		MetricsIntervalSeconds: pbConfig.MetricsIntervalSeconds,
		CollectorConfig:        string(collectorConfig),
		ProcessorsConfig:       string(processorsConfig),
		ErrorHandlingConfig:    string(errorHandlingConfig),
		ResourceLimitsConfig:   string(resourceLimitsConfig),
		Status:                 "idle",
	}, nil
}

// validatePipelineConfig 验证流水线配置
func (ps *PipelineService) validatePipelineConfig(config *model.PipelineConfig) error {
	if config.PipelineID == "" {
		return fmt.Errorf("pipeline ID is required")
	}
	if config.Name == "" {
		return fmt.Errorf("pipeline name is required")
	}
	if config.CollectorConfig == "" {
		return fmt.Errorf("collector configuration is required")
	}
	return nil
}

// recordEvent 记录事件
func (ps *PipelineService) recordEvent(pipelineID, agentID, eventType, message, severity string) {
	event := &model.PipelineEvent{
		PipelineID: pipelineID,
		AgentID:    agentID,
		EventType:  eventType,
		Message:    message,
		Severity:   severity,
		Timestamp:  time.Now().Unix(),
	}

	if err := ps.pipelineEventRepo.Create(event); err != nil {
		ps.logger.Error("Failed to record pipeline event",
			zap.String("pipeline_id", pipelineID),
			zap.String("event_type", eventType),
			zap.Error(err))
	}
}
