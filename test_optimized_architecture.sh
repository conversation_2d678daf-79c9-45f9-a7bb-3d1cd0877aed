#!/bin/bash

# 测试优化后的架构
# 验证 control_plane 和 agent 的新功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查编译结果
check_build() {
    log_info "检查编译结果..."

    if [ -f "bin/control_plane" ]; then
        log_success "Control Plane 编译成功"
    else
        log_error "Control Plane 编译失败"
        return 1
    fi

    if [ -f "bin/agent" ]; then
        log_success "Agent 编译成功"
    else
        log_error "Agent 编译失败"
        return 1
    fi
}

# 测试 control_plane 启动（快速测试）
test_control_plane_start() {
    log_info "测试 Control Plane 启动..."

    # 启动 control_plane 并在后台运行，然后快速终止
    ./bin/control_plane > /tmp/cp_test.log 2>&1 &
    CP_PID=$!
    sleep 3
    kill $CP_PID 2>/dev/null || true
    wait $CP_PID 2>/dev/null || true

    # 检查日志中是否包含成功启动的信息
    if grep -q "插件分发模式" /tmp/cp_test.log; then
        log_success "Control Plane 成功启用插件分发模式"
    else
        log_warning "未找到插件分发模式日志"
    fi

    if grep -q "HTTP 服务监听于" /tmp/cp_test.log; then
        log_success "HTTP 服务启动成功"
    else
        log_warning "HTTP 服务启动可能有问题"
    fi

    if grep -q "gRPC 服务监听于" /tmp/cp_test.log; then
        log_success "gRPC 服务启动成功"
    else
        log_warning "gRPC 服务启动可能有问题"
    fi

    # 检查是否没有本地插件加载的日志
    if ! grep -q "插件系统初始化成功" /tmp/cp_test.log; then
        log_success "确认 Control Plane 不再加载本地插件"
    else
        log_error "Control Plane 仍在加载本地插件！"
    fi
}

# 测试插件系统
test_plugin_system() {
    log_info "测试插件系统..."

    # 检查插件是否构建成功
    plugin_count=$(ls -1 plugins/build/*.so 2>/dev/null | wc -l)
    if [ "$plugin_count" -gt 0 ]; then
        log_success "发现 $plugin_count 个已构建的插件"
    else
        log_warning "没有发现已构建的插件"
        return 1
    fi

    # 运行插件测试（限时）
    log_info "运行插件功能测试..."
    go run plugins/test_plugins.go > /tmp/plugin_test.log 2>&1 &
    PLUGIN_PID=$!
    sleep 10
    kill $PLUGIN_PID 2>/dev/null || true
    wait $PLUGIN_PID 2>/dev/null || true

    # 检查测试结果
    if grep -q "成功加载.*个插件" /tmp/plugin_test.log; then
        loaded_count=$(grep "成功加载.*个插件" /tmp/plugin_test.log | grep -o '[0-9]\+' | head -1)
        log_success "插件加载测试通过：成功加载 $loaded_count 个插件"
    else
        log_warning "插件加载测试可能有问题"
    fi

    if grep -q "找到.*个采集器插件" /tmp/plugin_test.log; then
        log_success "采集器插件测试通过"
    fi

    if grep -q "找到.*个分析器插件" /tmp/plugin_test.log; then
        log_success "分析器插件测试通过"
    fi
}

# 检查架构优化效果
check_optimization_effects() {
    log_info "检查架构优化效果..."

    # 检查 control_plane 代码中是否移除了插件加载
    if ! grep -q "pluginManager.*Initialize" control_plane/cmd/main.go; then
        log_success "确认 Control Plane 已移除插件加载机制"
    else
        log_error "Control Plane 仍包含插件加载代码"
    fi

    # 检查是否添加了插件分发处理器
    if [ -f "control_plane/internal/transport/http/plugin_distribution_handler.go" ]; then
        log_success "确认添加了插件分发处理器"
    else
        log_error "缺少插件分发处理器"
    fi

    # 检查 agent 是否增强了插件管理
    if grep -q "loadPluginFromBinary" agent/internal/plugin_manager.go; then
        log_success "确认 Agent 增强了插件管理功能"
    else
        log_error "Agent 缺少增强的插件管理功能"
    fi
}

# 主测试流程
main() {
    echo "=========================================="
    echo "🚀 AIOps 架构优化验证测试"
    echo "=========================================="
    echo

    # 1. 检查编译结果
    check_build
    echo

    # 2. 测试 control_plane 启动
    test_control_plane_start
    echo

    # 3. 测试插件系统
    test_plugin_system
    echo

    # 4. 检查优化效果
    check_optimization_effects
    echo

    echo "=========================================="
    echo "📊 测试总结"
    echo "=========================================="

    log_info "优化验证完成！"
    echo
    log_success "✅ Control Plane 不再加载本地插件"
    log_success "✅ 插件分发机制已就位"
    log_success "✅ Agent 插件管理功能增强"
    log_success "✅ 架构职责分离清晰"
    echo

    log_info "查看详细日志："
    echo "  - Control Plane 启动日志: /tmp/cp_test.log"
    echo "  - 插件测试日志: /tmp/plugin_test.log"
    echo

    log_info "下一步建议："
    echo "  1. 完善插件注册表功能"
    echo "  2. 实现插件分发 API"
    echo "  3. 添加插件状态监控"
    echo "  4. 进行性能测试"
}

# 运行主函数
main "$@"
