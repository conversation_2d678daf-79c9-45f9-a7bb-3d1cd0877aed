package main

import (
	"flag"
	"fmt"
	"os"
	"path/filepath"
	"plugin"
	"strings"
	"text/tabwriter"
	"time"

	"aiops/pkg/pipeline"
)

var (
	pluginsDir = flag.String("plugins-dir", "plugins/build", "插件目录路径")
	action     = flag.String("action", "list", "操作类型: list, info, validate")
	pluginName = flag.String("plugin", "", "插件名称")
	verbose    = flag.Bool("verbose", false, "详细输出")
)

func main() {
	flag.Parse()

	switch *action {
	case "list":
		listPlugins()
	case "info":
		if *pluginName == "" {
			fmt.Println("错误: 请指定插件名称")
			os.Exit(1)
		}
		showPluginInfo(*pluginName)
	case "validate":
		if *pluginName == "" {
			validateAllPlugins()
		} else {
			validatePlugin(*pluginName)
		}
	default:
		fmt.Printf("未知操作: %s\n", *action)
		fmt.Println("支持的操作: list, info, validate")
		os.Exit(1)
	}
}

// listPlugins 列出所有插件
func listPlugins() {
	fmt.Printf("扫描插件目录: %s\n", *pluginsDir)
	fmt.Println()

	plugins, err := scanPlugins(*pluginsDir)
	if err != nil {
		fmt.Printf("扫描插件失败: %v\n", err)
		os.Exit(1)
	}

	if len(plugins) == 0 {
		fmt.Println("未找到任何插件")
		return
	}

	// 创建表格输出
	w := tabwriter.NewWriter(os.Stdout, 0, 0, 2, ' ', 0)
	fmt.Fprintln(w, "插件名称\t版本\t类型\t文件大小\t修改时间")
	fmt.Fprintln(w, "--------\t----\t----\t--------\t--------")

	for _, p := range plugins {
		fmt.Fprintf(w, "%s\t%s\t%s\t%s\t%s\n",
			p.Name, p.Version, p.Type, p.FileSize, p.ModTime.Format("2006-01-02 15:04"))
	}

	w.Flush()
	fmt.Printf("\n总计: %d 个插件\n", len(plugins))
}

// showPluginInfo 显示插件详细信息
func showPluginInfo(name string) {
	pluginPath := filepath.Join(*pluginsDir, name+".so")

	info, err := getPluginInfo(pluginPath)
	if err != nil {
		fmt.Printf("获取插件信息失败: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("插件详细信息:\n")
	fmt.Printf("  名称: %s\n", info.Name)
	fmt.Printf("  版本: %s\n", info.Version)
	fmt.Printf("  类型: %s\n", info.Type)
	fmt.Printf("  文件: %s\n", pluginPath)
	fmt.Printf("  大小: %s\n", info.FileSize)
	fmt.Printf("  修改时间: %s\n", info.ModTime.Format("2006-01-02 15:04:05"))

	if *verbose && info.Metadata != nil {
		fmt.Printf("  元数据:\n")
		for key, value := range info.Metadata {
			fmt.Printf("    %s: %v\n", key, value)
		}
	}

	// 尝试加载插件获取更多信息
	if *verbose {
		fmt.Printf("\n插件接口信息:\n")
		if err := showPluginInterfaces(pluginPath); err != nil {
			fmt.Printf("  获取接口信息失败: %v\n", err)
		}
	}
}

// validatePlugin 验证单个插件
func validatePlugin(name string) {
	pluginPath := filepath.Join(*pluginsDir, name+".so")

	fmt.Printf("验证插件: %s\n", pluginPath)

	if err := validatePluginFile(pluginPath); err != nil {
		fmt.Printf("❌ 验证失败: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("✅ 插件验证通过\n")
}

// validateAllPlugins 验证所有插件
func validateAllPlugins() {
	fmt.Printf("验证所有插件...\n\n")

	plugins, err := scanPlugins(*pluginsDir)
	if err != nil {
		fmt.Printf("扫描插件失败: %v\n", err)
		os.Exit(1)
	}

	successCount := 0
	for _, p := range plugins {
		pluginPath := filepath.Join(*pluginsDir, p.Name+".so")
		fmt.Printf("验证 %s... ", p.Name)

		if err := validatePluginFile(pluginPath); err != nil {
			fmt.Printf("❌ 失败: %v\n", err)
		} else {
			fmt.Printf("✅ 通过\n")
			successCount++
		}
	}

	fmt.Printf("\n验证完成: %d/%d 个插件通过验证\n", successCount, len(plugins))
}

// PluginInfo 插件信息
type PluginInfo struct {
	Name     string
	Version  string
	Type     string
	FilePath string
	FileSize string
	ModTime  time.Time
	Metadata map[string]interface{}
}

// scanPlugins 扫描插件目录
func scanPlugins(dir string) ([]*PluginInfo, error) {
	var plugins []*PluginInfo

	err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 只处理.so文件，跳过符号链接
		if !info.IsDir() && strings.HasSuffix(path, ".so") && !isSymlink(info) {
			pluginInfo, err := getPluginInfo(path)
			if err != nil {
				// 跳过无法解析的文件
				return nil
			}
			plugins = append(plugins, pluginInfo)
		}

		return nil
	})

	return plugins, err
}

// getPluginInfo 获取插件信息
func getPluginInfo(pluginPath string) (*PluginInfo, error) {
	// 获取文件信息
	fileInfo, err := os.Stat(pluginPath)
	if err != nil {
		return nil, err
	}

	info := &PluginInfo{
		FilePath: pluginPath,
		FileSize: formatFileSize(fileInfo.Size()),
		ModTime:  fileInfo.ModTime(),
	}

	// 尝试加载插件获取详细信息
	p, err := plugin.Open(pluginPath)
	if err != nil {
		// 如果无法加载，使用文件名作为基础信息
		baseName := strings.TrimSuffix(filepath.Base(pluginPath), ".so")
		info.Name = baseName
		info.Version = "unknown"
		info.Type = "unknown"
		return info, nil
	}

	// 查找插件工厂函数
	factorySymbol, err := p.Lookup("NewPluginFactory")
	if err != nil {
		return nil, fmt.Errorf("plugin factory function not found")
	}

	factoryFunc, ok := factorySymbol.(func() pipeline.PluginFactory)
	if !ok {
		return nil, fmt.Errorf("invalid plugin factory function signature")
	}

	factory := factoryFunc()
	if factory == nil {
		return nil, fmt.Errorf("plugin factory returned nil")
	}

	pluginInfo := factory.GetPluginInfo()
	if pluginInfo != nil {
		info.Name = pluginInfo.Name
		info.Version = pluginInfo.Version
		info.Type = string(pluginInfo.Type)
		info.Metadata = pluginInfo.Metadata
	}

	return info, nil
}

// validatePluginFile 验证插件文件
func validatePluginFile(pluginPath string) error {
	// 检查文件是否存在
	if _, err := os.Stat(pluginPath); err != nil {
		return fmt.Errorf("文件不存在: %w", err)
	}

	// 尝试加载插件
	p, err := plugin.Open(pluginPath)
	if err != nil {
		return fmt.Errorf("无法加载插件: %w", err)
	}

	// 检查必需的符号
	_, err = p.Lookup("NewPluginFactory")
	if err != nil {
		return fmt.Errorf("缺少插件工厂函数: %w", err)
	}

	return nil
}

// showPluginInterfaces 显示插件接口信息
func showPluginInterfaces(pluginPath string) error {
	p, err := plugin.Open(pluginPath)
	if err != nil {
		return err
	}

	factorySymbol, err := p.Lookup("NewPluginFactory")
	if err != nil {
		return err
	}

	factoryFunc, ok := factorySymbol.(func() pipeline.PluginFactory)
	if !ok {
		return fmt.Errorf("invalid factory function")
	}

	factory := factoryFunc()
	supportedTypes := factory.GetSupportedTypes()

	fmt.Printf("  支持的插件类型: %v\n", supportedTypes)

	// 尝试创建插件实例获取更多信息
	for _, pluginType := range supportedTypes {
		instance, err := factory.CreatePlugin(pluginType, make(map[string]interface{}))
		if err != nil {
			continue
		}

		fmt.Printf("  %s 接口:\n", pluginType)

		if inputSchema := instance.GetInputSchema(); inputSchema != nil {
			fmt.Printf("    输入模式: %s\n", inputSchema.Description)
		}

		if outputSchema := instance.GetOutputSchema(); outputSchema != nil {
			fmt.Printf("    输出模式: %s\n", outputSchema.Description)
		}
	}

	return nil
}

// formatFileSize 格式化文件大小
func formatFileSize(size int64) string {
	const unit = 1024
	if size < unit {
		return fmt.Sprintf("%d B", size)
	}
	div, exp := int64(unit), 0
	for n := size / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(size)/float64(div), "KMGTPE"[exp])
}

// isSymlink 检查是否为符号链接
func isSymlink(info os.FileInfo) bool {
	return info.Mode()&os.ModeSymlink != 0
}
